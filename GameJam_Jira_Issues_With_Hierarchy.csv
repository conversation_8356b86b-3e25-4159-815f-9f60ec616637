Issue ID,Issue Type,Summary,Description,Priority,Labels,Epic Name,Parent ID,Hierarchy Level
100,Epic,Game Jam Prep & Setup,Setup and configuration tasks before the jam begins,High,prep,GameJamPrepAndSetup,,1
101,Story,Setup Development Environment,"Prepare version control, file structure, and dev tools",High,prep,GameJamPrepAndSetup,100,0
102,Task,Create a GitHub/GitLab repo,Initialize GitHub or GitLab repo for the project,Medium,git,GameJamPrepAndSetup,101,0
103,Sub-task,Create repo and invite team,Set up repository and add team members,Low,git,GameJamPrepAndSetup,102,-1
104,Task,Set up project structure,"Add folders for src, assets, scenes, UI, etc.",Medium,structure,GameJamPrepAndSetup,101,0
105,Sub-task,Define folder structure for assets,"Set up directories for code, art, and tests",Low,structure,GameJamPrepAndSetup,104,-1
106,Task,Setup CI/CD (if desired),Implement basic CI/CD pipeline for builds and tests,Medium,devops,GameJamPrepAndSetup,101,0
107,Task,"Define folder structure for assets,code, and tests",Organize the file structure for ease of access,Low,structure,GameJamPrepAndSetup,101,0
108,Story,Tooling and Dependencies,"Choose engine, setup build process, and install dependencies",High,prep,GameJamPrepAndSetup,100,0
109,Task,Choose engine/framework,"Decide between Phaser, Three.js, Unity, etc.",High,decision,GameJamPrepAndSetup,108,0
110,Task,Add essential libraries,"Install audio, input handling, physics libraries",Medium,tooling,GameJamPrepAndSetup,108,0
111,Task,Setup ESLint/Prettier or formatting/linting rules,Implement ESLint/Prettier for consistent code,Low,devops,GameJamPrepAndSetup,108,0
112,Task,Setup basic build script and hot reload,Configure build tools and set up hot reload for development,Medium,build,GameJamPrepAndSetup,108,0
200,Epic,"Art, Audio, and Asset Pipeline",All tasks related to the creative asset workflow,Medium,assets,ArtAudioAndAssetPipeline,,1
201,Story,Asset Workflow Preparation,Set up tools and pipelines for art assets,Medium,assets,ArtAudioAndAssetPipeline,200,0
202,Task,Decide on art style,"Choose pixel art, vector art, or another style",High,art,ArtAudioAndAssetPipeline,201,0
203,Task,Choose tools for creating art,"Decide between tools like Aseprite, Piskel, etc.",Medium,art,ArtAudioAndAssetPipeline,201,0
204,Task,Create import pipeline for assets,Setup system for importing art assets into the project,Medium,art,ArtAudioAndAssetPipeline,201,0
205,Sub-task,Create import script,Write a script to streamline asset imports,Low,art,ArtAudioAndAssetPipeline,204,-1
206,Task,Create placeholder character and environment art,Develop basic placeholder art for dev,Medium,art,ArtAudioAndAssetPipeline,201,0
207,Story,Audio Workflow Setup,Setup tools and pipeline for sound effects and music,Low,audio,ArtAudioAndAssetPipeline,200,0
208,Task,Choose SFX/music tools,Select tools like BFXR for SFX or Bosca Ceoil for music,Medium,audio,ArtAudioAndAssetPipeline,207,0
209,Task,Create placeholder sound effects and music test file,Create basic sound effects and music for testing,Medium,audio,ArtAudioAndAssetPipeline,207,0
210,Task,Implement basic audio manager,Write code to manage audio playback,Medium,audio,ArtAudioAndAssetPipeline,207,0
300,Epic,Team Communication & Workflow,Streamlining collab and planning,High,team,TeamCommunicationAndWorkflow,,1
301,Story,Communication & Planning,Define team workflow and daily check-ins,High,workflow,TeamCommunicationAndWorkflow,300,0
302,Task,Define daily SCRUM cadence,Set up Discord call schedule and Jira standup notes,Medium,team,TeamCommunicationAndWorkflow,301,0
303,Task,Set up shared doc for brainstorming,Create a shared Google Doc or Notion for ideas,Medium,collaboration,TeamCommunicationAndWorkflow,301,0
304,Task,Create Jira workflows for Issues / In Progress / Done,Configure Jira board for SCRUM workflow,High,jira,TeamCommunicationAndWorkflow,301,0
305,Task,Assign roles and responsibilities,"Define roles for art, dev, sound, and other tasks",Medium,team,TeamCommunicationAndWorkflow,301,0
400,Epic,Pre-Jam Game Design,Design prep not dependent on theme,Medium,gamedesign,PreJamGameDesign,,1
401,Story,Game Design Research,Research past GameDev.js themes and gather inspiration,Low,research,PreJamGameDesign,400,0
402,Task,Research past GameDev.js themes,Analyze previous game jams for patterns and ideas,Low,research,PreJamGameDesign,401,0
403,Task,Collect inspiration references,"Look for inspiration in mechanics, genres, and visuals",Medium,inspiration,PreJamGameDesign,401,0
404,Task,Decide on preferred genres,Choose the genres both teammates enjoy,Medium,genre,PreJamGameDesign,401,0
405,Task,Brainstorm reusable systems,Think of reusable systems like tilemaps or platforming,High,mechanics,PreJamGameDesign,401,0
406,Story,Prototype Starter Systems,Start building the basic game systems for quick prototyping,High,prototype,PreJamGameDesign,400,0
407,Task,Create simple input system,"Setup system to handle keyboard, mouse, and touch input",High,controls,PreJamGameDesign,406,0
408,Task,Build test scenes,"Create basic scenes for testing player movement, menus, etc.",High,prototype,PreJamGameDesign,406,0
409,Task,Create template for levels/scenes,Create a level template for rapid development,Medium,prototype,PreJamGameDesign,406,0
410,Task,Implement basic UI system,"Setup basic UI elements like buttons, text, and menus",Medium,ui,PreJamGameDesign,406,0
500,Epic,Marketing & Submission Prep,Everything you’ll need for the final polish and upload,Low,marketing,MarketingAndSubmissionPrep,,1
501,Story,Prepare for Distribution,Prepare all materials for submission to itch.io,Low,distribution,MarketingAndSubmissionPrep,500,0
502,Task,Setup itch.io project page,Create a private itch.io page for the game,Medium,itch,MarketingAndSubmissionPrep,501,0
503,Task,Create a game logo or placeholder splash screen,Design a temporary logo or splash for the game,Low,branding,MarketingAndSubmissionPrep,501,0
504,Task,Define naming conventions and meta,"Decide on the game's title, description, and tags",Medium,branding,MarketingAndSubmissionPrep,501,0
505,Task,Make a list of things needed for final submission,Prepare a checklist for the final submission,Medium,release,MarketingAndSubmissionPrep,501,0
