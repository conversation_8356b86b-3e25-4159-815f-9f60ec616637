# 🛠️ Equilibrium Protocol: Player Feedback Report

_Compiled from Itch.io comments (excluding developer responses)_

---

## 🐞 Reported Bugs

### 1. Shop Button Bug
> "After wave 4 the shop button just didn’t appear, so I couldn’t proceed."

### 2. Wave Lock Bug
> "Got to wave 4, but then it wouldn't let me advance. Seems like a blocker."

### 3. Startup Crash / Progression Blocker
> "The game got stuck after wave 1, so I couldn’t experience much of it."

### 4. Wave Stalling Issue
> "I got stuck on one wave and the next one wouldn’t load."

### 5. Wave Completion Bug / Audio Sync Issue
- _Bug_: "I got stuck on a wave even though I defeated all enemies."
- _Audio_: "Music gets really loud at the start of a wave, then slows—might be a bug or design?"

---

## 💡 Gameplay Suggestions & Balance Feedback

### 1. Theme Mechanic Clarity
> "Didn’t understand what happens when you kill more blue than red enemies. Tried to only kill one color but couldn't survive without killing both."

### 2. Open Source + Design Praise
- README and License file appreciated.
- <PERSON>o praised as well-structured.
- Music and SFX received praise.
- Death animation (fireworks) was a hit.
- Balance bars and shop system were well-received.

### 3. Replayability & Game Flow
> "Flying, upgrades, and wave-based structure are great. Impressive scope for a jam."

### 4. UI and Variety Suggestions
> "Could be a really sweet game with more enemy variety and cleaned-up UI."

### 5. Replay Value
> "Quite fun and replayable."

### 6. Addictiveness
> "Nice vampire-survivor-like. Quite addictive."

### 7. Thematic Cohesion
> "Even the game name is about balance. Music fits the vibe nicely."

### 8. Core Loop Praise
> "Great mechanics loop. Found myself wanting to optimize and jump back in."

### 9. Upgrade Store Praise
> "Upgrade store felt good. Gave bullet hell vibes."

### 10. Kill Choice Mechanic
> "New twist: balancing which enemies to kill—very cool."

### 11. Boss Fight Balance Suggestion
> "Boss fight was easy but too long due to high HP. Consider shortening the fight while increasing intensity."

---

_Use this document as a reference for prioritizing bug fixes and new feature development in upcoming patches._
