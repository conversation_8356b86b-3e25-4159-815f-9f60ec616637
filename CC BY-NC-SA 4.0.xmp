<?xpacket begin='' id='W5M0MpCehiHzreSzNTczkc9d'?>
<x:xmpmeta xmlns:x='adobe:ns:meta/'>
    <rdf:RDF xmlns:rdf='http://www.w3.org/1999/02/22-rdf-syntax-ns#'
             xmlns:xapRights='http://ns.adobe.com/xap/1.0/rights/'
             xmlns:cc='http://creativecommons.org/ns#'xmlns:dc='http://purl.org/dc/elements/1.1/'>
        <rdf:Description rdf:about=''>
            <xapRights:Marked>True</xapRights:Marked>
            <xapRights:Owner>
                <rdf:Bag>
                    <rdf:li>Fluffy-Swizzle Interactive</rdf:li>
                </rdf:Bag>
            </xapRights:Owner>
            <xapRights:WebStatement rdf:resource='https://github.com/Fluffy-Swizzle-Interactive/Equilibrium-Protocol-GameDevjs-2025-Entry'/>
            <xapRights:UsageTerms>
                <rdf:Alt>
                  <rdf:li xml:lang='x-default'>This work is licensed under &lt;a href=&quot;https://creativecommons.org/licenses/by-nc-sa/4.0/&quot;&gt;Creative Commons Attribution-NonCommercial-ShareAlike 4.0 International&lt;/a&gt;</rdf:li>
                  <rdf:li xml:lang='en-US' >This work is licensed under &lt;a href=&quot;https://creativecommons.org/licenses/by-nc-sa/4.0/&quot;&gt;Creative Commons Attribution-NonCommercial-ShareAlike 4.0 International&lt;/a&gt;</rdf:li>
                </rdf:Alt>
            </xapRights:UsageTerms>
            <cc:license rdf:resource='https://creativecommons.org/licenses/by-nc-sa/4.0/'/>
            <cc:attributionName>Fluffy-Swizzle Interactive</cc:attributionName>
            <dc:title>
                <rdf:Alt>
                  <rdf:li xml:lang='x-default'>Equilibrium Protocol</rdf:li>
                  <rdf:li xml:lang='en-US'>Equilibrium Protocol</rdf:li>
                </rdf:Alt>
            </dc:title>
        </rdf:Description>
    </rdf:RDF>
</x:xmpmeta>
<?xpacket end='r'?>