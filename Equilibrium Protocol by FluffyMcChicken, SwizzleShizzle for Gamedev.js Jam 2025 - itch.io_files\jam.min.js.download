(()=>{var I2=window.I,React2=window.React,ReactDOM2=window.ReactDOM,ReactDOMFactories=window.ReactDOMFactories,ReactTransitionGroup=window.ReactTransitionGroup,PropTypes=window.PropTypes,classNames2=window.classNames,R2=window.R;React2||I2.libs.react.done(function(){return React2=window.React,ReactDOM2=window.ReactDOM,ReactDOMFactories=window.ReactDOMFactories,ReactTransitionGroup=window.ReactTransitionGroup,PropTypes=window.PropTypes,classNames2=window.classNames,R2=window.R});var _2=window._,slice=[].slice,ConversionTracker=I2.ConversionTracker||(I2.ConversionTracker=function(){function r(){}return r.types={impression:1,click:2,purchase:3,download:4,join:5},r.buffer=[],r.find_click=function(e){var t,n,i,a,o,s;for(s=new RegExp(":"+e+"$"),t=this.get_cookie(),i=0,o=t.length;i<o;i++)if(a=t[i],n=_2.isArray(a)?a[0]:a,n.match(s))return a},r.strip_click=function(e){var t,n,i,a,o,s;return o=new RegExp(":"+e+"$"),t=this.get_cookie(),s=0,a=function(){var c,m,b;for(b=[],c=0,m=t.length;c<m;c++){if(i=t[c],n=_2.isArray(i)?i[0]:i,n.match(o)){s+=1;continue}b.push(i)}return b}(),a.length===t.length?0:(this.write_cookie(a),s)},r.after_click_action=function(e,t){var n,i,a,o;if(n=this.find_click(t),!!n)return o=null,a=_2.isArray(n)?(o=n[1],n[0]):n,i=a.replace(/^\d+/,this.types[e]),this.strip_click(t),this.push(i,o),!0},r.download=function(e){return this.after_click_action("download",e)},r.purchase=function(e){return this.after_click_action("purchase",e)},r.join=function(e){return this.after_click_action("join",e)},r.click=function(e){var t,n,i,a,o,s;i=this.types.click+":"+e,a=(s=this.get_active_splits())?[i,s]:i,this.push(i);try{for(n=function(){var c,m,b,k;for(b=this.get_cookie(),k=[],c=0,m=b.length;c<m;c++)t=b[c],o=_2.isArray(t)?t[0]:t,o!==i&&k.push(t);return k}.call(this),n.push(a);n.length>100;)n.shift();return this.write_cookie(n),!0}catch(c){}},r.write_cookie=function(e){return I2.set_cookie("itchio_ca",JSON.stringify(e),{expires:1})},r.get_cookie=function(){var e;if(e=window.Cookies.get("itchio_ca"),e)try{return JSON.parse(e)||[]}catch(t){try{return JSON.parse(decodeURIComponent(e))||[]}catch(n){return[]}}else return[]},r.flush_later=function(){return this.flush_later=_2.throttle(this.flush_now,2e3,{leading:!1}),$(window).on("beforeunload",function(e){return function(){e.flush_now()}}(this)),this.flush_later()},r.encode_buffer=function(e){var t,n,i,a,o,s,c,m,b,k,y,f,d,h,v;for(e==null&&(e=slice.call(this.buffer)),e.sort(),y=[],c=null,s=null,o=null,i=0,m=e.length;i<m;i++)n=e[i],d=n.match(/^(\d+):(\d+):(\d+):(\d+)$/),t=d[0],v=d[1],h=d[2],k=d[3],b=d[4],v&&(v!==c&&(y.push("t"+v),c=v),h!==s&&(y.push("s"+h),s=h),k!==o&&(y.push("o"+k),o=k),a=(+b).toString(36),f=String.fromCharCode("A".charCodeAt(0)+a.length),y.push(""+f+a));return y.join("")},r.get_active_splits=function(){return I2.active_splits},r.flush_url=function(e){var t,n,i;return i=this.encode_buffer(),t=[{name:"x",value:i}],(n=e||this.get_active_splits())&&t.push({name:"s",value:n.join(",")}),I2.root_url("ca.gif")+"?"+$.param(t)},r.flush_now_beacon=function(e){var t;if(navigator.sendBeacon==null)return this.flush_now(e);if(this.buffer.length)if(I2.in_dev&&console.debug.apply(console,["ca(beacon)"].concat(slice.call(_2.compact([this.buffer,e])))),t=this.flush_url(e),navigator.sendBeacon(t))this.buffer=[];else return this.flush_now();return $.when()},r.flush_now=function(e){var t;return this.buffer.length?(I2.in_dev&&console.debug.apply(console,["ca"].concat(slice.call(_2.compact([this.buffer,e])))),t=this.flush_url(e),this.buffer=[],$.Deferred(function(n){return function(i){var a,o;return o=new Image,o.src=t,a=function(){return i.resolve()},o.onerror=a,o.onload=a}}(this))):$.when()},r.push=function(e,t){var n;return this.buffer=function(){var i,a,o,s;for(o=this.buffer,s=[],i=0,a=o.length;i<a;i++)n=o[i],n!==e&&s.push(n);return s}.call(this),this.buffer.push(e),this.buffer.length>50||t?this.flush_now(t):this.flush_later()},r}()),ReferrerTracker=I2.ReferrerTracker||(I2.ReferrerTracker=function(){function r(){}return r.MAX_ITEMS=20,r.get_cookie=function(){return window.Cookies.getJSON("itchio_refs")||[]},r.write_cookie=function(e){return I2.set_cookie("itchio_refs",e,{expires:14})},r.has_ref=function(e,t){var n,i,a,o,s,c;for(t=+t,s=r.get_cookie(),a=0,o=s.length;a<o;a++)if(c=s[a],i=c[0],n=c[1],i===e&&n===t)return!0;return!1},r.push=function(e,t,n){var i,a;if(n&&n.length>0){for(t=+t,a=function(){var o,s,c,m;for(c=this.get_cookie(),m=[],o=0,s=c.length;o<s;o++)i=c[o],!(i[0]===e&&i[1]===t)&&m.push(i);return m}.call(r),I2.in_dev&&console.log("pushing referrer",[e,t,n]),a.unshift([e,t,n]);a.length>r.MAX_ITEMS;)a.pop();return r.write_cookie(a)}},r}()),_react_ready=function(r){return I2.libs.react.done(r)},ConversionLink=null;_react_ready(function(){var r,e,t,n,i;return e=ReactDOMFactories,t=e.a,i=e.p,n=e.button,r={},ConversionLink=R2.component("ConversionLink",{pure:!0,propTypes:{source:PropTypes.number,object_type:PropTypes.number,object_id:PropTypes.number,href:PropTypes.string,target:PropTypes.string,class:PropTypes.any,impression:PropTypes.bool,impression_delay:PropTypes.number,on_click:PropTypes.func,on_impression:PropTypes.func},click:function(){return this.link_ref.current.click()},getInitialState:function(){return{seen:!1}},get_suffix:function(){return this.props.source+":"+this.props.object_type+":"+this.props.object_id},on_click:function(a){var o,s,c,m;if(a.type==="click"&&(o=this.link_ref.current,c=a.metaKey||a.ctrlKey||a.shiftKey||this.props.target==="_blank",i=this.handle_click(),!c&&i.state()!=="resolved"))return a.preventDefault(),m=new Date,s=function(b){return function(){if(s)return s=null,window.location=b.props.href}}(this),setTimeout(function(b){return function(){return typeof s=="function"?s():void 0}}(this),200),i.done(s)},on_mouse_up:function(a){if(a.button===1)return this.handle_click()},handle_click:function(){var a,o;return this.props.object_type&&this.props.source&&this.props.object_id?this.state.clicked?this.state.clicked:(o=ConversionTracker.types.click+":"+this.get_suffix(),r[o]?$.when():(r[o]=!0,ConversionTracker.click(this.get_suffix()),i=ConversionTracker.flush_now_beacon(),this.props.track_referrer&&ReferrerTracker.push.apply(ReferrerTracker,this.props.track_referrer),this.setState({clicked:i}),typeof(a=this.props).on_click=="function"&&a.on_click(),i)):$.when()},componentDidMount:function(){var a;if(this.props.object_type&&this.props.source&&this.props.object_id&&this.props.impression!==!1&&!r[ConversionTracker.types.impression+":"+this.get_suffix()])return a=function(o){return function(){var s;if(!o.unmounted)return s=o.link_ref.current,o.unbind_visibility=$(s).lazy_images({elements:[s],show_images:function(){var c,m;return m=ConversionTracker.types.impression+":"+o.get_suffix(),r[m]||(ConversionTracker.push(m),r[m]=!0),typeof(c=o.props).on_impression=="function"&&c.on_impression(),o.setState({seen:!0}),typeof o.unbind_visibility=="function"&&o.unbind_visibility(),o.unbind_visibility=null}})}}(this),this.props.impression_delay?setTimeout(a,this.props.impression_delay):a()},componentWillUnmount:function(){return this.unmounted=!0,typeof this.unbind_visibility=="function"?this.unbind_visibility():void 0},render:function(){var a;return t({ref:this.link_ref||(this.link_ref=React2.createRef()),href:this.props.href,className:this.props.class||this.props.className,onClick:this.on_click,onMouseUp:this.on_mouse_up,target:this.props.target,tabIndex:(a=this.props.tabindex)!=null?a:this.props.tabIndex,"aria-hidden":this.props["aria-hidden"]},this.props.children)}})}),window.init_ConversionLink=function(r,e){R2.render(r,()=>ConversionLink(e))};var JamGrid=function(){function r(e){var t,n,i,a;for(this.el=$(e),n=this.el.find(".date_countdown"),i=0,a=n.length;i<a;i++)t=n[i],new I.TimestampCountdown(t,$(t).text());_.defer(function(o){return function(){return o.el.lazy_images()}}(this))}return r}();window.init_JamGrid=function(r,e){new JamGrid(r)},window.init_BrowseJams=function(r,e){},window.init_ViewJamPlain=function(r,e){new I.ViewJamPlain(r,e)};var LazyImage=null;_react_ready(function(){var r,e;return r=ReactDOMFactories,e=r.img,LazyImage=R2.component("LazyImage",{pure:!0,propTypes:{src:PropTypes.string,src_set:PropTypes.string,width:PropTypes.number,height:PropTypes.number},getInitialState:function(){return{visible:!1}},componentDidMount:function(){var t;return t=this.image_ref.current,this.unbind_lazy_images=$(t).lazy_images({elements:[t],show_images:function(n){return function(){var i;return n.setState({visible:!0}),typeof(i=n.props).on_reveal=="function"?i.on_reveal():void 0}}(this)})},componentWillUnmount:function(){return typeof this.unbind_lazy_images=="function"?this.unbind_lazy_images():void 0},render:function(){var t,n;return e({ref:this.image_ref||(this.image_ref=React2.createRef()),className:classNames2(this.props.class,this.props.className,{lazy_loaded:this.state.loaded,lazy_visible:this.state.visible}),alt:this.props.alt,width:this.state.loaded?this.props.width:(t=this.props.initial_width)!=null?t:this.props.width,height:this.state.loaded?this.props.height:(n=this.props.initial_height)!=null?n:this.props.height,src:this.state.visible?this.props.src:void 0,srcSet:this.state.visible?this.props.src_set:void 0,onLoad:this.state.visible&&!this.state.loaded?function(i){return function(){var a;return i.setState({loaded:!0}),typeof(a=i.props).on_load=="function"?a.on_load():void 0}}(this):void 0})}})}),window.init_LazyImage=function(r,e){R2.render(r,()=>LazyImage(e))};var jQuery,jquery_default=jQuery=window.$;I2.Lightbox||console.warn("I.Lightbox is being depended on but is missing from the page");var Lightbox=I2.Lightbox,extend=function(r,e){for(var t in e)hasProp.call(e,t)&&(r[t]=e[t]);function n(){this.constructor=r}return n.prototype=e.prototype,r.prototype=new n,r.__super__=e.prototype,r},hasProp={}.hasOwnProperty,PickImageLightbox=function(r){extend(e,r);function e(){return e.__super__.constructor.apply(this,arguments)}return e.prototype.init=function(t){return this.el.dispatch("click",{tab_btn:function(n){return function(i){var a;return a=i.data("tab"),n.el.find(".tab_content").hide().filter("[data-tab="+a+"]").show(),n.el.find(".tab_btn").removeClass("selected").filter(i).addClass("selected")}}(this),pick_image_btn:function(n){return function(i){return typeof t=="function"?t(i.data("url")):void 0}}(this),upload_image_btn:function(n){return function(i){return I2.upload_image({url:I2.root_url("dashboard/upload-image"),thumb_size:"original"}).progress(function(){if(!i.prop("disabled"))return i.prop("disabled",!0).addClass("disabled"),i.data("original_text",i.text()),i.text("Uploading...")}).fail(function(){return i.prop("disabled",!1).removeClass("disabled"),i.text(i.data("original_text"))}).done(function(a){var o,s;return i.prop("disabled",!1).removeClass("disabled"),i.text(i.data("original_text")),a.success?typeof t=="function"?t(a.upload.thumb_url):void 0:(o=((s=a.errors)!=null?s[0]:void 0)||"Image upload failed",I2.flash(o,"error"))})}}(this)})},e}(Lightbox),dompurify_is_configured,Redactor=null,default_redactor_opts={plugins:["source","table","alignment","video","addimage"],toolbarFixed:!1,buttons:["format","bold","italic","deleted","lists","link"],minHeight:250,linkSize:80};dompurify_is_configured=!1;var sanitize_html=function(r){return dompurify_is_configured||(dompurify_is_configured=!0,DOMPurify.addHook("uponSanitizeElement",function(e,t,n){if(e.tagName==="IFRAME"&&e.innerHTML)return e.setAttribute("data-tmp-html",e.innerHTML),e.innerHTML=""}),DOMPurify.addHook("afterSanitizeElements",function(e,t,n){if(e.tagName==="IFRAME"&&e.getAttribute("data-tmp-html"))return e.innerHTML=e.getAttribute("data-tmp-html"),e.removeAttribute("data-tmp-html")})),DOMPurify.sanitize(r,{ADD_TAGS:["iframe"],ADD_ATTR:["width","height","frameborder","allowfullscreen","title","allow","scrolling","target"]})},redactor=function(r,e){var t,n,i,a;if(e==null&&(e={}),!window.location.href.match(/\bredactor=0\b/)&&!I2.in_test){if(!jquery_default.fn.redactor){console.warn("tried to create redactor text element without redactor on page",r[0]);return}e=jquery_default.extend({},default_redactor_opts,e),e.source===!1&&(delete e.source,e.plugins=function(){var o,s,c,m;for(c=e.plugins,m=[],o=0,s=c.length;o<s;o++)i=c[o],i!=="source"&&m.push(i);return m}()),r.closest(".lightbox_widget").exists()&&e.plugins&&(e.plugins=function(){var o,s,c,m;if(i!=="addimage"){for(c=e.plugins,m=[],o=0,s=c.length;o<s;o++)i=c[o],m.push(i);return m}}()),window.DOMPurify&&(t=r.val(),a=sanitize_html(t),a!==t&&r.val(a));try{return r.redactor(e)}catch(o){return n=o,I2.event("error","redactor","invalid_content"),r.parent().replaceWith(r).end().val("").redactor(e)}}};jquery_default.Redactor?Redactor=jquery_default.Redactor:I2.libs.redactor.done(function(){return Redactor=jquery_default.Redactor,jquery_default.Redactor.prototype.addimage=function(){return{langs:{en:{}},init:function(){var r;return r=this.button.addAfter("image","image","Add image"),this.button.setIcon(r,'<i class="re-icon-image"></i>'),this.button.addCallback(r,this.addimage.show)},show:function(){return Lightbox.open_remote(I2.root_url("dashboard/upload-image"),PickImageLightbox,function(r){return function(e){var t;return I2.Lightbox.close(),t=jquery_default("<img>").attr("src",e)[0].outerHTML,r.placeholder.hide(),r.buffer.set(),r.insert.html(t)}}(this))}}}});var extend2=function(r,e){for(var t in e)hasProp2.call(e,t)&&(r[t]=e[t]);function n(){this.constructor=r}return n.prototype=e.prototype,r.prototype=new n,r.__super__=e.prototype,r},hasProp2={}.hasOwnProperty,CollectionLightbox=I2.CollectionLightbox=function(r){extend2(e,r);function e(){return e.__super__.constructor.apply(this,arguments)}return e.prototype.init=function(){var t;return I2.has_follow_button(this.el),this.el.find("input[type='radio']:first").prop("checked",!0),this.el.on("click change",".collection_option",function(n){return function(i){var a;return a=$(i.currentTarget),a.find("input[type='radio']").prop("checked",!0)}}(this)),t=this.el.find("form").remote_submit(function(n){return function(i){if(i.errors){if(I2.add_recaptcha_if_necessary(t,i.errors))return;t.set_form_errors(i.errors);return}return n.el.addClass("is_complete"),n.el.find(".after_submit .collection_name").text(i.title).attr("href",i.url)}}(this)),this.with_redactor(function(n){return function(){return redactor(n.el.find("textarea"),{minHeight:40,source:!1,buttons:["bold","italic","deleted","lists","link"]})}}(this)),this.with_selectize(function(n){return function(){return n.el.find("select.collection_input").selectize()}}(this))},e}(Lightbox),dayjs,dayjs_default=dayjs=window.dayjs;window._dayjs_setup||(dayjs.extend(window.dayjs_plugin_duration),dayjs.extend(window.dayjs_plugin_calendar),dayjs.extend(window.dayjs_plugin_advancedFormat),dayjs.extend(window.dayjs_plugin_relativeTime),dayjs.extend(window.dayjs_plugin_utc),window._dayjs_setup=!0);var coerce_timestamp,slice2=[].slice;coerce_timestamp=function(r){var e;return(e=typeof r=="string"&&r.match(/(^\d{4}\-\d{1,2}\-\d{1,2}) (\d{1,2}:\d{1,2}:\d{1,2})$/))?e[1]+"T"+e[2]+"Z":r};var parse_timestamp=function(r){return dayjs_default(coerce_timestamp(r)).toDate()},format_timestamp=function(){var r,e,t,n;switch(n=arguments[0],e=arguments[1],r=3<=arguments.length?slice2.call(arguments,2):[],e==null&&(e="fromNow"),n=coerce_timestamp(n),e){case"fromNow":return dayjs_default(n).fromNow();case"calendar":return dayjs_default(n).calendar(null,{sameElse:"MMMM Do YYYY"});case"format":return(t=dayjs_default(n)).format.apply(t,r);default:throw new Error("unknown method for format_timestamp: "+e)}},SubmitChallenge=null;_react_ready(function(){var r,e,t,n;return e=ReactDOMFactories,n=e.input,t=e.fragment,t=React2.createElement.bind(null,React2.Fragment),t.type=React2.fragment,r=R2.package("Forms"),SubmitChallenge=r("SubmitChallenge",{pure:!0,getInitialState:function(){return{}},solve_deferred:function(){var i,a;return this.props.challenge===this.state.challenge?this.state.deferred:(i=$.Deferred(),a=+new Date,this.setState({challenge:this.props.challenge,solution:null,duration:null,busy:!0,deferred:i}),I2.solve_challenge(this.props.challenge).then(function(o){return function(s){return o.setState({busy:!1,duration:new Date-a,solution:s})}}(this)),i)},render:function(){var i;return i=this.state.solution||"",t({},n({type:"hidden",name:"challenge",value:this.props.challenge}),n({key:i,type:"hidden",name:"challenge_response",value:i,ref:function(a){return function(o){var s;if(o&&a.state.solution)return(s=a.state.deferred)!=null?s.resolve():void 0}}(this)}))}})});var TurnstileInput=null,with_turnstile=null;_react_ready(function(){var r,e,t,n,i;return r=ReactDOMFactories,n=r.p,t=r.form,e=r.div,i=null,with_turnstile=function(){return i||(i=new Promise(function(a,o){var s,c,m;return c=setTimeout(function(){return o("Timeout waiting for turnstile")},5e3),window._itch_turnstile_loaded=function(){return clearTimeout(c),a(window.turnstile)},m="https://challenges.cloudflare.com/turnstile/v0/api.js?onload=_itch_turnstile_loaded",s=document.createElement("script"),s.defer=!0,s.type="text/javascript",s.src=m,document.head.appendChild(s)})),i},TurnstileInput=function(a){var o,s,c,m;return m=React2.createRef(),s=React2.useState(null),o=s[0],c=s[1],React2.useEffect(function(){with_turnstile().then(function(b){return function(k){var y;if(y=m.current)return k.render(y,{sitekey:a.sitekey,callback:a.on_verify})}}(this),function(b){return function(k){return c(k)}}(this))},[]),o?n({className:"form_errors"},"Human verification failed to load, please make sure you aren't blocking Cloudflare's Turnstile."):e({ref:m,className:"turnstile_container"})},TurnstileInput.propTypes={sitekey:PropTypes.string.isRequired,on_verify:PropTypes.func},TurnstileInput=React2.createElement.bind(null,React2.memo(TurnstileInput))});var ReportLightbox=null;_react_ready(function(){var r,e,t,n,i,a,o,s,c,m,b,k,y,f,d;return e=ReactDOMFactories,m=e.input,o=e.form,s=e.fragment,c=e.h2,k=e.p,t=e.a,a=e.div,b=e.label,f=e.strong,y=e.span,d=e.textarea,n=e.button,i=e.code,s=React.createElement.bind(null,React.Fragment),s.type=React.fragment,r=R2.package("Game"),ReportLightbox=r("ReportLightbox",{pure:!0,render:function(){var h;return R2.Lightbox({className:this.enclosing_class_name()},(h=this.state)!=null&&h.submitted?this.render_after_submit():this.render_report_form())},get_report_options:function(){var h;return h=[["broken","Broken","Doesn't run, download, or crashes"],["offensive","Offensive material","Examples include hate speech, shock content, discriminatory material, incitement of violence, or harassment of others"],["malware","Malicious software or virus","This developer is trying to distribute software that harms the computer of the person running"],["doesnt_own","Uploader not authorized to distribute"],["miscategorized","Miscategorized","Shows up on wrong part of itch.io, incorrect tags, incorrect platforms, etc."],["spam","Spam","Unwanted or unsolicited advertising"],["other",this.tt("game.report_form.reason_other")]],this.props.jam&&h.unshift(["invalid_jam_submission","Invalid jam submission","Empty or incomplete page, breaks rules, etc."]),h},render_report_form:function(){var h,v,u,p,l;return s({},c({},this.tt("game.report_form.title",{page_title:this.props.page_title})),o({className:"form",method:"post",action:this.props.submit_url,onSubmit:function(g){return function(w){var j,N,S;return w.preventDefault(),g.setState({errors:null,loading:!0}),j=(N=g.submit_challenge_ref)!=null&&(S=N.current)!=null?S.solve_deferred():void 0,I.remote_submit($(w.target),j).done(function(x){if(x.errors){g.setState({loading:!1,errors:x.errors});return}return g.setState({submitted:!0,report_id:x.report_id})})}}(this)},R2.CSRF({}),this.props.challenge?R2.Forms.SubmitChallenge({ref:this.submit_challenge_ref||(this.submit_challenge_ref=React.createRef()),challenge:this.props.challenge}):void 0,this.props.support_email||this.props.support_link?k({className:"support_notice"},this.tt("game.report_form.support_from_creator")," ",this.props.support_link?t({rel:"nofollow",target:"blank",className:"forward_link",href:this.props.support_link},this.tt("game.report_form.support_page_link")):t({rel:"nofollow",target:"blank",className:"forward_link",href:"mailto:"+this.props.support_email},this.tt("game.report_form.email_creator_link"))):void 0,(h=this.state)!=null&&h.errors?R2.Forms.FormErrors({errors:this.state.errors,animated:!0,scroll_into_view:!0}):void 0,k({},this.tt("game.report_form.form_description")),this.props.jam?k({},"This report will be made available to hosts of ",t({href:this.props.jam.url},this.props.jam.name),"."):void 0,a({className:"input_row"},a({className:"label"},this.tt("game.report_form.reason_label")),R2.Forms.RadioButtons({name:"report[reason]",value:((v=this.state)!=null?v.reason:void 0)||"",onChange:function(g){return function(w){return g.setState({reason:w.target.value})}}(this),options:this.get_report_options()})),((u=this.state)!=null?u.reason:void 0)==="doesnt_own"?a({className:"input_row"},f({},"Are you the copyright owner and need to file a DMCA notice?")," ",t({target:"_blank",href:this.props.itchio_support_url,className:"forward_link"},"Contact our support team so we can step you through the process")):void 0,a({className:"input_row"},b({},a({className:"label"},this.tt("game.report_form.description_label"),y({className:"sub"}," \u2014 ",this.description_description())),d({maxLength:"2048",name:"report[description]",required:this.description_required(),placeholder:this.description_required()?this.t("misc.forms.required"):this.t("misc.forms.optional")}))),R2.Forms.TextInputRow({title:"Your email",type:"email",sub:"If your report needs a reply we'll use it to communicate with you",name:"report[email]",defaultValue:this.props.current_user_email,required:this.email_required(),placeholder:this.email_required()?this.t("misc.forms.required"):this.t("misc.forms.optional")}),this.props.turnstile_sitekey?TurnstileInput({sitekey:this.props.turnstile_sitekey}):void 0,a({callback:"buttons"},n({disabled:(p=this.state)!=null?p.loading:void 0,className:classNames("button",{disabled:(l=this.state)!=null?l.loading:void 0})},this.tt("game.report_form.submit_report_button")))))},render_after_submit:function(){return a({className:"report_submitted"},c({},this.tt("game.report_form.report_received_header")),k({},"You report will be reviewed shortly, and action will be taken if necessary. If you have any additional requests then you can ",t({href:this.props.itchio_support_url,target:"_blank"},"contact our support team"),"."),this.state.report_id?k({},"If you need to talk to itch.io staff about your report please use the following reference code: ",i({},f({},"R-"+this.state.report_id)),"."):void 0,k({},n({type:"button",className:"textlike",onClick:function(h){return function(v){return I.Lightbox.close()}}(this)},this.tt("misc.lightboxes.close"))))},description_description:function(){var h;switch((h=this.state)!=null?h.reason:void 0){case"broken":return"Please explain what is broken. Include how you tried to run the project, what operating system or browser you're using, and any error messages you saw. We will share your error with the creator to help them fix the issue.";case"invalid_jam_submission":return"Please explain why this submission should be disqualified or removed";case"miscategorized":return"Please explain how this project should be categorized.";default:return"Please provide a summary of your report"}},description_required:function(){var h,v;return(h=(v=this.state)!=null?v.reason:void 0)==="broken"||h==="miscategorized"||h==="other"||h==="invalid_jam_submission"},email_required:function(){var h,v;return(h=(v=this.state)!=null?v.reason:void 0)==="broken"||h==="doesnt_own"}})});var QuarantineLightbox=null,QuarantineLinkLightbox=null;_react_ready(function(){var r,e,t,n,i,a,o,s,c,m,b,k,y,f,d,h,v,u,p,l,g;return e=ReactDOMFactories,u=e.span,c=e.fragment,y=e.p,p=e.strong,t=e.a,m=e.h2,v=e.section,b=e.h3,g=e.ul,k=e.li,f=e.pre,i=e.button,s=e.div,a=e.code,c=React.createElement.bind(null,React.Fragment),c.type=React.fragment,r=R2.package("Game"),o=ReactDOMFactories.details,l=ReactDOMFactories.summary,n=ReactDOMFactories.abbr,h=function(w){return n({title:w,className:"datetime"},u({className:"icon icon-stopwatch","aria-hidden":"true"})," ",format_timestamp(w))},d=function(w){return w==null&&(w={}),c({},y({},p({},"Our system has flagged this page for additional review due to potential suspicious behavior from the page owner. ")),y({},"If someone has asked you to download from this page and you don't fully trust them, or their behavior isn't what you recognize, then we don't recommend downloading this file until our team has reviewed the page."),w.password_protected?y({},"Password-protecting files or pages is a technique often used by scammers in an attempt to block virus and other security scans from detecting malware. Do not trust password-protected files unless you fully trust the uploader."):void 0,y({},t({href:"https://itch.io/t/1659440/psa-beware-discord-scammers",className:"forward_link",target:"_blank"},'Learn more about the "Try my game" Discord scam')))},QuarantineLinkLightbox=r("QuarantineLinkLightbox",{pure:!0,render:function(){return R2.Lightbox({className:classNames2(this.enclosing_class_name(),"game_quarantine_lightbox_widget")},m({},"WARNING: This Link Has Been Quarantined"),d(),v({},b({},"Page and Account Details"),g({className:"account_summary"},this.props.suspicious_access?k({},u({className:"icon icon-warning"})," ","Account shows suspicious activity"):void 0,k({},"Page created ",h(this.props.game_created_at)))),o({},l({},"I understand the risks, let me access anyway..."),y({},"You can access the link by copy and pasting into your browser."),f({},this.props.link_url)),this.props.report_url?o({},l({},"Report this page..."),y({},"Need to notify staff of additional suspicious activity?"),i({type:"button",className:"button",onClick:this.report_page},"Report this page")):void 0,y({className:"buttons"},i({type:"button",className:"button outline close_btn"},"Close")))},report_page:function(){if(this.props.report_url)return Lightbox.open_remote_react(this.props.report_url,function(w){return ReportLightbox(w)})}}),QuarantineLightbox=r("QuarantineLightbox",{pure:!0,render:function(){return R2.Lightbox({className:this.enclosing_class_name()},m({},"WARNING: This Page Has Been Quarantined"),d({password_protected:!0}),v({},b({},"Page and Account Details"),g({className:"account_summary"},k({},"Page created ",h(this.props.game_created_at)),k({},"Account created ",h(this.props.user_created_at)),this.props.user_email_verified?void 0:k({},"Account's email is not verified"),this.props.user_email_invalid?k({},u({className:"icon icon-warning"})," ","Account may have fake email address"):void 0,this.props.user_recent_countries?k({},"Regions this account has connected from: ",p({},new Intl.ListFormat("en").format(this.props.user_recent_countries))):void 0)),o({},l({},"I understand the risks, let me download anyway..."),s({className:"download_row"},i({type:"button",className:"button",onClick:this.force_download},"Download ",this.props.upload.name?a({},this.props.upload.name):void 0,this.props.upload.size?u({className:"upload_size"}," ("+I.format_bytes(this.props.upload.size)+")"):void 0)," Uploaded ",h(this.props.upload.created_at))),o({},l({},"Report this page..."),y({},"Need to notify staff of additional suspicious activity?"),i({type:"button",className:"button",onClick:this.report_page},"Report this page")),y({className:"buttons"},i({type:"button",className:"button outline close_btn"},"Close")))},report_page:function(){return Lightbox.open_remote_react(this.props.report_url,function(w){return ReportLightbox(w)})},force_download:function(){return this.props.on_force_download?this.props.on_force_download():(I.bypass_quarantine=!0,Lightbox.close())}})});var _hex_piece,hex_color,hsl_to_rgb,luma,mix_color,parse_color,parse_color_safe,readable_text_color,relative_luma,rgb_helper,rgb_to_hsl,rgb_to_yuv,scale_luma,sub_color,sub_color2,yuv_to_rgb,slice3=[].slice;rgb_helper=function(r,e,t){return r<0?r+=1:r>1&&(r-=1),6*r<1?e+(t-e)*6*r:2*r<1?t:3*r<2?e+(t-e)*(2/3-r)*6:e},hsl_to_rgb=function(r,e,t){var n,i,a,o,s;return r=r/360,e=e/100,t=t/100,e===0?(a=t,i=t,n=t):(s=t<.5?t*(1+e):t+e-t*e,o=2*t-s,a=rgb_helper(r+1/3,o,s),i=rgb_helper(r,o,s),n=rgb_helper(r-1/3,o,s)),[a*255,i*255,n*255]},rgb_to_hsl=function(r,e,t){var n,i,a,o,s;return r=r/255,e=e/255,t=t/255,o=Math.min(r,e,t),a=Math.max(r,e,t),s=0,n=0,i=(o+a)/2,o!==a&&(s=i<.5?(a-o)/(a+o):(a-o)/(2-a-o),n=function(){switch(a){case r:return(e-t)/(a-o);case e:return 2+(t-r)/(a-o);case t:return 4+(r-e)/(a-o)}}()),n<0&&(n+=6),[n*60,s*100,i*100]},parse_color=function(){var r,e,t,n,i,a,o,s,c,m,b;if(m=arguments[0],s=2<=arguments.length?slice3.call(arguments,1):[],n="[a-fA-f0-9]",i=n+n,b="^#("+n+")("+n+")("+n+")$",c="^#("+i+")("+i+")("+i+")$",a=m.match(new RegExp(b)),a)r=a[0],o=a[1],t=a[2],e=a[3],o+=o,t+=t,e+=e;else{if(a=m.match(new RegExp(c)),!a)return null;r=a[0],o=a[1],t=a[2],e=a[3]}return[parseInt(o,16),parseInt(t,16),parseInt(e,16)]},parse_color_safe=function(){var r;return r=parse_color.apply(null,arguments),r||[0,0,0]},_hex_piece=function(r){var e;return e=Math.floor(r).toString(16),e.length===1?"0"+e:e},hex_color=function(r,e,t){return r=Math.min(255,Math.max(0,r)),e=Math.min(255,Math.max(0,e)),t=Math.min(255,Math.max(0,t)),"#"+_hex_piece(r)+_hex_piece(e)+_hex_piece(t)},sub_color=function(r,e,t){var n,i,a,o,s;return e==null&&(e=15),t==null&&(t=.8),o=rgb_to_hsl.apply(null,parse_color_safe(r)),n=o[0],s=o[1],i=o[2],a=i>50?i-e:i+e,a=Math.max(0,Math.min(100,a)),hex_color.apply(null,hsl_to_rgb(n,s*t,a))},luma=function(r,e,t){return(.299*r+.587*e+.114*t)/255},relative_luma=function(r,e,t){return r=r/255,e=e/255,t=t/255,r=r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4),e=e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4),t=t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4),.2126*r+.7152*e+.0722*t},rgb_to_yuv=function(r,e,t){var n,i,a;return a=.299*r+.587*e+.114*t,n=(t-a)*.565,i=(r-a)*.713,[a,n,i]},yuv_to_rgb=function(r,e,t){var n,i,a;return a=r+1.403*t,i=r-.344*e-.714*t,n=r+1.77*e,[a,i,n]},scale_luma=function(r,e,t,n){var i,a,o,s;return n==null&&(n=.1),i=rgb_to_yuv(r,e,t),s=i[0],a=i[1],o=i[2],n<0?s+=n*s:s+=n*(255-s),yuv_to_rgb(s,a,o)},readable_text_color=function(r,e,t){var n,i;return typeof r=="string"&&(n=parse_color_safe(r),r=n[0],e=n[1],t=n[2]),i=relative_luma(r,e,t),i>=.45?mix_color(hex_color(r,e,t),"#000000",.2):"#ffffff"},sub_color2=function(r,e,t){var n,i,a,o,s;return e==null&&(e=.15),t==null&&(t=.5),s=parse_color_safe(r),o=s[0],i=s[1],n=s[2],a=luma(o,i,n),a<t?hex_color.apply(null,scale_luma(o,i,n,e)):hex_color.apply(null,scale_luma(o,i,n,-e))},mix_color=function(r,e,t){var n,i,a,o,s,c,m,b,k;return t===1?r:t===0?e:(b=parse_color_safe(r),c=b[0],o=b[1],i=b[2],k=parse_color_safe(e),m=k[0],s=k[1],a=k[2],n=1-t,hex_color(c*t+m*n,o*t+s*n,i*t+a*n))};var Color=I2.Color||(I2.Color={hsl_to_rgb,rgb_to_hsl,parse_color,parse_color_safe,hex_color,sub_color,luma,relative_luma,rgb_to_yuv,yuv_to_rgb,scale_luma,readable_text_color,sub_color2,mix_color}),GridSizer=null;_react_ready(function(){var r,e,t,n;return e=ReactDOMFactories,t=e.a,n=e.div,r=R2.package("Index"),GridSizer=r("GridSizer",{propTypes:{children:PropTypes.array},getInitialState:function(){return{}},getDefaultProps:function(){return{cell_margin:20,min_width:0,expected_width:315,min_columns:1,crop_on_resize:!0}},componentDidMount:function(){return _.defer(function(i){return function(){return i.calculate_cell_sizes()}}(this)),this.resize_callback=_.debounce(function(i){return function(){return i.calculate_cell_sizes()}}(this),250),$(window).on("resize",this.resize_callback)},componentWillUnmount:function(){return this.unmounted=!0,$(window).off("resize",this.resize_callback)},componentDidUpdate:function(i){if(i.children!==this.props.children)return _.defer(function(a){return function(){return a.calculate_cell_sizes()}}(this))},calculate_cell_sizes:function(i){var a,o,s,c,m,b;if(i==null&&(i=!1),!this.unmounted&&(o=this.get_width(),this.state.container_width!==o&&(b=this.props.expected_width+this.props.cell_margin,a=o+this.props.cell_margin,c=a/b,m=Math.ceil(c),m<this.props.min_columns&&(m=this.props.min_columns),s=a/m-this.props.cell_margin,s=Math.floor(s),s<this.props.min_width&&m>1&&(m-=1,s=a/m-this.props.cell_margin,s=Math.floor(s)),this.setState({container_width:o,cell_width:s,cells_per_row:m},this.props.on_reshape),i===!1)))return _.defer(function(k){return function(){return k.calculate_cell_sizes(!0)}}(this))},get_width:function(){var i,a,o,s,c;return i=this.container(),a=(c=i[0])!=null?c.getBoundingClientRect().width:void 0,s=a-Math.floor(a),o=Math.floor(i.width()),s>0&&(o-=1),o},get_max_num_visible:function(){if(this.props.fit_rows&&this.state.cells_per_row)return this.state.cells_per_row*this.props.fit_rows},get_num_visible:function(){var i;return i=this.props.children.length,this.props.fit_rows&&this.state.cells_per_row?Math.min(i,this.state.cells_per_row*this.props.fit_rows):i},get_visible_children:function(){var i;return i=this.props.children,this.props.fit_rows&&this.state.cells_per_row&&(i=i.slice(0,this.state.cells_per_row*this.props.fit_rows)),i},render:function(){var i,a,o,s,c,m;return i=this.get_visible_children(),c={display:"grid",alignItems:"start",gap:this.props.cell_margin+"px"},a=this.props.crop_on_resize?{overflowX:"hidden"}:void 0,this.props.crop_on_resize&&this.state.container_width&&(c.width=this.state.container_width+"px"),m=this.state.cell_width&&this.state.cells_per_row?c.gridTemplateColumns="repeat("+this.state.cells_per_row+", "+this.state.cell_width+"px)":(s=window.innerWidth<400?5/9:7/9,o=Math.floor(this.props.expected_width*s),c.gridTemplateColumns="repeat(auto-fill, minmax("+o+"px, 1fr))"),this.enclose({component:this.props.component,style:a},React.createElement(this.props.sizer_component||"div",{className:"grid_sizer_children",style:c},i))}})});var slice4=[].slice,GameGrid=null,GameCell=null;_react_ready(function(){var r,e,t,n,i,a,o,s,c,m,b,k,y;return n=ReactDOMFactories,y=n.span,o=n.div,i=n.a,m=n.label,b=n.p,a=n.button,s=n.fragment,k=n.section,c=n.h2,s=React2.createElement.bind(null,React2.Fragment),s.type=React2.fragment,r=R2.package("Index"),e={linux:"tux",osx:"apple",windows:"windows8",android:"android"},t={linux:"Linux",osx:"macOS",windows:"Windows",android:"Android"},r("PlatformList",{pure:!0,getDefaultProps:function(){return{prefix:"Available for"}},render:function(){var f,d,h,v,u;if(!(this.props.platforms&&this.props.platforms.length))return null;for(v=[],u=this.props.platforms,f=0,d=u.length;f<d;f++)h=u[f],v.length>0&&v.push(" "),v.push(y({key:h,className:"icon icon-"+e[h],"aria-hidden":"true",title:this.props.prefix+" "+t[h]}));return v}}),r("StarRating",{propTypes:{average:PropTypes.number,count:PropTypes.number},pure:!0,render:function(){var f,d;return f=this.props.average||0,o({className:"game_rating",title:"Average: "+f.toFixed(2)},y({className:"screenreader_only"},"Average rating "+f.toFixed(2)+" out of 5"),o({className:"star_value"},o({className:"star_fill",style:{width:(f/5*100).toFixed(5)+"%"}},function(){var h,v;for(v=[],d=h=1;h<=5;d=++h)v.push(y({key:d,className:"star icon-star","aria-hidden":"true"}));return v}()),o({className:"star_holes"},function(){var h,v;for(v=[],d=h=1;h<=5;d=++h)v.push(y({key:d,className:"star icon-star2","aria-hidden":"true"}));return v}()))," ",y({className:"rating_count"},"("+I.format_integer(this.props.count),y({className:"screenreader_only"}," total ratings"),")"))}}),GameCell=r("GameCell",{pure:!0,getDefaultProps:function(){return{show_price:!0,show_short_text:!0,show_platforms:!0,show_rating:!0,show_cell_tools:!0}},getInitialState:function(){return{game:this.build_game()}},componentDidUpdate:function(f){if(f.build_game_object!==this.props.build_game_object||f.game!==this.props.game)return this.setState({game:this.build_game()})},update_focus:function(f){return this.setState({cell_tools_focused:f.currentTarget.contains(document.activeElement)})},build_game:function(){return this.props.build_game_object?this.props.build_game_object(this.props.game):this.props.game},add_to_collection:function(f){if(I.current_user)return f.preventDefault(),Lightbox.open_remote("/game/collections/"+this.state.game.id,CollectionLightbox)},mark_gif_loaded:function(){return this.setState({gif_loaded:!0})},track_grid_referrer:function(){if(this.props.grid_referrer)return ReferrerTracker.push("game",this.state.game.id,this.props.grid_referrer)},game_link:function(){var f,d,h;return h=arguments[0],f=2<=arguments.length?slice4.call(arguments,1):[],this.props.conversion_source?(d=$.extend({object_type:1,object_id:this.state.game.id,source:this.props.conversion_source,on_click:this.props.grid_referrer?this.track_grid_referrer:void 0},h),ConversionLink.apply(null,[d].concat(slice4.call(f)))):i.apply(null,[h].concat(slice4.call(f)))},render_user:function(f,d){return d==null&&(d=null),i({key:d,className:"user_link",href:f.url,target:this.props.target},f.name)},render:function(){var f,d,h,v,u,p,l,g,w,j,N,S,x;return l=this.state.game,x=l.user,w=l.platforms?function(){var M,C,L,T;for(L=l.platforms,T=[],M=0,C=L.length;M<C;M++)g=L[M],e[g]&&T.push(g);return T}():void 0,u=l.cover_color?(N=Color.parse_color(l.cover_color),j=N[0],p=N[1],f=N[2],"rgba("+j+", "+p+", "+f+", 0.5)"):void 0,this.enclose({"data-game_id":l.id,dir:"auto",className:classNames2("game_cell",{focused:this.state.cell_tools_focused}),onMouseEnter:this.state.game.gif_cover?function(M){return function(){return M.setState({hovering:!0})}}(this):void 0,onMouseLeave:this.state.game.gif_cover?function(M){return function(){return M.setState({hovering:!1})}}(this):void 0},o({className:"bordered",style:u&&{backgroundColor:u}},this.game_link({href:l.url,target:this.props.target,className:"game_thumb","aria-hidden":"true",tabIndex:-1},typeof(d=this.props).render_thumb_extras=="function"?d.render_thumb_extras(l):void 0,l.cover?LazyImage({src:l.cover}):o({className:"missing_image"},"no image :("),l.gif_cover?o({className:"gif_label"},"GIF"):void 0,(S=this.state)!=null&&S.hovering&&l.gif_cover?React2.createElement(React2.Fragment,{},this.state.gif_loaded?void 0:o({className:"gif_loading_overlay"}),LazyImage({className:"gif_cover",src:l.gif_cover,on_load:this.mark_gif_loaded})):void 0),this.props.show_price&&l.sale_rate?o({className:classNames2("sale_flag",{reverse_sale:l.sale_rate<0})},o({className:"rate"},Math.abs(l.sale_rate)+"%"),o({className:"mini_label"},l.sale_rate<0?"more":"off")):void 0,w!=null&&w.length?o({className:"p_data fading_data"},r.PlatformList({platforms:w})):void 0,this.props.show_cell_tools?o({className:"fading_data cell_tools",onFocus:this.update_focus,onBlur:this.update_focus},typeof(h=this.props).render_cell_extras=="function"?h.render_cell_extras(l):void 0,i({href:l.url+"/add-to-collection",title:this.t("add_to_collection.add_button"),className:"add_to_collection_btn",onClick:this.add_to_collection,role:"button",onKeyDown:function(M){return function(C){if(C.key===" ")return M.add_to_collection(C)}}(this)},y({className:"icon icon-playlist_add","aria-hidden":"true"}),y({className:"screenreader_only"},"Add to collection"))):void 0),o({className:"label"},function(){if(this.props.show_price)switch(l.flag){case"free":return o({className:"meta_tag free_tag"},"Free");case"web":return o({className:"meta_tag free_tag"},"Web");case"demo":return o({className:"meta_tag demo_tag"},"Demo");default:if(l.price)return o({className:classNames2("price_tag meta_tag")},l.price)}}.call(this),this.game_link({target:this.props.target,className:"title",title:l.title,href:l.url},l.title)),o({className:"user_row"},l.contributors?l.contributors.map(function(M){return function(C,L){return L>0?s({key:"user-"+L},", ",M.render_user(C)):M.render_user(C,"user-"+L)}}(this)):this.render_user(x)),this.props.show_rating&&l.rating?r.StarRating(l.rating):void 0,l.tags&&l.tags.length?o({className:"sub cell_tags"},l.tags.map(function(M){return function(C,L){return React2.createElement(React2.Fragment,{key:C.name},L>0?", ":void 0,i({href:C.url,target:M.props.target},"#"+C.name))}}(this))):void 0,this.props.show_short_text&&l.short_text?o({className:"sub short_text"},l.short_text):void 0,typeof(v=this.props).render_cell_below=="function"?v.render_cell_below(l):void 0)}}),GameGrid=r("GameGrid",{calculate_cell_sizes:function(f){return function(){var d,h;return(d=f.grid_sizer_ref)!=null&&(h=d.current)!=null?h.calculate_cell_sizes():void 0}}(this),componentDidMount:function(){var f;if(f=this.container(),this.props.enable_popups)return new I.GamePopups(f,{x_offset:10})},getDefaultProps:function(){return{cell_width:I.is_mobile()?300:250,enable_popups:!0,render_game:function(f){var d;return d={render_thumb_extras:this.props.render_thumb_extras,render_cell_extras:this.props.render_cell_extras,render_cell_below:this.props.render_cell_below,key:f.id,game:f,build_game_object:this.props.build_game_object,conversion_source:this.props.conversion_source,grid_referrer:this.props.grid_referrer},this.props.cell_props&&(d=$.extend(d,this.props.cell_props)),r.GameCell(d)}}},render:function(){var f,d;return f={ref:this.grid_sizer_ref||(this.grid_sizer_ref=React2.createRef()),cell_margin:10,expected_width:this.props.cell_width,fit_rows:this.props.fit_rows,children:(d=this.props.games)!=null?d.map(this.props.render_game.bind(this)):void 0},this.props.grid_sizer_props&&(f=$.extend(f,this.props.grid_sizer_props)),this.enclose({},GridSizer(f))}}),r("NewFromFollowers",{getInitialState:function(){return{games:this.props.games}},componentDidUpdate:function(f,d){var h,v;if(((h=d.games)!=null?h.length:void 0)!==((v=this.state.games)!=null?v.length:void 0))return $(window).trigger("i:reshape")},on_dismiss_games:function(f){var d,h,v,u,p,l,g;if(!((v=this.state)!=null&&v.loading)&&(h=(u=this.game_grid_ref)!=null&&(p=u.current)!=null&&(l=p.grid_sizer_ref)!=null?l.current:void 0,!!h&&(g=this.state.games.slice(0,h.get_num_visible()),!!g.length)))return this.setState({loading:!0}),$.post(this.props.dismiss_url,I.with_csrf({object_type:"game",object_id:function(){var w,j,N;for(N=[],w=0,j=g.length;w<j;w++)d=g[w],N.push(d.id);return N}().join(",")})).done(function(w){return function(j){return $.get(w.props.more_results_url).done(function(N){return w.setState({games:_.toArray(N.games),loading:!1})})}}(this))},render_cell_extras:function(f){return a({type:"button",className:"dismiss_btn",onClick:function(d){return function(){var h,v;return v=function(){var u,p,l,g;for(l=this.state.games,g=[],u=0,p=l.length;u<p;u++)h=l[u],h!==f&&g.push(h);return g}.call(d),d.setState({games:v}),$.post(d.props.dismiss_url,I.with_csrf({object_type:"game",object_id:f.id})).done(function(u){return d.fetch_more_if_necessary()})}}(this)},"Dismiss")},fetch_more_if_necessary:function(){var f,d,h,v;if(!this.state.loading&&!this.state.exhausted&&(f=(d=this.game_grid_ref)!=null&&(h=d.current)!=null&&(v=h.grid_sizer_ref)!=null?v.current:void 0,!!f&&f.get_max_num_visible()>f.get_num_visible()))return this.setState({loading:!0}),$.get(this.props.more_results_url).done(function(u){return function(p){return p=_.toArray(p!=null?p.games:void 0),u.setState({exhausted:p.length<f.get_max_num_visible(),games:p,loading:!1})}}(this))},render_dismiss_button:function(){var f;if(f=this.state.games,!!f.length)return a({className:"button outline",type:"button",onClick:this.on_dismiss_games},this.state.games.length>1?"Dismiss these":"Dismiss")},render:function(){var f;return this.state.games.length?this.enclose({component:"section",className:classNames2("game_section",{loading:(f=this.state)!=null?f.loading:void 0})},o({className:"header_split"},o({},o({className:"section_header"},c({},"New From Those You Follow")),b({className:"section_description"},"Projects from those you follow that you haven't seen yet.")),this.render_dismiss_button()),r.GameGrid({ref:this.game_grid_ref||(this.game_grid_ref=React2.createRef()),games:this.state.games,fit_rows:this.props.fit_rows,conversion_source:this.props.conversion_source,grid_referrer:this.props.grid_referrer,render_cell_extras:this.render_cell_extras})):null}})});var AfterDownloadLightbox=null;_react_ready(function(){var r,e,t,n,i,a,o,s,c,m,b,k,y,f,d;return e=ReactDOMFactories,t=e.a,f=e.span,i=e.div,s=e.h2,k=e.p,a=e.em,o=e.fragment,d=e.strong,b=e.label,n=e.button,m=e.img,y=e.section,c=e.h3,o=React.createElement.bind(null,React.Fragment),o.type=React.fragment,r=R.package("Ads"),R("ShareButtons",{pure:!0,render:function(){return this.enclose({className:"simple_social_buttons_widget"},this.props.twitter?t({href:this.props.twitter.url,target:"_blank",rel:"noopener nofollow",className:"twitter_link",title:"Share on Twitter..."},f({className:"icon icon-twitter"})):void 0,this.props.facebook?t({href:this.props.facebook.url,target:"_blank",rel:"noopener nofollow",className:"facebook_link",title:"Share on Facebook..."},f({className:"icon icon-facebook"})):void 0)}}),r("AfterDownload",{pure:!0,componentDidMount:function(){var h,v;return h=ReactDOM.findDOMNode(this),v=document.createElement("script"),v.async=!0,v.src="//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js",h.appendChild(v),(window.adsbygoogle||(window.adsbygoogle=[])).push({})},render:function(){return i({},React.createElement("ins",{className:"adsbygoogle",style:{display:"block"},"data-ad-client":"ca-pub-4267538250984114","data-ad-slot":"9114825859","data-ad-format":"auto","data-full-width-responsive":"true"}))}}),AfterDownloadLightbox=R("AfterDownloadLightbox",{componentDidMount:function(){var h,v;if(this.props.push_state)try{return v={title:document.title,url:window.location.toString()},typeof(h=window.history).replaceState=="function"&&h.replaceState({},this.props.push_state.title,this.props.push_state.url),this.previous_state=v}catch(u){}},componentWillUnmount:function(){var h;if(this.previous_state)return typeof(h=window.history).replaceState=="function"&&h.replaceState({},this.previous_state.title,this.previous_state.url),delete this.previous_state},render:function(){return R.Lightbox({className:this.enclosing_class_name()},s({},this.tt("game.after_download.thanks_for_downloading_title")),k({},this.tt("game.after_download.thanks_project_author",{author:this.props.user.name,project:a({},this.props.game.title)}),this.props.has_more_games?o({}," ",this.tt("game.after_download.more_from_creator",{profile:t({href:this.props.user.url},this.props.user.name)})):void 0," ",this.tt("game.after_download.check_popup_blocker")),this.props.show_ad?R.Ads.AfterDownload({}):void 0,this.props.show_app_banner&&this.props.app_url?k({},this.tt("game.after_download.app_learn_more",{s:function(h){return d({},h)},a:function(h){return function(v){return t({href:h.props.app_url,className:"forward_link","data-action":"download_app","data-label":"app_notice"},v)}}(this)})):void 0,i({className:"share_row"},this.props.user.follow_button?R.FollowButton(this.props.user.follow_button):void 0,this.props.share_buttons?f({className:"share_links"},f({className:"icon icon-heart"})," ",I.i18n.locale_is_english()?"Share this "+this.props.game.noun+":":this.tt("game.after_download.share_this_project"),R.ShareButtons(this.props.share_buttons)):void 0),this.render_jam_games()||this.render_rec_games(),i({className:"bandwidth_row"},this.tt("game.after_download.powered_by",{s:function(h){return f({className:"powered"},h)},itchio_logo:m({height:25,src:this.props.logo_url})})))},render_jam_games:function(){if(this.props.jam_games&&this.props.jam_games.length)return y({className:"related_games"},c({},this.tt("game.after_download.check_out_more_from_jam_title",{jam_title:t({href:this.props.related_jam.url,target:"_blank"},this.props.related_jam.title)})),this.render_game_grid(this.props.jam_games,{show_rating:!1}),this.props.related_jam.entries_url?i({className:"browse_footer"},t({href:this.props.related_jam.entries_url,className:"button outline"},this.tt("game.after_download.view_all_from_jam"))):void 0)},render_rec_games:function(){if(this.props.rec_games&&this.props.rec_games.length)return y({className:"related_games"},c({},this.props.rec_games_title||this.tt("game.after_download.more_from_itchio_header")),this.render_game_grid(this.props.rec_games),i({className:"browse_footer"},t({href:this.props.browse_url,className:"button outline"},this.tt("game.after_download.see_everything"))))},render_game_grid:function(h,v){return GameGrid({games:h,cell_width:200,fit_rows:3,enable_popups:!1,conversion_source:this.props.conversion_source,grid_referrer:this.props.grid_referrer,cell_props:$.extend({target:"_blank",show_rating:!0,show_cell_tools:!1},v)})}})});var CloseIcon=null;_react_ready(function(){var r,e;return r=ReactDOMFactories,e=r.img,CloseIcon=function(t){var n,i,a,o,s;return t==null&&(t={}),s=(i=t.width)!=null?i:24,n=(a=t.height)!=null?a:s,React2.createElement("svg",{className:"svgicon icon_close",strokeLinecap:"round",stroke:"currentColor",role:"img",version:"1.1",viewBox:"0 0 24 24",strokeWidth:(o=t.stroke_width)!=null?o:"2",width:s,height:n,strokeLinejoin:"round","aria-hidden":!0,fill:"none"},React2.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),React2.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))},CloseIcon=React2.createElement.bind(null,React2.memo(CloseIcon))});var Lightbox2=null,ErrorLightbox=null,open_lightbox=null;_react_ready(function(){var r,e,t,n,i,a,o,s;return r=ReactDOMFactories,e=r.a,i=r.div,t=r.button,o=r.label,a=r.h2,s=r.p,n=null,open_lightbox=function(c){return c.promise?(I.Lightbox.open_loading(),c.then(function(m){return function(b){return I.Lightbox.open(b)}}(this),function(m){return function(b){return I.Lightbox.open(ErrorLightbox({errors:b.errors}))}}(this))):I.Lightbox.open(c)},Lightbox2=R2.component("Lightbox",{is_modal_dialog:function(){var c;return c=document.getElementById("lightbox_container"),c!=null?c.contains(this.container_ref.current):void 0},getInitialState:function(){return{}},componentDidMount:function(){if(this.is_modal_dialog())return n?console.warn("A dialog already has the focus trap"):(this.detect_focus=function(c){return function(m){var b;if(b=c.container_ref.current,b&&"contains"in b&&!(b===m.target||b.contains(m.target)))return b.focus()}}(this),$(document.body).on("focusin",this.detect_focus),n=this),this.setState({previously_focused:document.activeElement,is_modal_dialog:!0},function(){return _.defer(function(c){return function(){var m;return(m=c.container_ref.current)!=null?m.focus():void 0}}(this))})},componentWillUnmount:function(){var c;return this.detect_focus&&($(document.body).off("focusin",this.detect_focus),n=null,delete this.detect_focus),(c=this.state.previously_focused)!=null?c.focus():void 0},close:function(){return I.Lightbox.close()},render:function(){return i({className:classNames("lightbox",this.props.className),style:this.props.style,role:this.state.is_modal_dialog?"dialog":void 0,"aria-modal":this.state.is_modal_dialog?"true":void 0,tabIndex:this.state.is_modal_dialog?-1:void 0,ref:this.container_ref||(this.container_ref=React.createRef())},this.props.close!==!1?t({className:"close_button",type:"button","aria-label":"Close Dialog"},CloseIcon({width:18})):void 0,this.props.children)}}),ErrorLightbox=R2.component("ErrorLightbox",{propTypes:{title:PropTypes.string,errors:PropTypes.array.isRequired},render:function(){return Lightbox2({className:classNames(this.enclosing_class_name(),"compact")},a({},this.props.title||this.tt("misc.lightboxes.error_title")),R2.Forms.FormErrors({title:!1,errors:this.props.errors}),s({className:"buttons"},t({className:"button",type:"button",onClick:function(c){return function(){return I.Lightbox.close()}}(this)},"Close")))}})});var start_download=function(r){return r.url?(_2.defer(function(e){return function(){var t,n;if(r.external){window.open(r.url);return}return t=window.location.protocol.match(/^https\b/i),n=t&&!r.url.match(/^https\b/i),n?window.location=r.url:I2.is_ios()||I2.coep_set?window.open(r.url):$('<iframe frameborder="0" style="width: 0; height: 0; visibility: hidden;"></iframe>').attr("src",r.url).appendTo(document.body)}}(this)),!0):!1},bind=function(r,e){return function(){return r.apply(e,arguments)}};I.ViewJamRateGame=function(){function r(e,t){var n;this.game=t,this.download_upload=bind(this.download_upload,this),this.setup_comments=bind(this.setup_comments,this),this.el=jquery_default(e),this.setup_downloads(),this.setup_header(),this.setup_comments(),this.el.format_timestamps(),n=this.el.find(".judge_feedback_input"),n.exists()&&redactor(n,{minHeight:100,source:!1}),this.el.dispatch("click",{report_btn:function(i){return function(a){return Lightbox.open_remote_react(a.data("lightbox_url"),function(o){return ReportLightbox(o)})}}(this),add_to_collection_btn:function(i){return function(a){return I.current_user?Lightbox.open_remote(a.attr("href"),CollectionLightbox):"continue"}}(this)})}return r.prototype.setup_comments=function(){var e,t;return t=this.el.find(".comment_input"),t.length&&redactor(t,{minHeight:100,source:!1}),e=this.el.find(".jam_comment_list"),e.dispatch("click",{delete_comment_btn:function(n){return function(i){return confirm("Are you sure you want to delete this comment?"),jquery_default.post(i.data("href"),I.with_csrf(),function(a){if(a.errors){alert(a.errors.join);return}return i.closest(".jam_comment").slideUp()})}}(this)})},r.prototype.setup_downloads=function(){var e,t,n,i;for(i=this.el.find(".file_size_value"),t=0,n=i.length;t<n;t++)e=i[t],e=jquery_default(e),e.html(I.format_bytes(parseInt(e.html())));return this.el.dispatch("click",{download_btn:function(a){return function(o){return a.download_upload(o.data("upload_id"))}}(this)})},r.prototype.download_upload=function(e,t){var n;return n=jquery_default.extend({after_download_lightbox:!0,as_props:"1",source:"jam_rate_game"},t),I.bypass_quarantine&&(n.bypass_quarantine=!0),jquery_default.ajax({url:"/game/download/"+this.game.id+"/file/"+e+"?"+jquery_default.param(n),type:"post",data:I.with_csrf(),success:function(i){return function(a){var o;if(a.errors){o=a.errors.join(", "),I.event("jam_game_download","error",o),R.ErrorLightbox?open_lightbox(R.ErrorLightbox({errors:a.errors})):alert(o);return}if(a.lightbox)switch(a.lightbox_type){case"QuarantineLightbox":open_lightbox(QuarantineLightbox(jquery_default.extend({on_force_download:function(){return i.download_upload(e,jquery_default.extend({},t,{bypass_quarantine:!0}))}},a.lightbox)));break;default:open_lightbox(AfterDownloadLightbox(a.lightbox))}if(start_download(a))return I.event("jam_game_download","download",""+i.game.id),ConversionTracker.download("1:"+i.game.id),ConversionTracker.flush_now()}}(this)})},r.prototype.setup_header=function(){return this.el.dispatch("click",{disqualify_submission_btn:function(e){return function(t){if(!t.is(".loading"))return jquery_default.post(t.data("href"),I.with_csrf({redirect:!0}),function(n){if(n.errors&&R.ErrorLightbox&&open_lightbox(R.ErrorLightbox({errors:n.errors})),n.redirect_to)return window.location=n.redirect_to})}}(this),remove_submission_btn:function(e){return function(t){if(!t.is(".loading")&&confirm("Are you sure you want to remove this submission?"))return t.addClass("loading"),jquery_default.post(t.data("href"),I.with_csrf({redirect:!0}),function(n){return window.location=n.redirect_to})}}(this)})},r}(),window.init_JamRateGame=function(r,e){new I.ViewJamRateGame(r,e)},window.init_Jams=function(r,e){new I.JamsPage(r)};var bind2=function(r,e){return function(){return r.apply(e,arguments)}};I.AddGameToJamForm=function(){function r(e,t){t==null&&(t={}),this.setup_badge_fields=bind2(this.setup_badge_fields,this),this.setup_public_domain_fields=bind2(this.setup_public_domain_fields,this),this.opts=$.extend({remote_submit:!1},t),this.el=$(e),this.form=this.el.is("form")&&this.el||this.el.find("form"),this.setup_remote_submit(),this.setup_public_domain_fields(),this.setup_badge_fields(),this.setup_game_picker()}return r.prototype.setup_game_picker=function(){var e,t,n;if(e=this.el.find(".game_id_input"),!!e.length)return n=_.template(this.form.attr("action")),t=function(i){return function(){var a;if(e.length)return a=n({game_id:e.val()}),i.form.attr("action",a)}}(this),t(),e.on("change",t)},r.prototype.setup_remote_submit=function(){if(this.opts.remote_submit)return $('<input type="hidden" name="json" value="true" />').appendTo(this.form),this.form.remote_submit(function(e){return function(t){var n;return t.errors?alert(t.errors.join(", ")):typeof(n=e.opts).on_submit=="function"?n.on_submit(t):void 0}}(this))},r.prototype.setup_public_domain_fields=function(){var e,t,n,i,a,o;for(this.el.on("change",".field_type_public_domain",function(s){return function(c){var m,b;return m=$(c.currentTarget),b=m.find(".field_value"),b.val(m.find(".public_domain_toggle").is(":checked")?JSON.stringify([m.find(".country_input").val()]):"")}}(this)),a=this.el.find(".field_type_public_domain"),n=0,i=a.length;n<i;n++){t=a[n],t=$(t),o=t.find(".field_value").val();try{e=JSON.parse(o)}catch(s){}if(!e)return;t.find(".public_domain_toggle").prop("checked",!0),t.find("option[value='"+e[0]+"']").prop("selected",!0)}},r.prototype.setup_badge_fields=function(){var e,t,n,i,a,o,s,c,m,b;for(this.el.on("change",".field_type_badges",function(k){return function(y){var f,d,h,v;return d=$(y.currentTarget),v=d.find(".field_value"),h=function(){var u,p,l,g;for(l=d.find("input.badge_toggle:checked"),g=[],u=0,p=l.length;u<p;u++)f=l[u],g.push(""+$(f).data("choice_idx"));return g}(),v.val(h.length?JSON.stringify(h):"")}}(this)),c=this.el.find(".field_type_badges"),n=0,o=c.length;n<o;n++){t=c[n],t=$(t),b=t.find(".field_value").val();try{e=JSON.parse(b)}catch(k){}if(!e)return;for(a=0,s=e.length;a<s;a++)i=e[a],m=t.find("[data-choice_idx='"+i+"']"),m.prop("checked",!0)}},r}(),I.JamAdmin=function(){function r(e,t){var n;this.jam=t,this.el=$(e),n=this.el.find(".appearance_editor_widget"),n.length&&(this.appearance_editor=new I.JamAppearanceEditor(n,{},this.jam)),this.el.dispatch("click",{edit_theme_btn:function(i){return function(a){return i.appearance_editor.toggle()}}(this)})}return r}();var UNKNOWN_IMAGE_FORMAT_ERROR,image_type_from_array_buffer,bind3=function(r,e){return function(){return r.apply(e,arguments)}},extend3=function(r,e){for(var t in e)hasProp3.call(e,t)&&(r[t]=e[t]);function n(){this.constructor=r}return n.prototype=e.prototype,r.prototype=new n,r.__super__=e.prototype,r},hasProp3={}.hasOwnProperty,catch_errors=function(r){var e,t;return t=function(n){return n.errors?$.Deferred().reject(n):n},e=function(n){return $.Deferred().reject({errors:["Server error ("+n.status+")","Please contact support if the error persists"]})},r.then(t,e)};I.prepare_upload=function(r,e){return $.when(e).then(function(t){return catch_errors($.ajax({url:r+"/upload/prepare",type:"post",data:I.with_csrf(t),dataType:"json"}))})},image_type_from_array_buffer=function(r){var e,t,n,i,a;return e=new DataView(r,0,5),n=e.getUint8(0,!0),i=e.getUint8(1,!0),t=n.toString(16)+i.toString(16),a={8950:"image/png",4749:"image/gif","424d":"image/bmp",ffd8:"image/jpeg"},a[t]},UNKNOWN_IMAGE_FORMAT_ERROR="You selected an image type we don't recognize. It's possible it has the wrong file extension for the format it is saved as. Please use an image editing program to convert it to a PNG, JPEG, or GIF.",I.test_image_format=function(r){return $.Deferred(function(e){return function(t){var n;return window.FileReader?(n=new FileReader,n.readAsArrayBuffer(r),n.onerror=function(){return t.reject("Failed to read image from disk")},n.onload=function(){var i;return i=image_type_from_array_buffer(n.result),i==="image/bmp"?t.reject("You selected a BMP file that has a wrong extension. Please use an image editing program to convert it to a PNG, JPEG, or GIF."):i?t.resolve():t.reject(UNKNOWN_IMAGE_FORMAT_ERROR)}):t.resolve()}}(this))},I.image_dimensions=function(r){return $.Deferred(function(e){var t,n;return n=typeof URL!="undefined"&&URL!==null&&typeof URL.createObjectURL=="function"?URL.createObjectURL(r):void 0,n?(t=new Image,t.src=n,t.onload=function(){return e.resolve([t.width,t.height])},t.onerror=function(){return e.reject(UNKNOWN_IMAGE_FORMAT_ERROR)}):e.reject(UNKNOWN_IMAGE_FORMAT_ERROR)})},I.video_dimensions=function(r){return $.Deferred(function(e){var t,n;return n=document.createElement("video"),t=typeof URL!="undefined"&&URL!==null&&typeof URL.createObjectURL=="function"?URL.createObjectURL(r):void 0,t?(n.src=t,n.onloadedmetadata=function(){return e.resolve([n.videoWidth,n.videoHeight])},n.onerror=function(){return e.reject("Invalid video file")}):e.reject("Invalid video file")})},I.test_image_dimensions=function(r,e,t){return e==null&&(e=3840),t==null&&(t=2160),I.image_dimensions(r).then(function(n){return function(i){var a,o;return o=i[0],a=i[1],$.Deferred(function(s){return o>e||a>t?s.reject("Image is greater than the maximum dimensions of "+e+"x"+t+" (you selected a "+o+"x"+a+" image)"):s.resolve()})}}(this))};var Upload=I.Upload||(I.Upload=function(){r.prototype.kind="upload";function r(e,t){this.file=e,this.opts=t!=null?t:{},this.save_upload=bind3(this.save_upload,this),this.decrement=bind3(this.decrement,this),this.increment=bind3(this.increment,this)}return r.prototype.upload_params=function(){return{kind:this.kind,filename:this.file.name}},r.prototype.progress=function(e,t){},r.prototype.save_params=function(){return{}},r.prototype.save_url=function(){return this.upload_data.success_url},r.prototype.start_upload=function(){if(this.upload_data){console.warn("Attempt to start_upload when upload has already been started");return}return this.prepare_upload().then(function(e){return function(){return e.start_xhr_upload()}}(this)).then(function(e){return function(){return e.save_upload()}}(this))},r.prototype.increment=function(){var e,t;if((e=this.constructor).active_uploads||(e.active_uploads=0),this.constructor.active_uploads++,this.constructor.active_uploads===1)return typeof(t=this.opts).start_uploading=="function"?t.start_uploading():void 0},r.prototype.decrement=function(){var e;if(this.constructor.active_uploads--,this.constructor.active_uploads===0)return typeof(e=this.opts).stop_uploading=="function"?e.stop_uploading():void 0},r.prototype.prepare_upload=function(){return $.when(this.opts.upload_prefix||this.upload_prefix).then(function(e){return function(t){return I.prepare_upload(t,e.upload_params()).done(function(n){e.upload_data=n})}}(this))},r.prototype.start_xhr_upload=function(){var e,t,n,i,a,o;if(!this.upload_data)throw"missing upload data";this.increment(),e=$.Deferred().always(function(s){return function(){return s.decrement()}}(this)),t=new FormData,i=this.upload_data.post_params;for(n in i)a=i[n],t.append(n,a);return t.append("file",this.file),o=new XMLHttpRequest,o.upload.addEventListener("progress",function(s){return function(c){if(c.lengthComputable)return s.progress(c.loaded,c.total)}}(this)),o.upload.addEventListener("error",function(s){return function(c){return I.event("upload","xhr error",s.kind)}}(this)),o.upload.addEventListener("abort",function(s){return function(c){return I.event("upload","xhr abort",s.kind)}}(this)),o.addEventListener("readystatechange",function(s){return function(c){var m;if(o.readyState===4){if(Math.floor(o.status/100)===2)return I.event("upload","save",s.kind),e.resolve();if(m="Failed upload.",o.responseXML)try{m=o.responseXML.querySelector("Error Message").innerHTML}catch(b){c=b}else m=o.responseText;return I.event("upload_error","server error "+o.status+": "+m,s.kind),e.reject({errors:[m]})}}}(this)),o.open("POST",this.upload_data.action),o.send(t),e},r.prototype.save_upload=function(){if(!this.upload_data){console.warn("attempted to call save_upload without upload_data");return}return catch_errors($.ajax({url:this.save_url(),data:I.with_csrf(this.save_params()),dataType:"json",type:"post"}))},r}()),ImageUpload=I.ImageUpload=function(r){extend3(e,r);function e(){return e.__super__.constructor.apply(this,arguments)}return e.prototype.kind="image",e.prototype.upload_params=function(){return $.when(e.__super__.upload_params.call(this)).then(function(t){return function(n){return I.image_dimensions(t.file).then(function(i){var a,o;return o=i[0],a=i[1],Object.assign({width:o,height:a},n)},function(){return n})}}(this))},e}(I.Upload),MockUpload=function(r){extend3(e,r),e.prototype.stage_delay=500,e.prototype.mock_prepare_result={id:123},e.prototype.mock_save_result={success:!0};function e(){this.save_upload=bind3(this.save_upload,this),this.start_xhr_upload=bind3(this.start_xhr_upload,this),this.prepare_upload=bind3(this.prepare_upload,this),e.__super__.constructor.apply(this,arguments),console.warn("MockUpload:new "+this.file.name)}return e.prototype.prepare_upload=function(){return console.warn("MockUpload: prepare_upload"),$.when(this.opts.upload_prefix()).then(function(t){return function(){return $.Deferred(function(n){return setTimeout(function(){return t.upload_data=t.mock_prepare_result,n.resolve(t.upload_data)},t.stage_delay)})}}(this))},e.prototype.start_xhr_upload=function(){var t,n,i,a,o,s;return console.warn("MockUpload: start_xhr_upload"),t=20,i=(a=this.file.size)!=null?a:1234,n=0,s=0,o=this.stage_delay/t*5,$.Deferred(function(c){return function(m){var b;return c.progress(0,i),b=function(){return setTimeout(function(){return s+=1,c.progress(Math.floor(s/t*i),i),s===t?m.resolve():b()},o)},b()}}(this))},e.prototype.save_upload=function(){return console.warn("MockUpload: save_upload"),$.Deferred(function(t){return function(n){return setTimeout(function(){return n.resolve(t.mock_save_result)},t.stage_delay)}}(this))},e}(I.Upload);I.pick_files=function(r){var e;return r==null&&(r={}),e=r.input?$(r.input):($("input.pick_files_input").remove(),$("<input type='file' class='pick_files_input' />").hide().insertAfter("body")),$.Deferred(function(t){return function(n){return r.multiple&&e.attr("multiple",!0),r.accept&&e.attr("accept",r.accept),e.on("change",function(i){var a,o,s,c,m,b,k;for(m=function(){var y,f,d,h;for(d=i.target.files,h=[],y=0,f=d.length;y<f;y++)s=d[y],h.push(s);return h}(),o=[],typeof r.on_pick_files=="function"&&r.on_pick_files(m),b=0,k=m.length;b<k;b++)c=m[b],r.max_size&&c.size>r.max_size&&o.push(["Image is greater than the max file size "+I.format_bytes(r.max_size)+" (you selected a "+I.format_bytes(c.size)+" file)",c]);if(o.length){n.reject(o,m);return}return r.test_file?(a=m.map(function(y){return $.Deferred(function(f){return r.test_file(y,f),setTimeout(function(){if(n.state()==="pending")return f.reject("Timed out checking file, are you sure it was an image file?")},1e3)}).catch(function(f){throw[f,y]})}),$.when.apply($,a).done(function(y){return n.resolve(m)}).fail(function(y){return n.reject([y])})):n.resolve(m)}),e.click()}}(this))},I.xhr_upload=function(r,e){return $.Deferred(function(t){return function(n){var i,a,o,s,c;i=new FormData,o=e.post_params;for(a in o)s=o[a],i.append(a,s);return i.append("file",r),c=new XMLHttpRequest,c.upload.addEventListener("progress",function(m){if(m.lengthComputable)return n.notify("progress",m.loaded,m.total)}),c.upload.addEventListener("error",function(m){return n.reject("xhr error")}),c.upload.addEventListener("abort",function(m){return n.reject("xhr aborted")}),c.addEventListener("readystatechange",function(m){var b;if(c.readyState===4){if(Math.floor(c.status/100)===2)return n.resolve();if(b="Failed upload.",c.responseXML)try{b=c.responseXML.querySelector("Error Message").innerHTML}catch(k){m=k}else b=c.responseText;return n.reject(b)}}),c.open("POST",e.action),c.send(i)}}(this))},I.upload_image=function(r){return r==null&&(r={}),r.accept||(r.accept="image/png,image/jpeg,image/gif"),r.file_params=function(e){return e.type==="video/mp4"?I.video_dimensions(e).then(function(t){return function(n){var i,a;return a=n[0],i=n[1],{width:a,height:i}}}(this)):I.image_dimensions(e).then(function(t){return function(n){var i,a;return a=n[0],i=n[1],{width:a,height:i}}}(this))},I.upload_file(r)};var prepare_and_upload_file=function(r,e){var t,n,i;return e==null&&(e={}),n=e.on_start_upload,i=e.test_file,t=e.file_params?e.file_params(r):{},$.when(t).then(function(a){return function(o){return $.ajax({url:e.url,type:"post",xhrFields:{withCredentials:!0},data:I.with_csrf(Object.assign({filename:r.name,thumb_size:e.thumb_size,action:"prepare"},o))}).then(function(s){var c;return c=I.xhr_upload(r,s).then(function(){return $.ajax({url:s.success_url,type:"post",xhrFields:{withCredentials:!0},data:I.with_csrf()})}),typeof n=="function"&&n(r,s,c),c})}}(this))};I.upload_file=function(r){var e,t,n,i,a;if(r==null&&(r={}),!r.url)throw new Error("missing url for upload image");return e=r.accept,t=r.max_size,n=r.multiple,i=r.on_pick_files,a=r.test_file,I.pick_files({accept:e,max_size:t,multiple:n,test_file:a,on_pick_files:i}).then(function(o){return function(s){var c,m;if(!n&&s.length>1)throw"Got multiple files for single upload";return m=function(){var b,k,y,f;for(y=s.slice(0,6),f=[],b=0,k=y.length;b<k;b++)c=y[b],f.push(prepare_and_upload_file(c,r));return f}(),n?$.when.apply($,m):m[0]}}(this))},I.make_upload_button=function(r,e){var t,n;return t=null,n=r.data("max_size"),r.on("click",function(i){return function(a){var o;return t&&t.remove(),t=$("<input type='file' multiple />").hide().insertAfter(r),(o=r.data("accept"))&&t.attr("accept",o),t.on("change",function(){var s,c,m,b,k;for(b=t[0].files,k=[],c=0,m=b.length;c<m;c++){if(s=b[c],n!=null&&s.size>n){I.flash(s.name+" is greater than max size of "+I.format_bytes(n),"error");continue}k.push(typeof e=="function"?e(s):void 0)}return k}),t.insertAfter(r),t.click()}}(this))};var ImagePicker,LayoutImageUpload,extend4=function(r,e){for(var t in e)hasProp4.call(e,t)&&(r[t]=e[t]);function n(){this.constructor=r}return n.prototype=e.prototype,r.prototype=new n,r.__super__=e.prototype,r},hasProp4={}.hasOwnProperty,bind4=function(r,e){return function(){return r.apply(e,arguments)}};LayoutImageUpload=function(r){extend4(e,r);function e(){return e.__super__.constructor.apply(this,arguments)}return e.prototype.upload_params=function(){return e.__super__.upload_params.call(this).then(function(t){return function(n){return n.type="layout",n}}(this))},e.prototype.save_params=function(){return{thumb_size:"original"}},e}(ImageUpload),ImagePicker=function(){function r(e,t,n){this.editor=t,n==null&&(n=null),n&&(this.update=n),this.el=jquery_default(e),this.make_upload_btn(),this.el.dispatch("click",{remove_image_btn:function(i){return function(a){return i.el.removeClass("has_image").find(".image_preview").css("background-image","").end().find(".image_id_input").val("").end().data("image_url",""),i.send_update()}}(this)}),this.el.on("change",function(i){return function(a){return i.send_update()}}(this)),this.send_update()}return r.prototype.upload_prefix=function(){return this.editor.upload_prefix()},r.prototype.prepare_upload=function(){return jquery_default.Deferred().resolve()},r.prototype.update=function(){},r.prototype.send_update=function(){var e,t,n,i,a,o,s;if(n=this.el.data("image_name"),s=this.el.data("image_url"),!s)return this.update(n,void 0);for(a={url:s},o=this.el.find(".image_option"),e=0,t=o.length;e<t;e++)i=o[e],i=jquery_default(i),a[i.data("name")]=i.find("select").val();return this.update(n,a)},r.prototype.make_upload_btn=function(){if(!this.upload_btn)return this.progress_inner||(this.progress_inner=this.el.find(".progress_inner")),this.upload_btn=this.el.find(".upload_image_btn").on("click",function(e){return function(){return I2.pick_files({accept:"image/png,image/jpeg,image/gif",test_file:function(t,n){return jquery_default.when(I2.test_image_dimensions(t),I2.test_image_format(t)).done(function(i){return function(){return n.resolve()}}(this)).fail(function(i){return function(a){return n.reject(a)}}(this))},max_size:e.upload_btn.data("max_size")}).fail(function(t){var n,i,a;return a=t[0],n=a[0],i=a[1],open_lightbox(ErrorLightbox({errors:[i.name+": "+n]}))}).then(function(t){var n,i;return n=t[0],i=new LayoutImageUpload(n,{upload_prefix:e.upload_prefix()}),e.progress_inner.css({width:""}),e.el.addClass("uploading"),i.start_upload().fail(function(a){return e.el.removeClass("uploading"),open_lightbox(ErrorLightbox({errors:a.errors}))}).done(function(a){return e.el.removeClass("uploading").addClass("has_image").find(".image_preview").css("background-image","url("+a.url+")").end().find(".image_id_input").val(i.upload_data.upload_id).end().data("image_url",a.url),e.send_update()})})}}(this))},r}();var AppearanceEditor2=I2.AppearanceEditor=function(){AppearanceEditor.ImagePicker=ImagePicker;function AppearanceEditor(r,e){this.data=e,this.set_height=bind4(this.set_height,this),this.css_template=bind4(this.css_template,this),this.el=jquery_default(r),this.style=_2.extend({},this.data),this.refresh_style=_2.debounce(function(t){return function(){return t.constructor.prototype.refresh_style.apply(t,arguments)}}(this),0),this.setup_color_picker(),this.setup_image_picker(),this.setup_text_areas(),this.setup_form(),this.el.dispatch("click",{toggle_editor_btn:function(t){return function(){return t.toggle()}}(this)}),window.location.hash.match(/\bedit\b/)&&_2.defer(function(t){return function(){return t.toggle()}}(this)),jquery_default(window).on("resize",_2.debounce(function(t){return function(){return t.set_height()}}(this),100))}return AppearanceEditor.prototype.css_template=function(){var template_code;if(!this.css_template_fn){if(template_code=this.el.data("css_template"),!template_code)return;this.css_template_fn=eval(template_code)}return this.css_template_fn.apply(this,arguments)},AppearanceEditor.prototype.set_height=function(){var r,e,t,n,i;return e=this.el.find(".appearance_editor_inner"),r=jquery_default("html").outerHeight(!0),i=jquery_default(window).height(),t=e.outerHeight(!0),t>i?(n=Math.max(r,i,t),this.el.height(n).css({bottom:"",position:""})):this.el.height("").css({bottom:"0",position:"fixed"})},AppearanceEditor.prototype.toggle=function(){var r;return r=jquery_default(document.body).toggleClass("show_appearance_editor"),this.set_height(),this.el.addClass("animating"),setTimeout(function(e){return function(){return e.el.removeClass("animating"),e.set_height()}}(this),200)},AppearanceEditor.prototype.refresh_style=function(){},AppearanceEditor.prototype.upload_prefix=function(){throw"override me"},AppearanceEditor.prototype.update_image=function(r,e){return this.style[r]=e,this.refresh_style()},AppearanceEditor.prototype.update_color=function(r,e){return this.style[r]=e,this.refresh_style()},AppearanceEditor.prototype.setup_form=function(){return this.el.find("form").remote_submit(function(r){return function(e){e.errors&&alert(e.errors.join(", "))}}(this))},AppearanceEditor.prototype.setup_text_areas=function(){return this.el.on("focusin focusout",".text_field",function(r){return function(e){return jquery_default(e.currentTarget).toggleClass("open",e.type==="focusin")}}(this))},AppearanceEditor.prototype.setup_image_picker=function(){var r,e;return e=this.el.find(".image_picker"),this.image_pickers=function(){var t,n,i;for(i=[],t=0,n=e.length;t<n;t++)r=e[t],i.push(new ImagePicker(jquery_default(r),this,function(a){return function(){return a.update_image.apply(a,arguments)}}(this)));return i}.call(this)},AppearanceEditor.prototype.setup_color_picker=function(){var r,e,t,n;for(this.el.dispatch("click",{color_preview:function(i){return function(a){return i.open_color_picker(a.closest(".color_picker"))}}(this)}),this.el.on("click",".color_picker .close_btn",function(i){return function(a){var o;return o=jquery_default(a.target).closest(".color_picker"),o.data("close")()}}(this)),this.el.find("input[type='text']").on("focus",function(i){return function(a){return i.open_color_picker(jquery_default(a.target).closest(".color_picker"),!1)}}(this)),n=this.el.find(".color_picker"),r=0,e=n.length;r<e;r++)t=n[r],t=jquery_default(t),this.set_picker_color(t,this.style[t.find("[data-name]").data("name")]);return n.on("keyup","input",function(i){return function(a){var o,s;return o=jquery_default(a.currentTarget).val(),(s=jquery_default(a.delegateTarget).data("farb"))!=null?s.setColor(o):void 0}}(this)),n.on("change","input",function(i){return function(a){var o,s;if(t=jquery_default(a.delegateTarget),o=(s=t.data("farb"))!=null?s.color:void 0)return t.find("input").val(o)}}(this))},AppearanceEditor.prototype.set_picker_color=function(r,e){var t,n,i,a,o,s,c;return t=r.data("farb")||new jquery_default._farbtastic(jquery_default()),i=r.find("input"),e?i.val(e):e=i.val(),o=r.find(".color_preview"),i=r.find("input"),s=t.RGBToHSL(t.unpack(e)),n=s[0],c=s[1],a=s[2],o.toggleClass("is_bright",a>.9),o.css("backgroundColor",e),this.update_color(r.find("[data-name]").data("name"),e)},AppearanceEditor.prototype.open_color_picker=function(r,e){var t,n,i,a,o,s,c,m,b;if(e==null&&(e=!0),r.is(".flyout_open"))return e&&r.data("close")(),!1;for(b=this.el.find(".color_picker"),i=0,o=b.length;i<o;i++)s=b[i],typeof(t=jquery_default(s).data("close"))=="function"&&t();return c=jquery_default(`<div class="color_popup input_flyout">
  <div class="close_btn">&times;</div>
  <div class="farb"></div>
</div>`),a=r.find("input"),m=r.find(".color_preview"),r.data("close",function(k){return function(){return c.removeClass("open"),r.removeClass("flyout_open").removeData("farb"),jquery_default(document.body).off("click.color_picker"),jquery_default(window).off("keydown.color_picker"),setTimeout(function(){return c.remove()},200)}}(this)),n=jquery_default.farbtastic(c.find(".farb"),function(k){return function(y){return k.set_picker_color(r,y)}}(this)),r.data("farb",n),n.setColor(a.val()),jquery_default(document.body).on("click.color_picker",function(k){return function(y){if(!jquery_default(y.target).closest(".color_picker").length)return r.data("close")()}}(this)),jquery_default(window).on("keydown.color_picker",function(k){return function(y){if(y.keyCode===27)return r.data("close")()}}(this)),r.append(c).addClass("flyout_open"),_2.defer(function(k){return function(){return c.addClass("open")}}(this))},AppearanceEditor}(),bind5=function(r,e){return function(){return r.apply(e,arguments)}},extend5=function(r,e){for(var t in e)hasProp5.call(e,t)&&(r[t]=e[t]);function n(){this.constructor=r}return n.prototype=e.prototype,r.prototype=new n,r.__super__=e.prototype,r},hasProp5={}.hasOwnProperty;I.JamAppearanceEditor=function(r){extend5(e,r);function e(t,n,i){this.jam=i,this.refresh_style=bind5(this.refresh_style,this),e.__super__.constructor.call(this,t,n),this.page=$(".view_jam_page"),this.css_input=this.el.find(".css_input").on("change",function(a){return function(){return a.refresh_style()}}(this)).on("keyup",_.debounce(function(a){return function(){return a.refresh_style()}}(this),300))}return e.prototype.upload_prefix=function(){return"/jam/"+this.jam.id},e.prototype.refresh_style=function(){var t,n,i,a,o,s,c,m,b,k,y,f,d,h,v,u,p,l;return n=this.style.banner_image,this.page.toggleClass("has_banner",!!n),n&&(this._banner_el||(this._banner_el=this.page.find(".jam_banner")),this._banner_el.attr("src",n.url).toggleClass("full_width",n.size==="full")),this._style||(this._style=$("#jam_theme")),f=this.style.bg_color2,a=Color.sub_color2(f,.1),i=Color.sub_color2(a,.05),p=Color.sub_color2(f,.6),l=Color.sub_color2(f,.45),d=Color.sub_color2(this.style.link_color,.05),h=Color.sub_color2(this.style.link_color,-.05),c=Color.readable_text_color(this.style.link_color),s=this.style.link_color,s==="#dd4a4a"&&(s="#FA5C5C"),m=Color.sub_color2(s,.17,.2),t=(y=this.style.background_image)?(v=function(){var g;switch((g=this.style.background_image)!=null?g.position:void 0){case"align_left":return"0% 0%";case"align_right":return"100% 0%";case"align_center":return"50% 0%";default:return"0% 0%"}}.call(this),'background-image: url("'+y.url+`");
background-repeat: `+(y.repeat||"repeat")+`;
background-position: `+v+";"):"",k=I.strip_css(this.css_input.val()||""),o=this.style.bg_color===this.style.bg_color2&&!this.style.background_image?a:"transparent",b=this.css_template({text_color:this.style.text_color,bg_color:this.style.bg_color,bg_color2:this.style.bg_color2,link_color:this.style.link_color,body_border_color:o,button_shadow:m,background_image_css:t,button_fg_color:c,button_bg_color:s,link_highlight:d,link_sub:h,bg_sub:a,bg_highlight:i,text_sub_1:p,text_sub_2:l,custom_css:k,mix_color:Color.mix_color}),(u=this._style)!=null&&u.remove(),this._style=$("<style type='text/css'>"+b+"</style>").appendTo($("head"))},e}(I.AppearanceEditor),I.JamsPage=function(){function r(e,t){this.el=$(e),this.el.format_timestamps(),I.libs.react.then(function(n){return function(){return ReactDOM.render(R.Jam.JamCalendar({jams:t}),n.el.find("#calendar .jam_calendar")[0])}}(this))}return r}();var setup_grid_referrers=function(r,e){var t;return t=function(n){var i,a,o,s;if(n.closest(".game_author").length||(i=n.closest("[data-game_id]").data("game_id"))&&ReferrerTracker.push("game",i,e(n)),!n.closest(".sale_author").length&&(a=n.closest("[data-sale_id]"),o=a.data("sale_id")))return s=a.data("sale_type"),ReferrerTracker.push(s,o,e(n))},r.on("mouseup",".game_cell a, .sale_row a, .leader_game a",function(n){if(!(n.which>3))return t(jquery_default(n.currentTarget))}),r.on("i:track_link",function(n){return t(jquery_default(n.target))})},extend6=function(r,e){for(var t in e)hasProp6.call(e,t)&&(r[t]=e[t]);function n(){this.constructor=r}return n.prototype=e.prototype,r.prototype=new n,r.__super__=e.prototype,r},hasProp6={}.hasOwnProperty,bind6=function(r,e){return function(){return r.apply(e,arguments)}};I.AddGameLightbox=function(r){extend6(e,r);function e(){return e.__super__.constructor.apply(this,arguments)}return e.prototype.init=function(t){var n;if(this.el.toggleClass("intent_join",t==="join").toggleClass("intent_submit",t==="submit"),n=this.el.find("form"),!!n.length)return new I.AddGameToJamForm(n,{remote_submit:!0,on_submit:function(i){return function(){return i.el.addClass("submitted"),i.el.focus()}}(this)})},e}(I.Lightbox),I.JamBase=function(){function r(){this.setup_buttons=bind6(this.setup_buttons,this)}return r.prototype.setup_buttons=function(){return this.el.remote_link(function(e){if(e.redirect_to)return window.location=e.redirect_to}),this.el.dispatch("click",{join_jam_btn:function(e){return function(t){return I.current_user||I.Lightbox.open_tpl("add_game_to_jam_lightbox",I.AddGameLightbox,"join"),ConversionTracker.join("4:"+e.jam.id),"continue"}}(this),add_game_btn:function(e){return function(t){return I.Lightbox.open_tpl("add_game_to_jam_lightbox",I.AddGameLightbox,"submit")}}(this),unsubmit_btn:function(e){return function(t){var n;return confirm("You are about to unsubmit your game. If the jam has closed submissions you will not be able to re-submit. Are you sure?")?(n=t.attr("href"),jquery_default.post(n,I.with_csrf({redirect:!0}),function(i){return window.location=i.redirect_to})):!1}}(this)})},r}(),I.ViewJam=function(r){extend6(e,r);function e(t,n){var i;this.jam=n,this.setup_filters=bind6(this.setup_filters,this),this.setup_grid=bind6(this.setup_grid,this),this.setup_countdown=bind6(this.setup_countdown,this),this.setup_admin=bind6(this.setup_admin,this),e.__super__.constructor.call(this),this.el=jquery_default(t),this.setup_buttons(),this.setup_grid(),this.setup_countdown(),this.setup_admin(),this.setup_filters(),this.jam.queue_url&&(i=window.parent)!=null&&i.postMessage({page_url:window.location.href,queue_url:this.jam.queue_url},"*"),this.el.format_timestamps(),setup_grid_referrers(this.el,function(a){return function(){return"jam:"+a.jam.id}}(this)),I.deferred_links(this.el,"a[data-label]")}return e.prototype.setup_admin=function(){return new I.JamAdmin(jquery_default(document.body),this.jam)},e.prototype.setup_countdown=function(){var t,n,i,a,o,s;if(n=this.el.find(".countdown"),!!n.length&&(n.is(":empty")&&n.html(this.el.find(".jam_content").data("countdown_template")),o=parse_timestamp(this.jam.start_date),i=parse_timestamp(this.jam.end_date),this.jam.voting_end_date&&(s=parse_timestamp(this.jam.voting_end_date)),a=new Date,t=a<o?o:a<i?i:s&&a<s?s:void 0,t))return new I.Countdown(n,t)},e.prototype.setup_grid=function(){var t;if(t=this.el.find(".browse_game_grid"),t.exists())return this.grid=new I.GameGrid(t),this.grid.setup_conversion_tracking("8")},e.prototype.setup_filters=function(){var t,n,i,a;if(n=this.el.find(".jam_filter_picker"),!!n.length)return a=n.closest(".grid_outer"),i=a.find(".browse_game_grid"),t=i.find(".game_cell"),n.dispatch("click",{filter_item:function(o){return function(s){var c,m;return s.parent().find(".filter_item").removeClass("active"),s.addClass("active"),c=s.data("filter"),m=c?t.hide().filter("."+c).show():t.show(),o.grid.refresh_images(),a.toggleClass("no_visible_games",!m.length)}}(this)})},e}(I.JamBase),I.ViewRawJam=function(r){extend6(e,r);function e(){e.__super__.constructor.apply(this,arguments),this.setup_status_message()}return e.prototype.setup_status_message=function(){var t;if(t=this.el.find(".jam_status"),!!t.length)return t.html(this.jam.status_html),t.format_timestamps()},e}(I.ViewJam);var bind7=function(r,e){return function(){return r.apply(e,arguments)}};I.ViewJamPlain=function(){function r(e,t){this.jam=t,this.setup_grid=bind7(this.setup_grid,this),this.setup_countdown=bind7(this.setup_countdown,this),this.el=$(e),this.setup_countdown(),this.setup_grid(),I.has_follow_button(this.el),this.el.format_timestamps(),this.el.remote_link(function(n){if(n.redirect_to)return window.location=n.redirect_to}),this.el.dispatch("click",{join_jam_btn:function(n){return function(i){if(!I.current_user){I.Lightbox.open_tpl("add_game_to_jam_lightbox",I.AddGameLightbox,"join");return}return"continue"}}(this),add_game_btn:function(n){return function(i){return I.Lightbox.open_tpl("add_game_to_jam_lightbox",I.AddGameLightbox,"submit")}}(this)})}return r.prototype.setup_countdown=function(){var e,t,n,i,a,o;if(t=this.el.find(".countdown"),!!t.length&&(a=parse_timestamp(this.jam.start_date),n=parse_timestamp(this.jam.end_date),this.jam.voting_end_date&&(o=parse_timestamp(this.jam.voting_end_date)),i=new Date,e=i<a?a:i<n?n:o&&i<o?o:void 0,e))return new I.Countdown(t,e,{max_blocks:3})},r.prototype.setup_grid=function(){var e;if(e=this.el.find(".browse_game_grid"),e.exists())return this.grid=new I.GameGrid(e)},r}(),I.ViewJamResults=function(){function r(e){this.el=$(e),this.el.lazy_images(),this.el.format_timestamps()}return r}();var extend7=function(r,e){for(var t in e)hasProp7.call(e,t)&&(r[t]=e[t]);function n(){this.constructor=r}return n.prototype=e.prototype,r.prototype=new n,r.__super__=e.prototype,r},hasProp7={}.hasOwnProperty,InfiniteScroll=function(){r.prototype.loading_element=".grid_loader";function r(e,t){this.opts=t!=null?t:{},this.el=jquery_default(e),this.setup_loading()}return r.prototype.get_next_page=function(){return alert("override me")},r.prototype.setup_loading=function(){var e,t;if(!this.opts.disable_infinite_scroll&&(this.loading_row=this.el.find(this.loading_element),!!this.loading_row.length))return t=jquery_default(window),e=function(n){return function(){if(!n.el.is(".loading")&&t.scrollTop()+t.height()>=n.loading_row.offset().top)return n.get_next_page()}}(this),t.on("scroll.browse_loader",e),e()},r.prototype.remove_loader=function(){return jquery_default(window).off("scroll.browse_loader"),this.loading_row.remove()},r}(),InfiniteGameGrid=function(r){extend7(e,r);function e(){return e.__super__.constructor.apply(this,arguments)}return e.prototype.method="post",e.prototype.current_page=1,e.prototype.get_next_page=function(){return this.current_page+=1,this.el.addClass("loading"),jquery_default[this.method]("",{page:this.current_page,format:"json"}).fail(function(t){return function(n){return t.el.removeClass("loading"),t.remove_loader()}}(this)).done(function(t){return function(n){return t.el.removeClass("loading"),n.num_items>0?t.grid.add_image_loading(jquery_default(n.content).appendTo(t.grid.el).hide().fadeIn()):t.remove_loader()}}(this))},e}(InfiniteScroll),extend8=function(r,e){for(var t in e)hasProp8.call(e,t)&&(r[t]=e[t]);function n(){this.constructor=r}return n.prototype=e.prototype,r.prototype=new n,r.__super__=e.prototype,r},hasProp8={}.hasOwnProperty;I.ViewJamScreenshots=function(r){extend8(e,r),e.prototype.current_page=1;function e(t){e.__super__.constructor.call(this,t),this.el.lazy_images()}return e.prototype.get_next_page=function(){return this.current_page+=1,this.el.addClass("loading"),$.get("",{page:this.current_page,format:"json"},function(t){return function(n){return t.el.removeClass("loading"),n.num_items>0?($(n.content).appendTo(t.el.find(".screenshot_grid_widget")).hide().fadeIn(),t.el.lazy_images()):t.remove_loader()}}(this))},e}(InfiniteScroll),_react_ready(function(){var r,e,t,n,i,a,o,s,c,m,b,k,y;return o=ReactDOMFactories,c=o.div,y=o.span,b=o.label,s=o.a,k=o.section,m=o.input,a=R.package("Jam"),$.easing.easeInOutQuad=function(f,d,h,v,u){return(d/=u/2)<1?v/2*d*d+h:-v/2*(--d*(d-2)-1)+h},n=function(){function f(d){var h,v;this.jams_by_id={},this.jams=function(){var u,p,l;for(l=[],u=0,p=d.length;u<p;u++)v=d[u],h=new e(v),this.jams_by_id[v.id]=h,l.push(h);return l}.call(this)}return f.prototype.truncate=function(d){return this.jams=_.reject(this.jams,function(h){return function(v){return v.end_date()<d}}(this))},f.prototype.find_in_progress=function(){return _.filter(this.jams,function(d){return function(h){return h.in_progress()}}(this))},f.prototype.find_in_before_start=function(){return _.filter(this.jams,function(d){return function(h){return h.before_start()}}(this))},f.prototype.find_in_range=function(d,h){return _.filter(this.jams,function(v){return function(u){return u.collides_with(d,h)}}(this))},f}(),e=function(){function f(d){this.data=d}return f.prototype.length=function(){return this.end_date()-this.start_date()},f.prototype.collides_with=function(d,h){return!(+this.start_date()>+h||+this.end_date()<+d)},f.prototype.in_progress=function(){var d;return d=+new Date,d>=+this.start_date()&&d<=+this.end_date()},f.prototype.before_start=function(){var d;return d=+new Date,d<+this.start_date()},f.prototype.after_end=function(){var d;return d=+new Date,d>+this.end_date()},f.prototype.start_date=function(){return this._start_date||(this._start_date=parse_timestamp(this.data.start_date))},f.prototype.end_date=function(){return this._end_date||(this._end_date=parse_timestamp(this.data.end_date))},f.prototype.voting_end_date=function(){return this._voting_end_date||(this._voting_end_date=parse_timestamp(this.data.voting_end_date))},f}(),i=React.memo(i=function(f){var d,h,v,u,p,l;for(d=dayjs_default(f.start_date).startOf("month"),v=f.end_date,p=[];d.toDate()<v;)h=d.add(1,"month"),u=f.x_scale_truncated(d.toDate()),l=f.x_scale_truncated(h.toDate()),p.push(c({key:"month-"+d,className:"month_marker",style:{left:u+"px",width:l-u+"px"}},y({className:"sticky_label"},d.format("MMMM")))),d=h;return c({className:"month_markers"},p)}),r=React.memo(r=function(f){var d,h,v,u,p,l,g;for(v=1e3*60*60*24,d=dayjs_default(f.start_date),u=+f.end_date,l=[];+d<u;)h=d.add(1,"day"),p=f.x_scale_truncated(d.toDate()),g=f.x_scale_truncated(h.toDate()),l.push(c({key:"day-"+d,className:"day_marker",style:{width:g-p+"px",left:p+"px"}},c({className:"day_ordinal"},d.format("Do")),c({className:"day_name"},d.format("ddd")))),d=h;return c({className:"day_markers"},l)}),t=React.memo(t=function(f){var d,h,v,u,p,l;return u=f.jam,d=u.highlight?"hsl("+u.hue%360+", 52%, 58%)":void 0,p=u.highlight?"hsl("+u.hue%360+", 52%, 48%)":void 0,l=f.right-f.left,v=l<40,h=l<80,l<10?null:c({key:"jam-"+u.id,"data-jam_id":u.id,className:classNames("jam_cell",{is_tiny:v,is_small:h,after_end:f.after_end}),style:{backgroundColor:d,textShadow:p?"1px 1px 1px "+p:void 0,left:f.left+"px",width:l+"px"}},y({className:"sticky_label"},s({href:u.url,target:"_blank",title:u.title},u.title)," ",y({className:"joined_count"},"("+I.number_format(u.joined||0)+" joined)")))}),a("JamCalendar",{pure:!0,getDefaultProps:function(){var f;return f=dayjs_default().startOf("day"),{day_width:120,cell_height:30,start_date:f.subtract(1,"month").toDate(),end_date:f.add(2,"month").toDate()}},getInitialState:function(){return{jams:new n(this.props.jams)}},componentDidUpdate:function(f){if(f.jams!==this.props.jams)return this.setState({jams:new n(this.props.jams)})},scroll_to_date:function(f){var d;return d=this.calendar_ref.current,$(d).animate({scrollLeft:this.x_scale(f-$(d).width()/2/this.x_ratio())},{duration:600,easing:"easeInOutQuad"})},move_calendar:function(f,d){var h;return h=this.calendar_ref.current,h.scrollLeft+=-f},componentDidMount:function(){return this.scroll_to_date(new Date),$(this.calendar_scrolling_ref.current).draggable({mobile:!1,skip_drag:function(f){return function(d){if($(d.target).closest("a").length)return!0}}(this),move:function(f){return function(d,h){return f.move_calendar(d,h)}}(this)})},render:function(){return k({className:classNames(this.enclosing_class_name(),"calendar",{mobile_scrolling:I.is_mobile()}),ref:this.calendar_ref||(this.calendar_ref=React.createRef())},this.render_jam_rows())},render_jam_rows:function(){var f,d,h;return f=this.state.jams.find_in_range(this.props.start_date,this.props.end_date),h=this.stack_jams(f),d=h.map(function(v){return function(u,p){return c({className:"calendar_row",key:"row-"+p},u.map(function(l){var g,w;return g=v.x_scale_truncated(l.start_date()),w=v.x_scale_truncated(l.end_date()),React.createElement(t,{key:"jam-"+l.data.id,jam:l.data,after_end:l.end_date()<new Date,left:g,right:w})}))}}(this)),c({className:"calendar_scrolling",ref:this.calendar_scrolling_ref||(this.calendar_scrolling_ref=React.createRef()),style:{width:this.scrolling_width()+"px",height:40*3+6+Math.max(1,h.length)*(this.props.cell_height+3)+"px"}},c({className:"calendar_rows"},d,d.length?void 0:c({className:"empty_message sticky_label"},"No jams match your filters")),this.render_day_markers(),this.render_month_markers(),c({className:"elapsed_time",style:{width:this.x_scale(new Date)+"px"}}))},render_month_markers:function(){return React.createElement(i,{start_date:this.props.start_date,end_date:this.props.end_date,x_scale_truncated:this.x_scale_truncated})},render_day_markers:function(){return React.createElement(r,{start_date:this.props.start_date,end_date:this.props.end_date,x_scale_truncated:this.x_scale_truncated})},sort_by_length:function(f){var d,h,v,u,p,l,g,w;for(d=1e3*60*60*24*12,w=[],p=[],g=[],h=0,u=f.length;h<u;h++)v=f[h],v.length()<d?w.push(v):v.after_end()?g.push(v):p.push(v);return w.sort(function(j,N){return j.length()-N.length()}),p.sort(function(j,N){return N.length()-j.length()}),l=w.concat(p).concat(g),l},stack_jams:function(f){var d,h,v,u,p,l,g,w,j,N,S,x;for(x=[],this.sort_by_length(f),h=0,l=f.length;h<l;h++){for(v=f[h],N=!1,u=0,g=x.length;u<g;u++){for(S=x[u],d=!1,p=0,w=S.length;p<w&&(j=S[p],d=v.collides_with(j.start_date(),j.end_date()),!d);p++);if(!d){S.push(v),N=!0;break}}N||x.push([v])}return x},scrolling_width:function(){var f;return f=(this.props.end_date-this.props.start_date)/(1e3*60*60*24),Math.floor(this.props.day_width*f)},x_ratio:function(){return this.scrolling_width()/(this.props.end_date-this.props.start_date)},x_scale:function(f){return Math.floor((f-+this.props.start_date)*this.x_ratio())},x_scale_truncated:function(f){return Math.min(this.scrolling_width(),Math.max(0,this.x_scale(f)))}}),a("FilteredJamCalendar",{getInitialState:function(){return{min_participants:"",duration:"",featured_only:!1}},get_jams:function(){var f,d,h;return d=this.props.jams,this.state.min_participants&&this.state.min_participants!==""&&(h=+this.state.min_participants,d=d.filter(function(v){return v.joined>=h})),this.state.featured_only&&(d=d.filter(function(v){return v.featured})),this.state.duration&&this.state.duration!==""&&(f=+this.state.duration,d=d.filter(function(v){var u;return u=parse_timestamp(v.end_date)-parse_timestamp(v.start_date),f<0?u<=-1*f*1e3*60*60*24:u>=1*f*1e3*60*60*24})),d},render:function(){return k({className:this.enclosing_class_name()},c({className:"jam_calendar_filters"},c({className:"label"},"Filter")," ",R.Forms.SimpleSelect({name:"min_participants",value:this.state.min_participants,onChange:function(f){return function(d){return f.setState({min_participants:d})}}(this),options:[{name:"Any number",short_name:"Participants",value:""},{name:"More than 10",short_name:"Participants \u2265 10",value:"10"},{name:"More than 100",short_name:"Participants \u2265 100",value:"100"}]})," ",R.Forms.SimpleSelect({name:"duration",value:this.state.duration,onChange:function(f){return function(d){return f.setState({duration:d})}}(this),options:[{name:"Any duration",short_name:"Duration",value:""},{name:"Less than 3 days",short_name:"\u2264 3 days",value:"-3"},{name:"Less than 7 days",short_name:"\u2264 7 days",value:"-7"},{name:"Less than 15 days",short_name:"\u2264 15 days",value:"-15"},{name:"Greater than 15 days",short_name:"\u2265 15 days",value:"15"}]})," ",b({},m({type:"checkbox",checked:this.state.featured_only,onChange:function(f){return function(d){return f.setState({featured_only:d.target.checked})}}(this)})," ",y({className:"label",title:"Top picks from the itch.io team"},"Featured"))),R.Jam.JamCalendar({jams:this.get_jams()}))}})});var LoadOnScroll=null;_react_ready(function(){return LoadOnScroll=R2("LoadOnScroll",{componentWillUnmount:function(){return typeof this.unbind_visibility=="function"?this.unbind_visibility():void 0},componentDidMount:function(){var r;return r=ReactDOM2.findDOMNode(this),this.unbind_visibility=$(r).lazy_images({elements:[r],show_images:function(e){return function(){var t;return(t=e.props)!=null?t.on_seen():void 0}}(this)})},render:function(){return this.props.children}})});var slice5=[].slice,create_line_icon=null,TriUpIcon=null,TriDownIcon=null,FilterIcon=null,EditIcon=null,ExternalIcon=null,HelpIcon=null,SearchIcon=null,TagIcon=null,VerifiedIcon=null,BrowseCategoryIcon=null;_react_ready(function(){var r,e,t;return r=ReactDOMFactories,e=r.a,t=r.img,create_line_icon=function(){var n,i,a;return a=arguments[0],i=2<=arguments.length?slice5.call(arguments,1):[],a==null&&(a={}),React2.createElement.bind(null,React2.memo(n=function(o){var s,c,m,b,k;return React2.createElement.apply(React2,["svg",{className:classNames2("svgicon",o.className,a.className),role:"img",version:"1.1",viewBox:"0 0 24 24",width:(s=o.width)!=null?s:"24",height:(c=o.height)!=null?c:"24",fill:(m=(b=o.fill)!=null?b:a.fill)!=null?m:"none",stroke:"currentColor",strokeWidth:(k=o.strokeWidth)!=null?k:"2",strokeLinejoin:"round",strokeLinecap:"round","aria-hidden":!0}].concat(slice5.call(i)))}))},TriUpIcon=create_line_icon({className:"icon_tri_up",fill:"currentColor"},React2.createElement("polygon",{points:"2 18 12 6 22 18"})),TriDownIcon=create_line_icon({className:"icon_tri_down",fill:"currentColor"},React2.createElement("polygon",{points:"2 6 12 18 22 6"})),FilterIcon=create_line_icon({className:"icon_filter"},React2.createElement("polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"})),EditIcon=create_line_icon({className:"icon_edit"},React2.createElement("path",{d:"M20 14.66V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h5.34"}),React2.createElement("polygon",{points:"18 2 22 6 12 16 8 16 8 12 18 2"})),ExternalIcon=create_line_icon({className:"icon_external_link"},React2.createElement("path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"}),React2.createElement("polyline",{points:"15 3 21 3 21 9"}),React2.createElement("line",{x1:"10",y1:"14",x2:"21",y2:"3"})),HelpIcon=create_line_icon({className:"icon_help"},React2.createElement("circle",{cx:"12",cy:"12",r:"10"}),React2.createElement("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),React2.createElement("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})),SearchIcon=create_line_icon({className:"icon_search"},React2.createElement("circle",{cx:"11",cy:"11",r:"8"}),React2.createElement("line",{x1:"21",y1:"21",x2:"16.65",y2:"16.65"})),TagIcon=create_line_icon({className:"icon_tag"},React2.createElement("path",{d:"M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"}),React2.createElement("line",{x1:"7",y1:"7",x2:"7",y2:"7"})),BrowseCategoryIcon=create_line_icon({className:"icon_browse_category"},React2.createElement("path",{d:"M4 4h16v2H4zM4 10h10v2H4zM4 16h14v2H4z"})),VerifiedIcon=create_line_icon({className:"icon_verified"},React2.createElement("title",{},"Verified Account"),React2.createElement("path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14"}),React2.createElement("polyline",{points:"22 4 12 14.01 9 11.01"}))});var SortIcon=null;_react_ready(function(){var r,e;return r=ReactDOMFactories,e=r.img,SortIcon=function(t){var n,i,a,o;return o=(i=t.width)!=null?i:24,n=(a=t.height)!=null?a:o,React2.createElement("svg",{className:"svgicon icon_sort",role:"img",version:"1.1",viewBox:"0 0 455 488",width:o,height:n,"aria-hidden":!0,fill:"currentColor"},React2.createElement("path",{d:"M304 392v48c0 4.5-3.5 8-8 8h-64c-4.5 0-8-3.5-8-8v-48c0-4.5 3.5-8 8-8h64c4.5 0 8 3.5 8 8zM184 360c0 2.25-1 4.25-2.5 6l-79.75 79.75c-1.75 1.5-3.75 2.25-5.75 2.25s-4-0.75-5.75-2.25l-80-80c-2.25-2.5-3-5.75-1.75-8.75s4.25-5 7.5-5h48v-344c0-4.5 3.5-8 8-8h48c4.5 0 8 3.5 8 8v344h48c4.5 0 8 3.5 8 8zM352 264v48c0 4.5-3.5 8-8 8h-112c-4.5 0-8-3.5-8-8v-48c0-4.5 3.5-8 8-8h112c4.5 0 8 3.5 8 8zM400 136v48c0 4.5-3.5 8-8 8h-160c-4.5 0-8-3.5-8-8v-48c0-4.5 3.5-8 8-8h160c4.5 0 8 3.5 8 8zM448 8v48c0 4.5-3.5 8-8 8h-208c-4.5 0-8-3.5-8-8v-48c0-4.5 3.5-8 8-8h208c4.5 0 8 3.5 8 8z"}))},SortIcon=React2.createElement.bind(null,React2.memo(SortIcon))});var hasProp9={}.hasOwnProperty,indexOf=[].indexOf||function(r){for(var e=0,t=this.length;e<t;e++)if(e in this&&this[e]===r)return e;return-1};_react_ready(function(){var r,e,t,n,i,a,o,s,c,m,b,k,y,f,d,h,v;return n=ReactDOMFactories,i=n.a,b=n.label,o=n.div,a=n.button,h=n.span,v=n.ul,k=n.li,d=n.section,m=n.input,s=n.fragment,y=n.option,f=n.p,c=n.h2,s=React.createElement.bind(null,React.Fragment),s.type=React.fragment,t=45,e=R2.package("Jam"),r=R2.package("Browse"),I.karma_score=function(u){var p,l;return p=u.coolness||0,l=u.rating_count||0,Math.log(1+p)-Math.log(1+l)/Math.log(5)},r("FilterGroup",{pure:!0,propTypes:{label:PropTypes.string.isRequired,active:PropTypes.bool,default_filters_open:PropTypes.bool,filters:PropTypes.array.isRequired,on_pick_filter:PropTypes.func,on_clear_group:PropTypes.func,on_clear_filter:PropTypes.func},getInitialState:function(){var u;return{filters_open:(u=this.props.default_filters_open)!=null?u:!0}},toggle_open:function(u){return this.setState(function(p){return{filters_open:!p.filters_open}})},render:function(){var u,p,l,g,w,j,N,S;for(w=0,j=this.props.filters,l=0,g=j.length;l<g;l++)u=j[l],u.active&&(w+=1);return p=(N=this.props.active)!=null?N:w>0,this.enclose({className:classNames("browse_filter_group_widget",{active:p,filters_open:this.state.filters_open})},o({className:"filter_group_label"},a({onClick:this.toggle_open,className:"group_toggle_btn",type:"button"},h({className:"filter_arrow"},h({className:"icon icon-triangle-down"}),h({className:"icon icon-triangle-right"}))," ",this.props.label),p&&w===1&&((S=this.props.show_group_clear)==null||S)?a({className:"filter_clear",title:this.t("browse.filter_group.clear_filter"),onClick:function(x){return function(M){var C;if(x.props.on_clear_group)return x.props.on_clear_group();if(w===1)return typeof(C=x.props).on_clear_filter=="function"?C.on_clear_filter(x.props.filters.find(function(L){return L.active})):void 0}}(this)},CloseIcon({width:16,stroke_width:3})):void 0),v({},this.props.filters.map(function(x){return function(M,C){return k({key:M.id?"filter-"+M.id:C,title:M.tooltip,className:classNames({active:M.active})},a({type:"button",style:M.style,onClick:function(L){var T;return typeof(T=x.props).on_pick_filter=="function"?T.on_pick_filter(M):void 0}},M.label),M.active&&w>1?o({},a({type:"button",title:"Clear filter",className:"filter_clear",onClick:function(L){var T;return typeof(T=x.props).on_clear_filter=="function"?T.on_clear_filter(M):void 0}},CloseIcon({width:16,stroke_width:3}))):void 0)}}(this))),this.props.default_filters_open===!1&&w>0?a({className:"toggle_more_btn",onClick:this.toggle_open,"data-more_label":this.t("browse.filter_group.more_options"),"data-fewer_label":this.t("browse.filter_group.fewer_options")},this.tt("browse.filter_group.toggle_more_options")):void 0)}}),e("EntriesFilters",{pure:!0,getInitialState:function(){return{}},getDefaultProps:function(){return{filters:{}}},render_search:function(){var u;if(this.props.show_search&&!((((u=this.props.filter_counts)!=null?u.total:void 0)||0)<2))return this.search_debounced||(this.search_debounced=_.throttle(function(p){return function(l){return p.props.set_filter("search",l)}}(this),300,{leading:!1})),d({className:"search"},m({type:"text",className:"search_input",placeholder:"Search by title or author",required:!0,onChange:function(p){return function(l){return p.search_debounced($.trim(l.target.value))}}(this)}))},render_rated_filter:function(){var u,p,l;if(u=[],(p=this.props.filter_counts)!=null&&p.user_ratings&&(u.push({label:"Haven't rated ("+I.number_format(this.props.filter_counts.user_missing_ratings||0)+")",value:"havent_rated",active:this.props.filters.rated===!1}),u.push({label:"Have rated ("+I.number_format(this.props.filter_counts.user_ratings||0)+")",value:"have_rated",active:this.props.filters.rated===!0})),((l=this.props.filter_counts)!=null?l.submitted_games:void 0)>0&&u.push({active:this.props.filters.own_submissions,value:"own_submissions",label:"My submissions ("+I.number_format(this.props.filter_counts.submitted_games)+")"}),!!u.length)return r.FilterGroup({label:"Submissions",filters:u,on_pick_filter:function(g){return function(w){switch(w.value){case"have_rated":case"havent_rated":return g.props.set_filter({own_submissions:"",rated:w.value==="have_rated"});case"own_submissions":return g.props.set_filter({own_submissions:!0,rated:""})}}}(this),on_clear_filter:function(g){return function(w){switch(w.value){case"have_rated":case"havent_rated":return g.props.set_filter("rated","");case"own_submissions":return g.props.set_filter("own_submissions","")}}}(this)})},render_platform_filter:function(){var u,p,l,g,w,j,N,S;return p=[],l=function(x){return function(M,C){return p.push({label:C,value:M,active:x.props.filters.platform===M})}}(this),(u=(g=this.props.filter_counts)!=null?g.web:void 0)&&l("web",s({},h({className:"icon icon-globe"}),"Play in browser ("+I.number_format(u)+")")),(u=(w=this.props.filter_counts)!=null?w.windows:void 0)&&l("windows",s({},h({className:"icon icon-windows8"}),"Windows ("+I.number_format(u)+")")),(u=(j=this.props.filter_counts)!=null?j.osx:void 0)&&l("osx",s({},h({className:"icon icon-apple"}),"macOS ("+I.number_format(u)+")")),(u=(N=this.props.filter_counts)!=null?N.linux:void 0)&&l("linux",s({},h({className:"icon icon-tux"}),"Linux ("+I.number_format(u)+")")),(u=(S=this.props.filter_counts)!=null?S.android:void 0)&&l("android",s({},h({className:"icon icon-android"}),"Android ("+I.number_format(u)+")")),p.length?r.FilterGroup({label:this.tt("browse.filter_group_platform"),filters:p,on_pick_filter:function(x){return function(M){return x.props.set_filter("platform",M.value)}}(this),on_clear_filter:function(x){return function(M){return x.props.set_filter("platform","")}}(this)}):null},render_sorts:function(){var u,p,l,g,w,j,N;if(!((((g=this.props.filter_counts)!=null?g.total:void 0)||0)<2))return u=(w=this.props.sort)!=null?w:"",p=[],l=function(S,x){return p.push({label:s({},SortIcon({width:18}),S),value:x,active:u===x})},l(this.tt("browse.sort_popular"),""),l("Random","random"),(j=this.props.filter_counts)!=null&&j.total_coolness&&l("Most Karma","most_karma"),(N=this.props.filter_counts)!=null&&N.total_ratings&&(l("Least rated","least_ratings"),l("Most rated","most_ratings")),l("Submission order","created_at_asc"),l("Most recently submitted","created_at_desc"),r.FilterGroup({label:this.tt("browse.sort"),default_filters_open:!1,show_group_clear:u!=="",filters:p,on_pick_filter:function(S){return function(x){return S.props.set_sort(x.value)}}(this),on_clear_filter:function(S){return function(x){return S.props.set_sort("")}}(this)})},render_fields:function(){if(this.props.fields)return this.props.fields.map(function(u){return function(p,l){var g,w,j,N,S,x,M;for(w="field:"+p.id,j=[],x=p.options,N=0,S=x.length;N<S;N++)y=x[N],g=(M=u.props.filter_counts)!=null?M[w+":"+y.id]:void 0,g&&j.push({key:w,label:y.name+" ("+I.number_format(g)+")",tooltip:y.description?y.name+" "+y.description:y.name,value:y.id,active:u.props.filters[w]===y.id});if(j.length)return r.FilterGroup({key:p.id?"field-"+p.id:"idx-"+l,label:p.name,filters:j,on_pick_filter:function(C){return u.props.set_filter(C.key,C.value)},on_clear_filter:function(C){return u.props.set_filter(C.key,"")}})}}(this))},render:function(){return this.enclose({component:"section",className:"filter_pickers"},this.render_search(),this.render_rated_filter(),this.render_platform_filter(),this.render_fields(),this.render_sorts())}}),e("BrowseEntries",{getInitialState:function(){return{loading:!0,sort:this.props.default_sort,filter_counts:this.props.jam_games?this.summarize_results(this.props.jam_games):void 0}},componentDidMount:function(){if(this.props.total_entries_count&&this.props.jam_games&&this.props.total_entries_count===this.props.jam_games.length){this.setState({page_number:1,jam_games:this.props.jam_games},function(u){return function(){return u.refresh_random_order(),u.setState(Object.assign({loading:!1},u.filter_results(u.state.jam_games)))}}(this));return}return this.refresh_games()},refresh_games:function(){return this.setState({loading:!0}),$.get(this.props.entries_url).done(function(u){return function(p){if(p.errors){u.setState({loading:!1,errors:p.errors});return}return u.setState({page_number:1,jam_games:_.toArray(p.jam_games),generated_on:p.generated_on,fetched_on:+new Date/1e3},function(){return u.refresh_random_order(),u.setState(Object.assign({loading:!1},u.filter_results(u.state.jam_games)))})}}(this)).fail(function(u){return function(p){var l,g;return l=((g=p.responseJSON)!=null?g.errors:void 0)||["There was an error fetching the entries ("+p.status+")"],u.setState({loading:!1,errors:l})}}(this))},set_filter:function(u,p){return this.setState(function(l){return function(g){var w,j,N,S,x;w=Object.assign({},g.filters),S=typeof u=="string"?(N={},N[u]=p,N):u;for(j in S)hasProp9.call(S,j)&&(x=S[j],x===""?delete w[j]:w[j]=x);return{filters:w,page_number:1}}}(this),function(l){return function(){if(l.state.jam_games)return l.setState(l.filter_results(l.state.jam_games))}}(this))},refresh_random_order:function(){var u,p,l,g,w;if(this.state.jam_games){for(g=this.state.jam_games,w=[],u=0,l=g.length;u<l;u++)p=g[u],w.push(p.r=Math.random());return w}},set_sort:function(u){return this.refresh_random_order(),this.setState({sort:u,page_number:1},function(p){return function(){return p.setState(p.filter_results(p.state.jam_games))}}(this))},transform_query:function(u){var p;return u=u.replace(/[:^~]/g," "),p=u.match(/[^\s]+/g).map(function(l){return function(g){return g.match(/[*]$/)||g.match(/^[-+*]/)?g:g+"^2 "+g+"*"}}(this)),p.join(" ")},filter_results:function(u){var p,l,g,w,j,N,S,x,M,C,L,T,q;return N=this.state.page_number*t,q=this.summarize_results(u),C=!1,L=!1,l=function(){var z,F;if(g=this.state.filters){if(M=this.state.filters.search,M)if(M=this.transform_query(M),this.state.search_index)for(C=this.state.search_index.search(M),L={},z=0,F=C.length;z<F;z++)x=C[z],L[x.ref]=!0;else this.get_search_index().then(function(A){return function(){if(A.state.filters===g)return A.set_filter("search",g.search)}}(this));return u.filter(function(A){return function(P){var O,E,D,G,U,V,H,J;if(L&&!L[P.game.id])return!1;G=A.state.filters;for(O in G)if(hasProp9.call(G,O))switch(J=G[O],O){case"platform":if(!(P.game.platforms&&indexOf.call(P.game.platforms,J)>=0))return!1;break;case"rated":if(D=(U=A.props.rated_games)!=null?U[P.game.id]:void 0,J===!0){if(!D)return!1}else if(J===!1&&D)return!1;break;case"own_submissions":if(J&&!((V=A.props.submitted_games)!=null&&V[P.game.id]))return!1;break;default:if((E=O.match(/^field:(\d+)/))&&(H=E[1]+":"+J,!(P.field_responses&&indexOf.call(P.field_responses,H)>=0)))return!1}return!0}}(this))}else return u}.call(this),T=function(){var z,F,A,P,O;if(C){for(p={},z=0,A=l.length;z<A;z++)j=l[z],p[j.game.id]=j;for(O=[],F=0,P=C.length;F<P;F++)x=C[F],j=p[x.ref],j&&O.push(j);return O}else switch(this.state.sort){case"random":return l.slice().sort(function(E,D){return E.r-D.r});case"most_karma":return l.slice().sort(function(E,D){var G,U;return G=I.karma_score(E),U=I.karma_score(D),U-G});case"least_ratings":return l.slice().sort(function(E,D){return(E.rating_count||0)-(D.rating_count||0)});case"most_ratings":return l.slice().sort(function(E,D){return(D.rating_count||0)-(E.rating_count||0)});case"created_at_desc":return l.slice().sort(function(E,D){return D.id-E.id});case"created_at_asc":return l.slice().sort(function(E,D){return E.id-D.id});default:return l}}.call(this),S=T.slice(0,N),w=S.length<T.length,{visible_jam_games:S,filter_counts:q,has_more:w}},summarize_results:function(u){var p,l,g,w,j,N,S,x,M,C,L,T,q,z,F,A,P,O,E;for(x={total:0},p=0,w=u.length;p<w;p++){if(C=u[p],C.rating_count&&(x.total_ratings=x.total_ratings||0+C.rating_count),C.coolness&&(x.total_coolness=x.total_coolness||0+C.coolness),x.total+=1,(L=C.game)!=null&&(T=L.platforms)!=null&&T.length)for(q=C.game.platforms,l=0,j=q.length;l<j;l++)M=q[l],x[M]=(x[M]||0)+1;if((z=this.props.rated_games)!=null&&z[(F=C.game)!=null?F.id:void 0]?x.user_ratings=(x.user_ratings||0)+1:x.user_missing_ratings=(x.user_missing_ratings||0)+1,(A=this.props.submitted_games)!=null&&A[(P=C.game)!=null?P.id:void 0]&&(x.submitted_games=(x.submitted_games||0)+1),C.field_responses)for(O=C.field_responses,S=0,N=O.length;S<N;S++)E=O[S],g="field:"+E,x[g]=(x[g]||0)+1}return x},show_next_page:function(){return this.setState(function(u){return function(p){return{page_number:p.page_number+1}}}(this),function(u){return function(){return u.setState(u.filter_results(u.state.jam_games))}}(this))},build_game_object:function(u){return Object.assign({},u.game,{url:u.url,contributors:u.contributors,jam_game:u})},get_search_index:function(){if(this.state.jam_games)return this.lunr_deferred||(this.lunr_deferred=$.Deferred(function(u){return function(p){var l;return l=document.createElement("script"),l.type="text/javascript",l.src=u.props.lunr_js_url,document.body.appendChild(l),I.wait_for_object(window,"lunr",function(){var g,w;return w=u.state.jam_games,g=window.lunr(function(){var j,N,S,x,M,C,L;for(this.ref("id"),this.field("title",{boost:2}),this.field("flat_title"),this.field("authors"),L=[],x=0,C=w.length;x<C;x++)M=w[x],S=M.game,N=S.title.replace(/\s+/,""),N===S.title&&(N=null),j=M.contributors?M.contributors.map(function(T){return function(q){return q.name}}(this)):S.user.name,L.push(this.add({id:S.id,title:S.title,flat_title:N,authors:j}));return L}),u.setState({search_index:g},function(){return p.resolve(g)})})}}(this)))},render_unrated_nag:function(){if(this.props.jam.during_voting&&!this.props.jam.score_upvote&&this.props.allowed_to_rate)return this.props.rate_queue_required?f({className:"rating_message"},"This jam requires your to rate entries at random. ",i({href:this.props.unrated_entries_url,className:"forward_link"},"View your queue")):f({className:"rating_message"},this.tt("jam.submissions.help_submissions_in_need_of_ratings",{a:function(u){return function(p){return i({href:u.props.unrated_entries_url},p)}}(this)}))},render:function(){return this.enclose({className:classNames("browse_columns",{sidebar_open:this.state.sidebar_open})},o({className:"browse_sidebar",tabIndex:-1,ref:this.sidebar_ref||(this.sidebar_ref=React.createRef())},this.render_sidebar()),o({className:"primary_column",onClick:this.state.sidebar_open?function(u){return function(p){return u.setState({sidebar_open:!1}),p.preventDefault()}}(this):void 0},this.render_primary_column()))},render_sidebar:function(){var u;return o({className:"sticky_wrapper"},d({},c({},"Filter Submissions")),e.EntriesFilters({show_search:!0,filter_counts:this.state.filter_counts,set_sort:this.set_sort,set_filter:this.set_filter,fields:this.props.fields,randomizer_url:this.props.randomizer_url,unrated_entries_url:this.props.unrated_entries_url,rate_queue_required:this.props.rate_queue_required,filters:this.state.filters,sort:(u=this.state.sort)!=null?u:this.props.default_sort}),this.props.rate_queue_required?d({className:"filter_pickers sidebar_buttons"},i({href:this.props.unrated_entries_url,className:"button outline randomizer_link"},h({className:"icon icon-shuffle"})," ","View my queue")):this.props.randomizer_url?d({className:"filter_pickers sidebar_buttons"},i({href:this.props.randomizer_url,className:"button outline randomizer_link"},h({className:"icon icon-shuffle"})," ","View random submission")):void 0)},render_primary_column:function(){var u;return s({},o({className:"filter_toggle_row"},a({type:"button",onClick:function(p){return function(l){return p.setState({sidebar_open:!0},function(){var g;return(g=p.sidebar_ref.current)!=null?g.focus():void 0})}}(this)},FilterIcon({}),"Filter Results"),this.props.total_entries_count?h({className:"entries_count"},this.props.total_entries_count+" total entries"):void 0),this.render_unrated_nag(),this.props.show_cache_age&&this.state.fetched_on-this.state.generated_on>1?o({className:"cache_message"},h({className:"icon icon-warning"})," ","Some data on this page is cached, was last updated "+format_timestamp(this.state.generated_on*1e3)):void 0,this.state.errors?R2.Forms.FormErrors({title:"There was an issue with your request",errors:this.state.errors}):this.state.loading?this.props.jam_games?this.render_preview_grid():o({className:"loading_container"},o({className:"loader_spinner"})):this.state.visible_jam_games?this.state.page_number===1&&!((u=this.state.visible_jam_games)!=null&&u.length)?o({className:"empty_content"},this.state.filters?this.tt("jam.submissions.empty_results"):this.props.jam.submissions_upcoming?this.tt("jam.submissions.there_are_no_submissions_yet"):this.tt("jam.submissions.there_were_no_submissions")):s({},GameGrid({ref:this.game_grid_ref||(this.game_grid_ref=React.createRef()),games:this.state.visible_jam_games,build_game_object:this.build_game_object,conversion_source:this.props.conversion_source,cell_props:this.grid_cell_props||(this.grid_cell_props={render_cell_below:function(p){return function(l){var g;if(p.props.show_cache_age&&(g=l.jam_game))return s({},o({},"Ratings: "+g.rating_count),o({},"Coolness: "+g.coolness),o({},"\u0394: "+(g.coolness-g.rating_count)),o({},"K: "+I.karma_score(g)))}}(this),render_thumb_extras:function(p){return function(l){var g;if((g=p.props.rated_games)!=null&&g[l.id])return o({className:"rated_game"},o({className:"rated_label"},h({className:"icon icon-checkmark"})," ",p.tt("jam.submissions.rated")))}}(this),target:"_blank"}),grid_sizer_props:this.grid_sizer_props||(this.grid_sizer_props={on_reshape:function(p){return function(){if(!p.state.grid_ready)return p.setState({grid_ready:!0})}}(this)})}),this.state.has_more&&this.state.grid_ready?LoadOnScroll({key:[this.state.page_number,JSON.stringify(this.state.filters||{},Object.keys(this.state.filters||{}).sort()),this.state.sort].join("-"),on_seen:this.show_next_page},o({className:"loading_container"},o({className:"loader_spinner"}))):void 0):void 0)},render_preview_grid:function(){return o({className:"index_game_grid_widget preview_grid"},this.props.jam_games.map(function(u){return function(p){var l;return l=p.game,o({className:"game_cell index_game_cell_widget",key:l.id},o({className:"bordered"}),o({className:"label"},i({className:"title",href:l.url},l.title)),o({className:"user_row"},i({className:"user_link",href:l.user.url},l.user.name)))}}(this)))}})});var StarPicker=null;_react_ready(function(){var r,e,t,n,i,a;return e=ReactDOMFactories,t=e.button,i=e.label,a=e.span,n=e.input,r=R2.package("Forms"),StarPicker=r("StarPicker",{pure:!0,getDefaultProps:function(){return{total_stars:5,star_filled:"icon-star",star_empty:"icon-star2"}},getInitialState:function(){return{}},set_value:function(o){var s;return this.props.set_value?this.props.set_value(o):this.setState({value:o}),typeof(s=this.props).on_change=="function"?s.on_change(o):void 0},render:function(){var o,s,c,m,b,k,y;return y=(c=(m=(b=this.props.value)!=null?b:this.state.value)!=null?m:this.props.defaultValue)!=null?c:0,o=this.state.preview_value||y,k=function(){var f,d,h;for(h=[],s=f=1,d=this.props.total_stars;1<=d?f<=d:f>=d;s=1<=d?++f:--f)h.push(function(v){return function(u){var p,l,g;return p=u>o?"star icon "+v.props.star_empty:"star icon "+v.props.star_filled,l=y===u,g=u+" Star"+(u===1?"":"s"),t({key:"star-"+u,type:"button",title:g,role:"radio","aria-describedby":v.props["aria-describedby"],"aria-label":g,"aria-checked":l?"true":"false",onMouseEnter:function(){return v.setState({preview_value:u})},onMouseLeave:function(){return v.setState({preview_value:null})},onClick:function(){return v.set_value(u)}},a({className:p}))}}(this)(s));return h}.call(this),this.enclose({"aria-labelledby":this.props["aria-labelledby"],role:"radiogroup",className:classNames2(this.props.className,{interactive:!0,previewing:this.state.preview_value!=null,has_value:y>0})},this.props.name?n({type:"hidden",name:this.props.name,value:y>0?y:""}):void 0,k)}})});var indexOf2=[].indexOf||function(r){for(var e=0,t=this.length;e<t;e++)if(e in this&&this[e]===r)return e;return-1},hasProp10={}.hasOwnProperty;_react_ready(function(){var r,e,t,n,i,a,o,s,c,m,b,k,y,f,d,h,v;return e=ReactDOMFactories,o=e.form,n=e.a,a=e.div,b=e.span,c=e.input,i=e.button,k=e.strong,m=e.label,r=R2.package("Jam"),y=ReactDOMFactories.table,h=ReactDOMFactories.thead,f=ReactDOMFactories.tbody,v=ReactDOMFactories.tr,d=ReactDOMFactories.td,t=0,s=function(){return""+t++},r("JamGameVoter",{getInitialState:function(){var u,p,l,g,w,j;for(j={},g=this.props.criteria,p=0,l=g.length;p<l;p++)u=g[p],j[u.id]=(w=u.existing_rating)!=null?w.score:void 0;return{uid:this.enclosing_class_name()+"-"+s(),criteria_values:j,show_recaptcha:this.props.show_recaptcha,stored_criteria_values:j}},componentDidMount:function(){return $(window).on("beforeunload",this.send_dirty_warning)},componentWillUnmount:function(){return $(window).off("beforeunload",this.send_dirty_warning)},send_dirty_warning:function(){if(this.is_dirty())return"You have unsaved changes"},remote_submit:function(u){if(!this.state.loading)return u.preventDefault(),this.setState({loading:!0,errors:null,flash:!1}),I.remote_submit($(this.form_ref.current)).then(function(p){return function(l){if(p.setState({loading:!1}),l.errors){indexOf2.call(l.errors,"recaptcha")>=0&&p.setState({show_recaptcha:!0}),p.setState({errors:l.errors});return}if(l.flash&&p.setState({flash:l.flash}),l.success)return p.setState({show_recaptcha:!1,stored_criteria_values:p.state.criteria_values})}}(this))},is_completed:function(){var u,p,l,g;if(this.props.type_upvote)return!0;for(g=this.props.criteria,p=0,l=g.length;p<l;p++)if(u=g[p],this.state.criteria_values[u.id]==null)return!1;return!0},is_dirty:function(){var u,p,l,g;p=this.state.stored_criteria_values;for(u in p)if(hasProp10.call(p,u)&&(l=p[u],g=this.state.criteria_values[u],g!=null&&l!==g))return!0;return!1},render:function(){var u;return a({className:classNames(this.enclosing_class_name(),"criteria_rater")},o({ref:this.form_ref||(this.form_ref=React2.createRef()),onSubmit:this.remote_submit,method:"post"},R2.CSRF({}),this.props.rating_token?c({type:"hidden",name:"rating_token",value:this.props.rating_token}):void 0,c({type:"hidden",name:"json",value:"1"}),c({type:"hidden",name:"action",value:"rate"}),this.render_criteria_table(),this.state.errors?R2.Forms.FormErrors({errors:this.state.errors,animated:!0}):void 0,this.state.show_recaptcha?R2.Forms.RecaptchaInput({sitekey:this.props.recaptcha_sitekey}):void 0,u=this.is_completed(),a({className:"buttons_row"},i({className:classNames("button rate_btn",{disabled:!u}),disabled:!u},this.props.type_upvote?this.tt("jam.submission.rate_submission.save_vote"):this.tt("jam.submission.rate_submission.save_rating"))," ",this.state.flash?b({className:"form_flash","aria-live":"polite"},this.state.flash):u?this.is_dirty()?b({className:"form_flash"},k({},"You have unsaved changes!")):void 0:b({className:"form_flash"},"Vote on all criteria to save your rating"))))},render_criteria_table:function(){return y({className:"criteria_wrap"},f({},this.props.criteria.map(function(u){return function(p){var l,g,w,j,N;return l="ratings["+p.name+"]",g=u.state.uid+"-"+p.id+"-label",v({className:"criteria_row",key:p.id},d({className:"crit_label",id:g},p.name),d({},u.props.type_upvote?m({className:"criteria_upvote"},c({type:"checkbox",name:l,defaultChecked:((w=(j=p.existing_rating)!=null?j.score:void 0)!=null?w:0)>0,onChange:function(S){var x;return x={},x[p.id]=S.target.checked,u.setState(function(M){return{flash:!1,criteria_values:Object.assign({},M.criteria_values,x)}})}})," ",k({},u.tt("jam.submission.rate_submission.vote"))):StarPicker({className:"star_picker",name:l,"aria-labelledby":g,"aria-describedby":g,defaultValue:(N=p.existing_rating)!=null?N.score:void 0,on_change:function(S){var x;return x={},x[p.id]=S,u.setState(function(M){return{flash:!1,criteria_values:Object.assign({},M.criteria_values,x)}})}})))}}(this))))}})})})();
