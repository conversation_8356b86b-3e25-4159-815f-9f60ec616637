# **App Name**: StackJack

## Core Features:

- Blackjack Core Game: Simulate a Blackjack game against the dealer.
- Specific Blackjack Rules:
    - Played with **two decks** of standard playing cards.
    - Dealer **stands on all 17s** (including soft 17).
    - Player actions: Hit, Stand.
    - **Doubling Down:** Allowed on any initial two cards, and **also after splitting**.
    - **Splitting:** Allowed for pairs. Player can **double down after splitting**. (Implementation may initially limit re-splitting).
- Display Game State: Display the player's current stack size, hand, and the dealer's visible card.
- Betting System: Allow the player to set a bet amount for each hand.
- AI Strategy Hint: Provide an AI tool that provides basic Blackjack strategy advice (hit/stand/double/split) based on the specific rules, displayed optionally.

## Style Guidelines:

- Primary color: Dark green (#228B22) to mimic a Blackjack table.
- Accent color: Gold (#FFD700) to highlight interactive elements and wins.
- Clean and intuitive layout optimized for iPhone screen size.
- Use simple, clear icons for actions like 'Hit', 'Stand', 'Double Down', and 'Split'.
- Subtle animations for card dealing and win/loss outcomes.

## Original User Request:
create me a blackjack game for iphone where you try to build the biggest stack possible
