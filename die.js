die() {
        // Exit rage state if active
        if (this.rageState) {
            this.exitRageState();
        }

        // Skip if already marked inactive to prevent double-counting
        if (!this.active) return;

        // Make inactive
        this.active = false;

        // Deregister from GroupManager if needed
        if (this.scene.groupManager && this.groupId) {
            this.scene.groupManager.deregister(this, this.groupId);
        }

        // Determine if this is a boss enemy
        const isBoss = this.isBossEnemy();

        // Spawn cash pickup based on enemy value (30% chance for regular enemies, always for bosses)
        if (this.scene.cashManager && this.graphics) {
            // Always drop cash for bosses, 40% chance for regular enemies
            const shouldDropCash = isBoss || Math.random() < 0.4;

            if (shouldDropCash) {
                // Calculate cash value based on enemy type
                let cashMultiplier = 1.0; // Regular enemy

                if (isBoss) {
                    cashMultiplier = 2.0; // Boss enemies drop more cash
                } else if (this.type === 'enemy2' || this.type === 'enemy3') {
                    cashMultiplier = 1.5; // Elite enemies (types 2 and 3) drop more cash
                }

                const cashValue = Math.ceil(this.scoreValue * cashMultiplier);

                // Spawn cash pickup at enemy position
                this.scene.cashManager.spawnCashPickup(
                    this.graphics.x,
                    this.graphics.y,
                    cashValue
                );
            }

            // 5% chance to spawn a health pickup (independent of cash drop)
            if (Math.random() < 0.05 && this.scene.spritePool) {
                // Create health pickup
                this.scene.spritePool.createHealthPickup(
                    this.graphics.x,
                    this.graphics.y,
                    { value: 20 } // Health amount to restore
                );
            }
        }

        // Call the central kill handling method in the Game scene
        if (this.scene.onEnemyKilled) {
            // Pass enemy type and position for effects
            this.scene.onEnemyKilled(
                isBoss,
                this.graphics.x,
                this.graphics.y,
                this.type
            );
        }

        // Cleanup health bar if exists
        if (this.hasHealthBar) {
            this.cleanupHealthBar();
        }

        // When using object pooling, we don't destroy the graphics
        // We just make it inactive - the pool manager will handle the rest
        if (this.graphics) {
            this.graphics.setActive(false);
            this.graphics.setVisible(false);
        }
    }