Issue ID,Issue Type,Summary,Description,Priority,Labels,Epic Name,Parent ID
E100,Epic,Game Jam Prep & Setup,Setup and configuration tasks before the jam begins,High,prep,Game Jam Prep & Setup,
S101,Story,Setup Development Environment,"Prepare dev environment, repo, and structure",High,"dev,prep",Game Jam Prep & Setup,E100
T102,Sub-task,Create a GitHub/GitLab repo,Initialize Git repository,Medium,git,,S101
T103,Sub-task,Set up project structure,"Configure Webpack/Vite, Phaser, Godot, etc.",Medium,structure,,S101
T104,Sub-task,Setup CI/CD,Optional CI/CD setup for builds/tests,Medium,devops,,S101
T105,Sub-task,Define folder structure,"Structure for assets, code, tests",Medium,structure,,S101
S106,Story,Tooling and Dependencies,"Choose tools, frameworks, and linters",High,"tooling,dev",Game Jam Prep & Setup,E100
T107,Sub-task,Choose engine/framework,"Phaser, Three.js, Unity, etc.",High,engine,,S106
T108,Sub-task,Add essential libraries,"Audio, input, physics",Medium,libs,,S106
T109,Sub-task,Setup ESLint/Prettier,Linting/formatting setup,Low,lint,,S106
T110,Sub-task,Setup build script and hot reload,Build tool config and live reload,Medium,build,,S106
E200,Epic,"Art, Audio, and Asset Pipeline",Creative asset and pipeline setup,Medium,assets,"Art, Audio, and Asset Pipeline",
S201,Story,Asset Workflow Preparation,Set up tools for art pipeline,Medium,art,"Art, Audio, and Asset Pipeline",E200
T202,Sub-task,Decide on art style,"Pixel art, vector, hybrid",High,artstyle,,S201
T203,Sub-task,Choose art tools,"Aseprite, Piskel",Medium,tools,,S201
T204,Sub-task,Create import pipeline for assets,Automate asset loading,Medium,pipeline,,S201
T205,Sub-task,Create placeholder art,Placeholder character + environment,Medium,placeholder,,S201
S206,Story,Audio Workflow Setup,Setup for audio assets,Low,audio,"Art, Audio, and Asset Pipeline",E200
T207,Sub-task,Choose SFX/music tools,"BFXR, Bosca Ceoil",Medium,sfx,,S206
T208,Sub-task,Create placeholder audio,Temp SFX + music test files,Medium,audio,,S206
T209,Sub-task,Implement audio manager,Basic sound controller,Medium,audio,,S206
E300,Epic,Team Communication & Workflow,Setup for planning and collaboration,High,workflow,Team Communication & Workflow,
S301,Story,Communication & Planning,"Daily standups, docs, roles",High,team,Team Communication & Workflow,E300
T302,Sub-task,Define SCRUM cadence,Set time + tool for daily check-ins,Medium,scrum,,S301
T303,Sub-task,Shared doc for brainstorming,Create Google Doc / Notion,Medium,doc,,S301
T304,Sub-task,Create Jira workflows,Define issue lifecycle,High,jira,,S301
T305,Sub-task,Assign team roles,Split up responsibilities,Medium,roles,,S301
E400,Epic,Pre-Jam Game Design,Design work before jam theme is revealed,Medium,design,Pre-Jam Game Design,
S401,Story,Game Design Research,Research themes and genres,Low,research,Pre-Jam Game Design,E400
T402,Sub-task,Research past themes,Review past GameDev.js themes,Low,themes,,S401
T403,Sub-task,Collect inspiration,"Genres, mechanics, visuals",Medium,inspo,,S401
T404,Sub-task,Decide on genres,Pick what fits the team,Medium,genres,,S401
T405,Sub-task,Brainstorm reusable systems,"Tilemap, point-and-click, etc.",High,systems,,S401
S406,Story,Prototype Starter Systems,Build starter systems for dev,High,prototype,Pre-Jam Game Design,E400
T407,Sub-task,Create input system,Keyboard/mouse/touch,High,controls,,S406
T408,Sub-task,Build test scenes,"Menu, player movement",High,testscenes,,S406
T409,Sub-task,Create scene templates,Reusable scene format,Medium,template,,S406
T410,Sub-task,Implement basic UI,"Buttons, text, visuals",Medium,ui,,S406
E500,Epic,Marketing & Submission Prep,"Page, branding, packaging",Low,marketing,Marketing & Submission Prep,
S501,Story,Prepare for Distribution,Game page and polish assets,Low,release,Marketing & Submission Prep,E500
T502,Sub-task,Setup itch.io page,Create project listing,Medium,itchpage,,S501
T503,Sub-task,Create logo/splash,Placeholder logo/art,Low,logo,,S501
T504,Sub-task,Naming and metadata,"Define title, tags, description",Medium,meta,,S501
T505,Sub-task,Final submission checklist,"Zip, screenshots, trailer?",Medium,final,,S501
