(()=>{var Ke=Object.create,we=Object.defineProperty,Xe=Object.getOwnPropertyDescriptor,ye=Object.getOwnPropertyNames,Qe=Object.getPrototypeOf,Ze=Object.prototype.hasOwnProperty,et=(t,e)=>function(){return e||(0,t[ye(t)[0]])((e={exports:{}}).exports,e),e.exports},tt=(t,e,r,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of ye(e))!Ze.call(t,i)&&i!==r&&we(t,i,{get:()=>e[i],enumerable:!(n=Xe(e,i))||n.enumerable});return t},be=(t,e,r)=>(r=t!=null?Ke(Qe(t)):{},tt(e||!t||!t.__esModule?we(r,"default",{value:t,enumerable:!0}):r,t)),xe=et({"coffee/bundle/_react.js"(){var t,e,r,n={}.hasOwnProperty,i=[].slice;window.R||(t={},e=function(o,s){var a;for(a in o)if(n.call(o,a)&&o[a]!==s[a])return!0;for(a in s)if(n.call(s,a)&&!(a in o))return!0;return!1},r={},window.R=function(o,s,a,u){var l,c,d,h,g,x;return a==null&&(a=R),u==null&&(u=""),a[o]?(I.in_dev&&console.warn("Already declared, using existing:",o),a[o]):(s.trigger=function(){var m;m=$(ReactDOM.findDOMNode(this)),R.trigger.apply(R,[m].concat(i.call(arguments)))},s.dispatch=function(){var m;m=$(ReactDOM.findDOMNode(this)),R.dispatch.apply(R,[m].concat(i.call(arguments)))},s.container=function(){return $(ReactDOM.findDOMNode(this))},s.tt=function(m,y){var f;return y==null&&(y=t),(f=I.i18n.fetch_key_sync(m))&&_.isString(f)?f:(r[m]||(r[m]=I.i18n.react_class(m)),React.createElement(r[m],y))},s.t=function(m,y){return I.i18n.fetch_key_sync(m)?I.i18n.t_sync(m,y):(I.i18n.fetch_key(m).done(function(f){return function(){if(f.isMounted())return f.forceUpdate()}}(this)),"\u2026")},c=_.once(function(){return(""+u+o).replace(/[A-Z]/g,"_$&").replace(/\./g,"_").replace(/__+/g,"_").replace(/^_+/,"").replace(/_+$/,"").toLowerCase()+"_widget"}),s.enclosing_class_name=c,s.enclose=function(){var m,y,f;return f=arguments[0],m=2<=arguments.length?i.call(arguments,1):[],y=f.component||"div",delete f.component,React.createElement.apply(React,[y,$.extend({},f,{className:classNames(c(),f.className)})].concat(i.call(m)))},s.displayName=""+u+o,s.pure&&(s.shouldComponentUpdate=function(m,y){return e(this.props||t,m)||e(this.state||t,y)}),d=s.getDefaultProps,delete s.getDefaultProps,x=s.propTypes,delete s.propTypes,h=s.getDerivedStateFromError,delete s.getDerivedStateFromError,l=createReactClass(s),d&&(l.defaultProps=d()),x&&(l.propTypes=x),h&&(l.getDerivedStateFromError=h),g=React.createElement.bind(null,l),g.type=l,a[o]=g)},R.scope_event_name=function(o){return"itch:"+o},R.trigger=function(){var o,s,a;return a=arguments[0],s=arguments[1],o=3<=arguments.length?i.call(arguments,2):[],a.trigger(R.scope_event_name(s),i.call(o))},R.dispatch=function(o,s,a){var u,l,c,d;typeof s=="object"&&(a=s,s=!1),u=[],d=function(h,g){return u.push(function(){return o.off(h,g)})};for(l in a)n.call(a,l)&&(c=a[l],s&&(l=s+":"+l),l=R.scope_event_name(l),o.on(l,c),d(l,c));return function(){return u.map(function(h){return h()})}},R.component=function(){return R.apply(null,arguments)},R.is_different=e,R.package=function(o){var s;return s=R[o]||(R[o]=function(a,u){return u.Package=s,R.component(a,u,s,o+".")}),s.component=function(){return s.apply(null,arguments)},s},R.render=function(o,s){var a;return window.ReactDOM?(a=typeof o=="string"?document.querySelector(o):o,ReactDOM.render(s(),a)):I.libs.react.done(function(){return R.render(o,s)})})}}),Z,ke,Ie,rt={}.hasOwnProperty,$e=[].slice;ke=10,Z="inputmemory_keys",Ie=1,window.I={get_worker:function(){var t;return t=$.Deferred(),I.get_worker=function(){return t},t.resolve(new Worker("/static/worker.min.js")),t},solve_challenge:function(t){return I.get_worker().then(function(e){var r,n,i;return r=Ie++,e.postMessage([r,"solve_challenge",t]),n=$.Deferred(),e.addEventListener("message",i=function(o){return function(s){var a,u,l;if(u=s.data,a=u[0],l=u[1],a===r)return e.removeEventListener("message",i),n.resolve(l)}}(this)),n})},libs:{react:$.Deferred(function(t){if(typeof React!="undefined"&&React!==null)return t.resolve(React)}),selectize:$.Deferred(function(t){var e;if(typeof $!="undefined"&&$!==null&&(e=$.fn)!=null&&e.selectize)return t.resolve($.fn.selectize)}),redactor:$.Deferred(function(t){var e;if(typeof $!="undefined"&&$!==null&&(e=$.fn)!=null&&e.redactor)return t.resolve($.fn.redactor)})},setup_page:function(){return I.setup_register_referrers($(document.body)),_.defer(function(t){return function(){return I.setup_affiliate_code()}}(this))},root_url:function(){var t;return t=null,function(e){return t||(t=$("body").data("host")||"",t!==""&&(t=window.location.protocol+"//"+t,window.location.port&&window.location.port!=="80"&&(t+=":"+window.location.port))),t+"/"+e}}(),page_name:function(){return $(document.body).data("page_name")||"unknown"},sort_keys:function(t){var e,r;return r=[],e=function(n){var i,o,s;o=[];for(i in n)rt.call(n,i)&&(s=n[i],r.push(i),typeof s=="object"?o.push(e(s)):o.push(void 0));return o},e(t),r.sort()},get_csrf:function(){return I._csrf_token||(I._csrf_token=$("meta[name='csrf_token']").attr("value"))},with_csrf:function(t){var e;return t==null&&(t={}),e={csrf_token:I.get_csrf()},typeof t=="string"?t+"&"+$.param(e):$.extend(t,e)},add_params:function(t,e){var r;return r=$.param(e),t.match(/\?/)?t+"&"+r:t+"?"+r},flash:function(t,e){return e==null&&(e="notice"),I.flasher||(I.flasher=new I.Flasher),t.match(/^error:/)&&(t=t.replace(/^error:/,"Error: "),e="error"),I.flasher.show(e,t)},slugify:function(t,e){return t=t.replace(/\s+/g,"-"),t=e!=null&&e.for_tags?t.replace(/[^\w_.-]/g,"").replace(/^[_.-]+/,"").replace(/[_.-]+$/,""):t.replace(/[^\w_-]/g,""),t.toLowerCase()},truncate:function(t,e,r){return e==null&&(e=30),r==null&&(r="..."),t?t.length>e+r.length?""+t.slice(0,e)+r:t:""},number_format:function(t,e,r,n){var i,o,s;return r==null&&(r="."),n==null&&(n=","),isNaN(t)||t===null?"":(t=t.toFixed(~~e),s=t.split("."),o=s[0],i=s[1]?r+s[1]:"",o.replace(/(\d)(?=(?:\d{3})+$)/g,"$1"+n)+i)},format_bytes:function(){var t;return t=[["gb",Math.pow(1024,3)],["mb",Math.pow(1024,2)],["kb",1024]],function(e){var r,n,i,o,s;for(r=0,i=t.length;r<i;r++)if(s=t[r],n=s[0],o=s[1],e>=o)return""+I.number_format(e/o)+n;return I.number_format(e)+" bytes"}}(),ecommerce_event:function(t){return window.gtag?gtag("event","purchase",t):console.log("purchase event:",t)},event:function(t,e,r,n,i){var o;return i==null&&(i=!0),o={hitType:"event",eventCategory:t,eventAction:e,eventLabel:r,eventValue:n},i||(o.nonInteraction=1),I.event_with_opts(o)},event_with_opts:function(t){try{return window.ga!=null?window.ga("send",t):(console.log("ga event:",t),typeof t.hitCallback=="function"?t.hitCallback():void 0)}catch(e){}},parse_money:function(t){var e;return e=t&&parseInt(t.replace(/[^\d]/g,""),10),e||0},currency_symbols:{USD:"$",GBP:"\xA3",EUR:"\u20AC",JPY:"\xA5"},currency_formats:{USD:{prefix:"$"},GBP:{prefix:"\xA3"},JPY:{prefix:"\xA5"},EUR:{suffix:"\u20AC"}},format_money:function(t,e){var r,n;return e==null&&(e="USD"),t<0?"-"+I.format_money(-t,e):(n=I.currency_symbols[e]||"$",e==="JPY"?n+t:(r=I.number_format(t/100,2),e==="EUR"?""+r+n:""+n+r))},money_input:function(t,e){var r,n;return e==null&&(e={}),t=$(t),r=e.currency||t.data("currency"),n=I.currency_formats[r]||{prefix:"$"},t.maskMoney($.extend({affixesStay:!0,precision:r==="JPY"?0:2},n,e))},plural:function(t,e){return e===1?e+" "+t:e+" "+t+"s"},add_recaptcha_if_necessary:function(t,e){if(e[0]==="recaptcha")return t.data("adding_recaptcha")||(t.data("adding_recaptcha",!0),I.with_recaptcha(function(){var r;return r=t.find(".g-recaptcha"),I.event("recaptcha","show",I.page_name()),grecaptcha.render(r[0],{sitekey:r.data("sitekey")})})),t.set_form_errors(["Please fill out the CAPTCHA to continue"]),!0},with_recaptcha:function(t){var e;return window.grecaptcha?t():I.recaptcha_deferred?I.recaptcha_deferred.done(t):(I.recaptcha_deferred=$.Deferred().done(t),window._itch_recaptcha_loaded=function(){return I.recaptcha_deferred.resolve()},e="https://www.google.com/recaptcha/api.js?onload=_itch_recaptcha_loaded&render=explicit",$('<script defer type="text/javascript">').attr("src",e).appendTo("head"))},wait_for_object:function(t,e,r){var n,i,o;return i=1,n=10,o=function(){return t[e]?typeof r=="function"?r():void 0:(i+=n,i=Math.min(500,i),setTimeout(o,i))},o()},with_selectize:function(t){return I.libs.selectize.done(t)},get_template:function(t){return _.template($("#"+t+"_tpl").html())},lazy_template:function(t,e){return function(){var r,n;return r=1<=arguments.length?$e.call(arguments,0):[],n=I.get_template(e),t.prototype.template=n,n.apply(null,r)}},setup_sticky_bar:function(t){var e,r,n;return n=$(window),e=$(document.body),t=$(t),r=!1,n.on("scroll",function(i){if(n.scrollTop()>t.offset().top+t.outerHeight()){if(!r)return e.addClass("show_sticky_bar"),r=!0}else if(r)return e.removeClass("show_sticky_bar"),r=!1})},setup_selectize:function(t){return t.find(".selectize_input").addBack(".selectize_input").each(function(e,r){var n,i,o,s,a,u,l;if(n=$(r),!n.hasClass("selectized"))return a={plugins:[],persist:!1},n.is("select")&&(i={},n.find("option").each(function(c,d){var h;return h=$(d),i[h.val()]=h.data("extra")}),l=function(c,d){var h,g;return h=d(c.text),(g=i[c.value])&&(h=h+" <span class='sub'>\u2014 "+g+"</span>"),"<div class='option'>"+h+"</div>"},a.render={item:l,option:l}),(u=n.data("placeholder"))&&(a.placeholder=u),n.hasClass("dropdown")&&(a.maxItems=1),(s=n.data("options"))&&(n.hasClass("options_object")?(a.options=s,a.searchField=["text","keywords"]):a.options=s.map(function(c){return{value:c[0],text:c[1]}}),a.plugins.push("remove_button"),a.delimiter=",",a.persist=!1),(o=n.data("optgroups"))&&(a.optgroups=o),n.selectize(a)})},format_filesize:function(t,e){var r,n,i,o,s;for(e==null&&(e=".file_size_value"),o=t.find(e),s=[],n=0,i=o.length;n<i;n++)r=o[n],r=$(r),s.push(r.html(I.format_bytes(parseInt(r.html()))));return s},adjust_font_size_to_fit:function(t,e){var r,n,i;if(e==null&&(e=16),!!t.length){for(n=t.css("font-size"),n=parseInt(n.match(/\d+/),10),r=t[0],i=[];r.offsetWidth<r.scrollWidth&&(n-=2,!(n<e));)i.push(t.css("font-size",n+"px"));return i}},slug_input:function(t,e){var r;if(r=/[a-z_0-9_-]/g,t.on("keypress",function(n){var i;if(n.keyCode>=32&&(i=String.fromCharCode(n.keyCode),!i.match(r)))return!1}),e&&e.length)return t.on("change",function(n){var i;if(i=I.slugify(e.val()),t.val().match(/^\s*$/)&&i!=="")return t.val(i)}),e.on("change",function(n){if(t.val().match(/^\s*$/))return t.val(I.slugify($(n.currentTarget).val()))})},deferred_links:function(t,e,r){return r||(r=I.delegate_tracking),t.on("mouseup",e,function(n){var i;if(n.which===2)return i=$(n.currentTarget),r(i,n)}),t.on("click",e,function(n){var i,o,s,a;if(n.which===1&&(a=$(n.currentTarget),o=n.metaKey||n.ctrlKey||n.shiftKey||a.attr("target")==="_blank",i=null,o||(i=function(){if(i)return i=null,window.location=a.attr("href")},setTimeout(function(){return typeof i=="function"?i():void 0},200)),(s=r(a,n))!=null&&s.done(i),!o))return!1})},delegate_tracking:function(t){var e;return e=[],t.trigger("i:delegate_tracking",[function(r){return e.push(r)}]),$.when.apply($,e)},ga_tracker:function(t,e,r,n){var i;return i=function(o){var s,a,u,l,c;return s={hitType:"event",eventCategory:(a=o.data("category"))!=null?a:t,eventAction:(u=o.data("action"))!=null?u:e,eventLabel:(l=o.data("label"))!=null?l:r,eventValue:(c=o.data("value"))!=null?c:n},$.Deferred(function(d){return s.hitCallback=function(){return d.resolve()},I.event_with_opts(s)})}},tracked_links:function(){var t,e,r,n,i,o,s;return r=arguments[0],i=2<=arguments.length?$e.call(arguments,1):[],e=i[0],t=i[1],n=i[2],s=i[3],o=I.ga_tracker(e,t,n,s),I.deferred_links(r,"a[data-label]",o),r.on("i:track_link",function(a,u){var l;return u&&(l=u[0]),I.ga_tracker(e,t,l!=null?l:n,s)($(a.target))})},set_cookie:function(t,e,r){return r==null&&(r={}),I.in_dev&&console.log("set cookie:",[t,e],r),Cookies.set(t,e,$.extend({path:"/",domain:"."+$(document.body).data("host")},r))},set_register_referrer:function(t){var e;if(e=I.page_name(),e!=null&&I.set_cookie("ref:register:page_params",e),t!=null)return I.set_cookie("ref:register:action",t)},setup_register_referrers:function(t){if(!I.current_user)return t.on("mouseup","[data-register_action]",function(e){var r;return r=$(e.currentTarget).data("register_action"),I.set_register_referrer(r)})},setup_affiliate_code:function(t,e){var r,n;if(e==null&&(e=!1),t==null&&(t=(n=window.location.search.match(/\bac=([\w\d]+(?:\-[\.:_\w\d]+)?)/))!=null?n[1]:void 0),!!t&&!(!e&&t===Cookies.get("acode")))return I.set_cookie("acode",t,{expires:1}),document.referrer&&I.set_cookie("acode:ref",document.referrer.substring(0,180),{expires:1}),r=window.location.href.replace(/\?.*$/,"").substring(0,180),I.set_cookie("acode:land",r,{expires:1})},bind_checkbox_to_input:function(t,e,r){var n;return n=function(){var i;return i=t.prop("checked"),e.prop("disabled",i),typeof r=="function"?r(i):void 0},t.on("change",n),n()},add_facebook:function(t){var e,r,n;if(!window.FB&&(I.add_facebook=function(){},$('<div id="fb-root"></div>').appendTo(document.body),r="facebook-jssdk",!document.getElementById(r)))return t!=null&&(window.fbAsyncInit=t),e=document.getElementsByTagName("script")[0],n=document.createElement("script"),n.id=r,n.src="//connect.facebook.net/en_GB/sdk.js#xfbml=1&appId=537395183072744&version=v2.0",e.parentNode.insertBefore(n,e)},add_twitter:function(){var t,e,r;if(e="twitter-wjs",!document.getElementById(e))return I.add_twitter=function(){},t=document.getElementsByTagName("script")[0],r=document.createElement("script"),r.id="twitter-wjs",r.src="//platform.twitter.com/widgets.js",t.parentNode.insertBefore(r,t)},add_react:function(){var t,e;return t=$.Deferred(),window.ReactDOM?(t.resolve(),t):(I.add_react=function(){return t},I.libs.react.done(function(){return t.resolve()}),e=$("#lib_react_src"),e.replaceWith($('<script type="text/javascript">').attr("src",e.data("src"))),t)},has_follow_button:function(t,e){var r,n,i,o,s,a,u;return e==null&&(e={}),t=$(t),i=(s=e.cls)!=null?s:"follow_button_widget",r=(a=e.animate_follow)!=null?a:"animate_bounce",n=(u=e.animate_unfollow)!=null?u:"animate_drop_down",o={},o[i]=function(l){var c,d,h,g;if(!I.current_user)return"continue";if(!l.is(".loading"))return l.removeClass(r+" "+n),h=$.Deferred(function(x){return setTimeout(function(){return x.resolve()},500)}),d=l.is(".is_following"),g=d?(_.defer(function(){return l.addClass(n)}),l.data("unfollow_url")):(_.defer(function(){return l.addClass(r)}),l.data("follow_url")),l.addClass("loading").trigger("i:track_link",["follow_btn"]),c=l.data("follow_data")||{},$.when($.post(g,I.with_csrf(c)),h).done(function(x){var m;if(m=x[0],l.removeClass("loading "+r+" "+n),m.errors){I.flash(m.errors.join(", "));return}return l.toggleClass("is_following",m.following).trigger("i:follow_updated",[{user_id:l.data("user_id"),following:m.following,btn:l}])})},t.dispatch("click",o),$(document.body).on("i:follow_updated",function(l,c){var d,h,g;return g=c.user_id,h=c.following,d=c.btn,t.find("."+i+"[data-user_id="+g+"]").not(d).toggleClass("is_following",h)})},is_mobile:function(){return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)},is_firefox:function(){var t;return t=navigator.userAgent,t.match(/Firefox/)&&!t.match(/Seamonkey/)},is_safari:function(){var t;return t=navigator.userAgent,t.match(/Safari/)&&!t.match(/Chrome|Chromium|Android|Edge/)},is_ios:function(){return/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream},strip_css:function(t){return t.replace(/<\s*\/\s*style\s*>/i,"")},request_fullscreen:function(t,e){var r,n,i;return r=t.requestFullscreen?(t.requestFullscreen(),!0):t.msRequestFullscreen?(t.msRequestFullscreen(),!0):t.mozRequestFullScreen?(t.mozRequestFullScreen(),!0):t.webkitRequestFullscreen?(t.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT),!0):!1,r&&e&&(n=window.screen)!=null&&(i=n.orientation)!=null&&typeof i.lock=="function"&&i.lock(e),r},is_fullscreen:function(){return!(!document.fullscreenElement&&!document.mozFullScreenElement&&!document.webkitFullscreenElement&&!document.msFullscreenElement)},exit_fullscreen:function(){if(document.exitFullscreen)return document.exitFullscreen();if(document.msExitFullscreen)return document.msExitFullscreen();if(document.mozCancelFullScreen)return document.mozCancelFullScreen();if(document.webkitExitFullscreen)return document.webkitExitFullscreen()},toggle_fullscreen:function(t,e){return I.is_fullscreen()?(I.exit_fullscreen(),!1):I.request_fullscreen(t,e)?!0:"failed"},setup_dirty_warning:function(t,e){return e||(e=function(){return t.data("dirty")}),t.data("dirty",!1),$(window).on("beforeunload",function(){var r;if(r=e(),!!r)switch(typeof r){case"string":return r;default:return"You've made modifications to this page."}}),t.on("i:after_submit",function(){return t.data("dirty",!1)}),_.defer(function(){return t.on("change","input, select, textarea",function(r){return t.data("dirty",!0)}),t.on("keydown, mousedown",".redactor-editor",function(){return t.data("dirty",!0)})})},remote_submit:function(t,e,r){var n,i;return r==null&&(r={}),t.trigger("i:before_submit"),r.loading_lock&&(i=t.serializeArray(),n=t.addClass("loading").find("button, input[type='submit']").prop("disabled",!0).addClass("disabled")),$.when(e).then(function(o){var s;return i||(i=t.serializeArray()),o&&(i=i.concat(o)),$.ajax({data:i,type:(s=t.attr("method"))!=null?s:"POST",dataType:"json",url:t.attr("action"),xhrFields:{withCredentials:!0},error:function(a){var u;return u=function(){try{return JSON.parse(a.responseText)}catch(l){}}(),u!=null&&u.errors?I.flash(u.errors.join(", ")):I.flash("We seem to be having server problems right now! Please try again later.")},complete:function(){return r.loading_lock&&(n.prop("disabled",!1).removeClass("disabled"),t.removeClass("loading")),t.trigger("i:after_submit")}}).then(null,function(a){try{return JSON.parse(a.responseText)}catch(u){throw a}})})},is_middle_click:function(t){return t.which===2||t.metaKey||t.ctrlKey},escape_regex:function(t){return t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")},move:function(t,e,r){var n,i,o,s;if(i=t.indexOf(e),i===-1)throw"Failed to find object in array";if(s=i+r,s<0||s>=t.length)throw"Movement is outside of array ("+s+") [0, "+(t.length-1)+"]";return n=function(){var a,u,l;for(l=[],a=0,u=t.length;a<u;a++)o=t[a],o!==e&&l.push(o);return l}(),n.splice(s,0,e),n},format_integer:function(t){return t>1e7?Math.floor(t/1e6)+"m":t>=1e6?Math.floor(t/1e5)/10+"m":t>1e4?Math.floor(t/1e3)+"k":t>=1e3?Math.floor(t/100)/10+"k":""+t},store_memory:function(t,e){var r,n,i,o;if(window.localStorage){if(i=function(){try{return JSON.parse(localStorage.getItem(Z))}catch(s){}}(),i||(i=[]),i[0]!==t){for(n=function(){var s,a,u;for(u=[],s=0,a=i.length;s<a;s++)r=i[s],t!==r&&u.push(r);return u}(),i=[t].concat(n);i.length>ke;)o=i.pop(),typeof localStorage!="undefined"&&localStorage!==null&&localStorage.removeItem(o);localStorage.setItem(Z,JSON.stringify(i))}return localStorage.setItem(t,e)}},clear_memory:function(t){var e,r;if(window.localStorage){r=function(){try{return JSON.parse(localStorage.getItem(Z))}catch(n){}}(),r&&localStorage.setItem(Z,JSON.stringify(function(){var n,i,o;for(o=[],n=0,i=r.length;n<i;n++)e=r[n],t!==e&&o.push(e);return o}()));try{return localStorage.removeItem(t)}catch(n){}}}},$(function(){var t;if((t=window.location.hash.match(/\bflash=([^&]*)/))&&(I.flash(t[1]),window.location.hash=window.location.hash.replace(/\bflash=([^&]*)/,"")),I.ie)return $(document.body).addClass("ie")}),I.Flasher=function(){t.prototype.duration=1e4,t.prototype.animation_duration=250,t.prototype.clipping="-7px";function t(){$(document).on("click",".global_flash",function(e){return function(){return e.dismiss()}}(this))}return t.prototype.dismiss=function(){var e;if(e=this.current_flash)return this.timeout&&(clearTimeout(this.timeout),this.timeout=null),e.css({"margin-top":"-"+(e.outerHeight()+4)+"px"}),setTimeout(function(r){return function(){return e.remove()}}(this),this.animation_duration*2)},t.prototype.show=function(e,r){var n;return this.dismiss(),n=$("<div class='global_flash "+e+"' role='alert' aria-live='polite'>").text(r).appendTo("body"),n.css({"margin-left":"-"+n.width()/2+"px","margin-top":"-"+(n.outerHeight()+4)+"px"}),this.timeout=setTimeout(function(i){return function(){return n.addClass("animated"),n.css({"margin-top":i.clipping}),setTimeout(function(){return i.dismiss()},i.duration)}}(this),100),this.current_flash=n},t}(),_.templateSettings={escape:/\{\{(?![&])(.+?)\}\}/g,interpolate:/\{\{&(.+?)\}\}/g,evaluate:/<%([\s\S]+?)%>/g},$.easing.smoothstep=function(t){return t*t*t*(t*(t*6-15)+10)};var K,X=K=window.dayjs;window._dayjs_setup||(K.extend(window.dayjs_plugin_duration),K.extend(window.dayjs_plugin_calendar),K.extend(window.dayjs_plugin_advancedFormat),K.extend(window.dayjs_plugin_relativeTime),K.extend(window.dayjs_plugin_utc),window._dayjs_setup=!0);var ce,nt=[].slice;ce=function(t){var e;return(e=typeof t=="string"&&t.match(/(^\d{4}\-\d{1,2}\-\d{1,2}) (\d{1,2}:\d{1,2}:\d{1,2})$/))?e[1]+"T"+e[2]+"Z":t};var Ce=function(t){return X(ce(t)).toDate()},Re=function(){var t,e,r,n;switch(n=arguments[0],e=arguments[1],t=3<=arguments.length?nt.call(arguments,2):[],e==null&&(e="fromNow"),n=ce(n),e){case"fromNow":return X(n).fromNow();case"calendar":return X(n).calendar(null,{sameElse:"MMMM Do YYYY"});case"format":return(r=X(n)).format.apply(r,t);default:throw new Error("unknown method for format_timestamp: "+e)}},je;$.fn.dispatch=function(t,e){return this.on(t,function(r){return function(n){var i,o,s,a;for(s in e)if(o=e[s],i=$(n.target).closest("."+s),!!i.length)return i.is(".disabled")?!1:i.is("a")&&n.ctrlKey||(a=o(i,n),a==="continue")?void 0:!1;return null}}(this)),this},$.fn.exists=function(){return this.length>0?this:!1},$.fn.format_timestamps=function(t){var e,r,n,i,o;for(t==null&&(t="calendar"),o=this.find(".date_format"),r=function(s){var a,u,l;return a=s.data("format"),u=s.html(),l=a?Re(u,"format",a):Re(u,t),s.html(l).attr("title",u+" UTC")},n=0,i=o.length;n<i;n++)e=o[n],r($(e));return this},$.fn.has_tooltips=function(t){var e,r,n,i,o,s;if(t==null&&(t={}),!I.is_mobile())return o=function(){var a;return a=$('<div class="tooltip_drop"></div>'),$(document.body).append(a),o=function(){return a},a},s=_.template('<div class="tooltip">{{ label }}</div>'),i=function(a,u){var l,c,d,h;return u==null&&(u=!1),l=a.data("tooltip_el"),l||(l=$(s({label:a.attr("aria-label")||a.data("tooltip")})),a.data("tooltip_el",l)),l.removeClass("visible"),o().empty().append(l),d=a.offset(),c=l.outerHeight(),h=l.outerWidth(),l.css({position:"absolute",top:t.below?d.top+a.outerHeight()+10:d.top-c-10,left:Math.floor(d.left+(a.outerWidth()-h)/2)}),l.toggleClass("below",!!t.below),u?l.addClass("visible"):setTimeout(function(g){return function(){return l.addClass("visible")}}(this),10)},r=function(a){return function(u){var l,c;if(c=$(u.currentTarget),l=c.data("tooltip_el"),c.removeData("tooltip_el"),!!l&&l.is(":visible"))return i(c,!0)}}(this),n=function(a){return function(u){var l;if(l=$(u.currentTarget),!l.closest(".redactor-box").length)return i(l)}}(this),e=function(a){return function(u){var l,c;if(c=$(u.currentTarget),l=c.data("tooltip_el"))return l.remove()}}(this),this.is("[data-tooltip]")?(this.on("i:refresh_tooltip",r),this.on("mouseenter focus",n),this.on("mouseleave blur i:hide_tooltip",e)):(this.on("i:refresh_tooltip","[data-tooltip], [aria-label]",r),this.on("i:clear_tooltips",function(){return o().empty()}),this.on("mouseenter focus","[data-tooltip], [aria-label]",n),this.on("mouseleave blur i:hide_tooltip","[data-tooltip], [aria-label]",e)),this},$.fn.inline_edit=function(t){var e,r,n;if(t==null&&(t={}),this.is(".inline_editing")){this.data("finished_editing")();return}return this.addClass("inline_editing"),r=$('<input type="text" class="inline_edit_input" />').attr("placeholder",this.data("placeholder")).val((typeof t.get_val=="function"?t.get_val(this):void 0)||this.text()).insertAfter(this.hide()).select(),e=function(i){return function(){return r.remove(),i.show(),$(document).off("click.inline_editing"),i.removeClass("inline_editing")}}(this),this.data("finished_editing",e),n=function(i){return function(){return r.prop("disabled",!0),t.set_val?t.set_val(i,r.val(),e):(i.text(r.val()),e())}}(this),$(document).on("click.edit_title",function(i){return function(o){if(!$(o.target).closest(".inline_edit_input").length)return n()}}(this)),r.on("keydown",function(i){return function(o){switch(o.keyCode){case 9:return _.defer(function(){return n()});case 13:return n();case 27:return e()}}}(this))},I.support_passive_scroll=function(){var t,e;e=!1;try{t=Object.defineProperty({},"passive",{get:function(r){return function(){return e=!0}}(this)}),window.addEventListener("test",null,t)}catch(r){}return e},I.support_intersection_observer=function(){return"IntersectionObserver"in window},je=function(t,e){var r,n,i,o,s,a,u,l;for(t.removeClass("lazy_images"),n=t.find("[data-background_image]").addBack("[data-background_image]"),o=function(){var c,d,h;for(h=[],c=0,d=n.length;c<d;c++)r=n[c],h.push(function(g){var x;if(g=$(g),x=g.data("background_image"),g.css({backgroundImage:"url("+x+")"}),e)return $.Deferred(function(m){return function(y){return $("<img />").attr("src",x).on("load",function(){return y.resolve()})}}(this))}(r));return h}(),l=t.find("img[data-lazy_src]").addBack("img[data-lazy_src]"),i=function(c){var d,h;if(c=$(c),d=c.data("lazy_src"),c.attr("src",d),(h=c.data("lazy_srcset"))&&c.attr("srcset",h),e)return $.Deferred(function(g){return function(x){return c.on("load",function(){return x.resolve()})}}(this))},a=0,u=l.length;a<u;a++)s=l[a],i(s);if(e)return o.length===1?o[0]:$.when.apply($,o)},$.fn.lazy_images=function(t){var e,r,n,i,o,s,a,u,l,c,d,h,g,x,m,y;if(d=this.data("lazy_images"))return d();if(u=t!=null&&t.elements?function(){var f,p,v,b;for(v=t.elements,b=[],f=0,p=v.length;f<p;f++)n=v[f],b.push($(n));return b}():(h=(t!=null?t.selector:void 0)||".lazy_images",function(){var f,p,v,b;for(v=this.find(h),b=[],f=0,p=v.length;f<p;f++)n=v[f],b.push($(n));return b}.call(this)),e=(c=t!=null?t.show_images:void 0)!=null?c:je,I.support_intersection_observer()){for(i=function(f){var p,v,b,C,j,M;for(M=[],b=0,C=f.length;b<C;b++)v=f[b],v.isIntersecting?(n=v.target,s.unobserve(n),n=$(n),j=t!=null?t.show_item:void 0,p=e(n,!!j),M.push(typeof j=="function"?j(n,p):void 0)):M.push(void 0);return M},s=new IntersectionObserver(i,{}),a=0,l=u.length;a<l;a++)n=u[a],s.observe(n[0]);return function(){return s.disconnect()}}return y=$(window),g=t!=null?t.target:void 0,o=t!=null?t.horizontal:void 0,m=null,r=function(f){return function(){var p,v,b,C,j,M,Y,O,z;for(p=function(){if(g){if(o)return g.outerWidth()+g.position().left;throw new Error("not yet")}else return y.scrollTop()+y.height()}(),b=0,C=M=0,Y=u.length;M<Y;C=++M)if(j=u[C],!!j){if(!document.body.contains(j[0])){u[C]=null,b+=1;continue}z=function(){if(g){if(o)return j.position().left;throw new Error("not yet")}else return j.offset().top}(),j[0].offsetParent&&z<p&&(O=t!=null?t.show_item:void 0,v=e(j,!!O),typeof O=="function"&&O(j,v),b+=1,u[C]=null)}if(b>0)return u=function(){var S,D,P;for(P=[],S=0,D=u.length;S<D;S++)n=u[S],n&&P.push(n);return P}()}}(this),x=_.throttle(r,100),g?(g.on("scroll",x),y.on("resize",x),m=function(){return g.off("scroll",x),y.off("resize","throttled")}):I.support_passive_scroll()?(window.addEventListener("scroll",x,{passive:!0}),y.on("resize i:reshape",x),m=function(){return window.removeEventListener("scroll",x,{passive:!0}),y.off("resize",x)}):(y.on("scroll resize i:reshape",x),m=function(){return y.off("scroll resize i:reshape",x)}),this.data("lazy_images",function(f){return function(){return u=t!=null&&t.elements?function(){var p,v,b,C;for(b=t.elements,C=[],p=0,v=b.length;p<v;p++)n=b[p],C.push($(n));return C}():(h=(t!=null?t.selector:void 0)||".lazy_images",function(){var p,v,b,C;for(b=this.find(h),C=[],p=0,v=b.length;p<v;p++)n=b[p],C.push($(n));return C}.call(f)),r()}}(this)),r(),m},$.fn.max_height=function(t){var e,r,n;return t==null&&(t=0),n=$(window),e=this.outerHeight(!0)-this.height(),r=function(i){return function(){return i.css("min-height",n.height()-e-t+"px")}}(this),n.on("resize",r),r()},$.fn.on_first_view=function(t,e){var r,n,i,o,s,a;if(e==null&&(e={}),window.IntersectionObserver==null){console.warn("IntersectionObserver is not supported by this browser.");return}for(o=new IntersectionObserver(function(u){return function(l){return l.forEach(function(c){if(c.isIntersecting)return o.unobserve(c.target),typeof t=="function"?t(c.target):void 0})}}(this)),s=this,a=[],n=0,i=s.length;n<i;n++)r=s[n],a.push(o.observe(r));return a},$.fn.remote_link=function(t){return this.on("click","[data-remote]",function(e){return function(r){var n,i,o,s,a;if(r.preventDefault(),i=$(r.currentTarget),!i.is(".loading")&&(s=i.data("method")||"POST",a=I.with_csrf($.extend({},i.data("params"))),o=i.data("href")||i.attr("href"),!((n=i.data("confirm"))&&!confirm(n))))return i.addClass("loading").prop("disabled",!0),$.ajax({type:s,url:o,data:a,xhrFields:{withCredentials:!0}}).done(function(u){return i.removeClass("loading").prop("disabled",!1),typeof t=="function"?t(u,i):void 0}),null}}(this))},$.fn.remote_submit=function(t,e,r){var n;return n=null,this.on("click","button[name], input[type='submit'][name]",function(i){return function(o){var s;return s=$(o.currentTarget),n!=null&&n.remove(),n=$("<input type='hidden' />").attr("name",s.attr("name")).val(s.attr("value")).prependTo(i)}}(this)),this.on("submit",function(i){return function(o,s){var a;if(o.preventDefault(),a=$(o.currentTarget),!(e&&!(typeof e=="function"&&e(a))))return I.remote_submit(a,r,{loading_lock:!0}).done(function(u){return a.data("dirty",!1),s!=null?s(u,a):t(u,a)}),null}}(this))},$.fn.remote_table=function(){var t,e,r,n;return r=this.data("max_page"),t=function(i){return function(){return i.data("page")||1}}(this),n={},e=function(i){return function(o){var s,a;if(!i.is(".loading")&&1<=o&&o<=r)return s=i.find("table"),i.addClass("loading").data("page",o).toggleClass("first_page",o===1).toggleClass("last_page",o===r).find(".current_page").text(o),a=n[o]||(n[o]=$.get(i.data("remote_url")+"?"+$.param({page:o}))),a.done(function(u){return i.removeClass("loading"),s.html(u.content)})}}(this),this.dispatch("click",{next_page_btn:function(i){return function(){return e(t()+1)}}(this),prev_page_btn:function(i){return function(){return e(t()-1)}}(this)})},$.fn.set_form_errors=function(t,e,r){var n,i,o,s,a,u,l,c;if(e==null&&(e=!0),r==null&&(r="Errors"),this.find(".form_errors").remove(),a=!!(t!=null&&t.length),this.toggleClass("has_errors",a),a){for(o=$(_.template(`<div class="form_errors">
  <div>{{ msg }}:</div>
  <ul></ul>
</div>`)({msg:r})),s=o.find("ul"),u=0,l=t.length;u<l;u++)i=t[u],s.append($("<li></li>").text(i));this.prepend(o),e&&(c=this.closest(".lightbox"),c.length||this.offset().top>$(window).height()/2?typeof(n=this[0]).scrollIntoView=="function"&&n.scrollIntoView():$("html, body").animate({scrollTop:0},"fast"))}return this},$.fn.swap_with=function(t){var e,r,n,i,o,s,a,u;if(t=$(t),!!(this.length&&t.length))return r=this.offset(),i=t.offset(),u=this.prop("tagName"),s=t.prop("tagName"),a=$("<"+u+"></"+u+">").insertAfter(this),o=$("<"+s+"></"+s+">").insertAfter(t),a.after(t),o.after(this),e=this.offset(),n=t.offset(),o.replaceWith(this.detach().css({position:"relative",top:r.top-e.top+"px",left:r.left-e.left+"px"})),a.replaceWith(t.detach().css({position:"relative",top:i.top-n.top+"px",left:i.left-n.left+"px"})),_.defer(function(l){return function(){return l.css({top:"",left:""}),t.css({top:"",left:""})}}(this))},$.fn.track_input=function(t,e){var r,n;return r=this.find(t),n=function(i){return function(){var o,s,a;return a=function(){var u,l,c;for(c=[],u=0,l=r.length;u<l;u++)s=r[u],c.push(e+"_"+$(s).attr("value"));return c}(),i.removeClass(a.join(" ")),o=r.serializeArray().map(function(u){return e+"_"+u.value}),i.addClass(o.join(" "))}}(this),r.on("change",function(i){return n()}),n()};var Jt=be(xe()),it,w=it=window.$,L=[].slice,ot=function(t,e){for(var r in e)st.call(e,r)&&(t[r]=e[r]);function n(){this.constructor=t}return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},st={}.hasOwnProperty;I.Lightbox&&console.warn("I.Lightbox is being double declared");var at=I.Lightbox||(I.Lightbox=function(){t.include_dependencies=function(e){var r;return e?(r=[],e.redactor&&!w.fn.redactor&&(w("head").append(e.redactor),r.push(I.libs.redactor)),e.selectize&&!w.fn.selectize&&(w("head").append(e.selectize),r.push(I.libs.selectize)),r):[]},t.lightbox_container=function(){return this._container||(this._container=w("#lightbox_container").exists()||w('<div id="lightbox_container"></div>').appendTo("body"))},t.shroud_container=function(){return this._shroud||(this._shroud=w("#lightbox_shroud").exists()||w('<div id="lightbox_shroud"></div>').appendTo("body"))},t.show_shroud=function(){var e;if(!this.shroud_visible)return this._shroud||(e=this.shroud_container(),this.lightbox_container().on("click",function(r){return function(n){if(!w(n.target).closest(".lightbox").length)return w(n.target).attr("href")==="javascript:void(0)"?void 0:r.close()}}(this)),e.on("click",function(r){return function(){return r.close()}}(this)),w(document.body).on("keydown",function(r){return function(n){return n.keyCode===27?(n.preventDefault(),n.stopPropagation(),r.close()):r.on_keydown(n)}}(this)),w(window).on("resize",function(r){return function(n){return r.on_resize(n)}}(this))),this.shroud_visible=!0,this.shroud_container().addClass("invisible").show(),_.defer(function(r){return function(){return r.shroud_container().removeClass("invisible")}}(this))},t.hide_shroud=function(){return this.shroud_visible=!1,this.shroud_container().hide()},t.open_loading=function(){return this.open_tpl("loading_lightbox")},t.open_remote_react=function(e,r){return this.open_loading(),w.when(I.add_react(),w.ajax({dataType:"json",url:e,data:{props:!0},xhrFields:{withCredentials:!0},error:function(n){return function(i,o,s){var a,u,l;return l=function(){try{return JSON.parse(i.responseText)}catch(c){}}(),a=(u=l!=null?l.errors:void 0)!=null?u:["Something went wrong. Please try again later and contact support if it persists."],R.ErrorLightbox?I.Lightbox.open(R.ErrorLightbox({errors:a})):(alert(a.join(",")),n.close())}}(this)})).done(function(n){return function(i,o){var s,a;if(a=o[0],a.errors){R.ErrorLightbox?I.Lightbox.open(R.ErrorLightbox(a)):(alert(a.errors.join(",")),n.close());return}return s=I.Lightbox.include_dependencies(a._dependencies),w.when.apply(w,[r(a)].concat(L.call(s))).then(function(u){return u?I.Lightbox.open(u):n.close()})}}(this))},t.open_remote=function(){var e,r,n;return n=arguments[0],e=arguments[1],r=3<=arguments.length?L.call(arguments,2):[],e==null&&(e=I.Lightbox),this.open_loading(),w.ajax({dataType:"json",url:n,data:{lightbox:!0},xhrFields:{withCredentials:!0},success:function(i){return function(o){if(o.errors){alert(o.errors.join(",")),i.close();return}return i.open.apply(i,[o.content,e].concat(L.call(r)))}}(this),error:function(i){return function(o,s,a){return alert("Something went wrong. Please try again later and contact support if it persists."),i.close()}}(this)})},t.open_tpl=function(){var e,r,n;if(n=arguments[0],e=2<=arguments.length?L.call(arguments,1):[],r=w("#"+n+"_tpl"),!r.length)throw"missing template for lightbox: "+n;return this.open.apply(this,[r.html()].concat(L.call(e)))},t.open_tpl_with_params=function(){var e,r,n,i,o;if(o=arguments[0],i=arguments[1],e=3<=arguments.length?L.call(arguments,2):[],r=w("#"+o+"_tpl"),!r.length)throw"missing template for lightbox: "+o;return n=_.template(r.html())(i),this.open.apply(this,[n].concat(L.call(e)))},t.open=function(){var e,r,n,i,o,s;return n=arguments[0],e=arguments[1],r=3<=arguments.length?L.call(arguments,2):[],e==null&&(e=I.Lightbox),this.current_lightbox&&(this.current_lightbox.close(),this.current_lightbox=null),s=!1,n.$$typeof?(s=!0,i=new I.ReactLightbox(n,this)):(typeof n=="string"&&(n=w.trim(n)),o=w(n),(i=o.data("object"))||(s=!0,i=function(a,u,l){l.prototype=a.prototype;var c=new l,d=a.apply(c,u);return Object(d)===d?d:c}(e,[o,this].concat(L.call(r)),function(){}),o.data("object",i))),w(document.body).addClass("lightbox_open"),this.show_shroud(),i.show(s),this.current_lightbox=i,i},t.close=function(){var e;if(!(this.current_lightbox&&!this.current_lightbox.is_closable())&&!(this.current_lightbox&&this.current_lightbox.el.is(".has_changes")&&!confirm("You've have unsaved changes in this dialog. Are you sure you want to close it?")))return this.hide_shroud(),(e=this.current_lightbox)!=null&&e.close(),this.current_lightbox=null,w(document.body).removeClass("lightbox_open")},t.on_keydown=function(e){var r,n;return(r=this.current_lightbox)!=null&&(n=r.el)!=null?n.trigger("i:lightbox_keydown",e):void 0},t.on_resize=function(e){var r,n;return(r=this.current_lightbox)!=null&&(n=r.el)!=null?n.trigger("i:lightbox_resize",e):void 0};function t(){var e,r,n;e=arguments[0],r=arguments[1],n=3<=arguments.length?L.call(arguments,2):[],this.parent=r,this.el=w(e),this.el.data("lightbox",this),this.el.on("click",".close_button, .close_btn",function(i){return function(o){return I.Lightbox.close(),!1}}(this)),this.init.apply(this,n)}return t.prototype.init=function(){},t.prototype.first_show=function(){},t.prototype.with_selectize=function(e){var r;if(!w.fn.selectize){if(r=this.el.data().selectize,!r)throw"missing selectize include in lightbox";w("head").append(r)}return I.with_selectize(e)},t.prototype.with_redactor=function(e){var r;if(!w.fn.redactor){if(r=this.el.data().redactor,!r)throw"missing redactor include in lightbox";w("head").append(r)}return I.libs.redactor.done(e)},t.prototype.show=function(e){if(this.el.appendTo(this.parent.lightbox_container()).addClass("animated").show().trigger("i:lightbox_open"),this.position(),e)return this.first_show()},t.prototype.close=function(){return this.el.removeClass("animated").hide().trigger("i:lightbox_close").remove()},t.prototype.position=function(){var e;return e=this.parent.lightbox_container().css("position")==="fixed"?"":w(window).scrollTop()+"px",this.el.css({top:e})},t.prototype.closable=!0,t.prototype.is_closable=function(){return this.closable},t}()),Yt=I.ReactLightbox||(I.ReactLightbox=function(t){ot(e,t);function e(){var r,n,i,o,s,a;r=arguments[0],i=arguments[1],s=3<=arguments.length?L.call(arguments,2):[],a=w('<div class="react_lightbox"></div>').appendTo(i.lightbox_container()),o=ReactDOM.render(r,a[0]),n=ReactDOM.findDOMNode(o),e.__super__.constructor.apply(this,[n,i].concat(L.call(s)))}return e.prototype.show=function(r){return this.el.addClass("animated"),this.position()},e.prototype.close=function(){var r;return this.el.trigger("i:lightbox_close"),r=this.el.parent(),ReactDOM.unmountComponentAtNode(r[0]),r.remove()},e}(I.Lightbox)),ut=function(t,e){return function(){return t.apply(e,arguments)}};I.GameCarousel=function(){t.prototype.edge_threshold=5,t.prototype.margin=20,t.prototype.inner_padding=10,t.prototype.paddle_margin_bottom=30,t.prototype.cell_class=".game_cell",t.prototype.add_image_loading=function(){return this.el.lazy_images({show_item:function(e){return function(r){return r.trigger("i:impression"),r.find(".image_loading").addBack(".image_loading").removeClass("image_loading")}}(this)})};function t(e,r){this.add_image_loading=ut(this.add_image_loading,this),this.el=$(e),$.extend(this,r),this.scroll_outer=this.el.find(".scrolling_outer"),this.scroll_inner=this.el.find(".scrolling_inner"),this.paddles=this.el.find(".paddle_next, .paddle_prev"),I.is_mobile()?(this.el.find(".scrollbar_outer").remove(),this.paddles.remove()):this.setup_scrollbar(),new I.GameCells(this.el),_.defer(function(n){return function(){return n.add_image_loading()}}(this)),this.el.dispatch("click",{paddle_next:function(n){return function(i){return n.scroll_to(n.cell_offset(n.current_cell()+3)),i.trigger("i:track_link")}}(this),paddle_prev:function(n){return function(i){return n.scroll_to(n.cell_offset(n.current_cell()-3)),i.trigger("i:track_link")}}(this)}),this.update_height(),$(window).on("resize",_.debounce(function(n){return function(){return n.update_scrollbar()}}(this),100)),this.el.on("i:carousel:update_scrollbar",function(n){return function(){return n.update_scrollbar()}}(this)),_.defer(function(n){return function(){return n.el.addClass("ready"),n.update_height()}}(this))}return t.prototype.tallest_child=function(){var e,r,n,i,o,s,a,u;for(a=null,u=null,s=this.scroll_inner.children(),n=i=0,o=s.length;i<o;n=++i)e=s[n],e=$(e),a?(r=e.outerHeight(!0),r>u&&(a=e,u=r)):(a=e,u=e.outerHeight(!0));return a},t.prototype.update_height=function(){var e,r;return r=this.tallest_child(),e=r.outerHeight(!0),e+=this.inner_padding*2,this.scroll_outer.css({height:e+"px"}),this.paddles.css({height:e-this.paddle_margin_bottom+"px"}),this.update_scrollbar()},t.prototype.setup_scrollbar=function(){return this.have_scrollbar=!0,this.scrollbar_outer=this.el.find(".scrollbar_outer"),this.scrollbar_inner=this.el.find(".scrollbar_inner"),this.scroll_inner.on("scroll",function(e){return function(){return e.update_scrollbar()}}(this)),this.scrollbar_inner.draggable({move:function(e){return function(r,n){if(e.unit_scroll)return e.scroll_inner[0].scrollLeft+=r*e.unit_scroll}}(this)}),this.update_scrollbar()},t.prototype.current_cell=function(){var e,r,n,i,o,s;for(r=this.el.find(this.cell_class),n=Math.floor(r.width()/2),i=o=0,s=r.length;o<s;i=++o)if(e=r[i],$(e).position().left+n>=0)return i;return 0},t.prototype.cell_offset=function(e){var r,n,i;return e=Math.max(0,e),r=this.el.find(this.cell_class+":eq("+e+")"),n=this.max_scroll(),i=r.length?r.position().left+this.scroll_pos()-this.margin:n,Math.min(i,n)},t.prototype.scroll_to=function(e){var r,n;if(!this.scroll_inner.is(":animated"))return r=this.scroll_pos(),n=Math.abs(r-e),this.scroll_inner.animate({scrollLeft:e},n/2)},t.prototype.scroll_pos=function(){return this.scroll_inner[0].scrollLeft},t.prototype.max_scroll=function(){return this.scroll_inner[0].scrollWidth-this.scroll_outer.innerWidth()},t.prototype.update_scrollbar=function(){var e,r,n,i;if(this.have_scrollbar)return n=this.scroll_outer.innerWidth(),r=this.scroll_inner[0].scrollWidth,e=r-n>this.edge_threshold,this.el.toggleClass("no_scrollbar",!e),e?(i=this.scroll_pos(),this.scrollbar_inner.css({width:100*n/r+"%",left:100*i/r+"%"}),this.unit_scroll=r/this.scroll_outer.width(),this.el.toggleClass("on_left",i<=this.edge_threshold),this.el.toggleClass("on_right",i>=this.max_scroll()-this.edge_threshold)):this.el.removeClass("on_left on_right")},t}(),I.ContentWarning=function(){function t(e,r){this.opts=r,this.el=$(e),this.setup_forms()}return t.prototype.setup_forms=function(){var e;return e=this.el.find("form"),e.remote_submit(function(r){return function(n){if(n.errors){e.set_form_errors(n.errors);return}return r.el.addClass("hidden"),setTimeout(function(){return r.el.remove()},300),e.set_form_errors([])}}(this))},t}();var lt=function(t,e,r){var n,i,o,s,a,u,l,c,d;for(e==null&&(e=null),r==null&&(r=!0),typeof t=="number"&&(t=X.duration(t)),l=[["y","years","a year"],["m","months","a month"],["d","days","a day"],["h","hours","an hour"],["m","minutes","a minute"],["s","seconds","a second"]],a=[],i=0,s=l.length;i<s&&(u=l[i],!(e&&a.length===e));i++)o=u[0],n=u[1],c=u[2],d=t[n](),d>0&&(d>1?n==="seconds"&&r?a.push("a few seconds"):a.push(d+" "+n):a.push(c));return a.length>1&&(a[a.length-1]="and "+a[a.length-1]),a.join(", ")},ct=function(t,e){return function(){return t.apply(e,arguments)}},ft=function(t,e){for(var r in e)dt.call(e,r)&&(t[r]=e[r]);function n(){this.constructor=t}return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},dt={}.hasOwnProperty;I.Countdown=function(){t.prototype.max_blocks=4;function t(e,r,n){this.date=r,this.opts=n!=null?n:{},this.opts.max_blocks&&(this.max_blocks=this.opts.max_blocks),this.el=$(e),this.update_countdown(),window.setInterval(function(i){return function(){return i.update_countdown()}}(this),1e3)}return t.prototype.update_countdown=function(){var e,r,n,i,o,s,a,u,l,c,d,h,g,x,m,y,f,p;for(d=["years","months","days","hours","minutes","seconds"],e=X.duration(Ce(this.date)-new Date),r=!0,f=function(){var v,b,C;for(C=[],v=0,b=d.length;v<b;v++)l=d[v],p=e[l](),!(p===0&&r)&&(p>0&&(r=!1),C.push([l,p]));return C}(),f=f.slice(0,this.max_blocks),g={},n=0,a=f.length;n<a;n++)h=f[n],l=h[0],y=h[1],g[l]=y;for(x=[],i=0,u=d.length;i<u;i++)l=d[i],c=this.el.find("[data-name='"+l+"']"),p=g[l]||0,m=g[l]==null,m||(o=c.find(".block_label"),s=o.text(),o.text(s.replace(/s$/,"")+(p===1?"":"s"))),x.push(c.toggleClass("hidden",m).find(".block_value").text(p));return x},t}(),I.TimestampCountdown=function(t){ft(e,t);function e(){return this.update_countdown=ct(this.update_countdown,this),e.__super__.constructor.apply(this,arguments)}return e.prototype.update_countdown=function(){var r;return r=Ce(this.date)-new Date,r<0&&(r=-r),this.el.text(lt(r,1,!1))},e}(I.Countdown),$.fn.draggable=function(t){var e,r,n,i,o,s,a,u;return t==null&&(t={}),u="ontouchstart"in document,t.mobile===!1&&(u=!1),e=$(document.body),o=$("html"),s=0,a=0,i=function(l){return function(c){return e.removeClass("dragging"),l.removeClass("dragging"),o.off("mousemove touchmove",r),typeof t.stop=="function"?t.stop():void 0}}(this),r=function(l){return function(c,d,h){var g,x;return g=d-s,x=h-a,s+=g,a+=x,typeof t.move=="function"?t.move(g,x):void 0}}(this),n=function(l){return function(c,d,h){if(!e.is(".dragging")&&!(typeof t.skip_drag=="function"&&t.skip_drag(c)))return e.addClass("dragging"),l.addClass("dragging"),s=d,a=h,typeof t.start=="function"&&t.start(),!0}}(this),u?this.on("touchstart",function(l){return function(c){var d,h,g;d=c.originalEvent.targetTouches[0],h=d.pageX,g=d.pageY,n(c,h,g)&&(o.one("touchend",i),r=function(x){return function(m){var y;return y=m.originalEvent.targetTouches[0],h=y.pageX,g=y.pageY,x(m,h,g)}}(r),o.on("touchmove",r))}}(this)):this.on("mousedown",function(l){return function(c){if(n(c,c.pageX,c.pageY))return o.one("mouseup",i),r=function(d){return function(h){return d(h,h.pageX,h.pageY)}}(r),o.on("mousemove",r)}}(this))};var ht=function(t,e){for(var r in e)pt.call(e,r)&&(t[r]=e[r]);function n(){this.constructor=t}return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},pt={}.hasOwnProperty;I.FeedbackLightbox=function(t){ht(e,t);function e(){return e.__super__.constructor.apply(this,arguments)}return e.prototype.init=function(){return this.el.find("form").remote_submit(function(r){return function(n){if(n.errors){alert(n.errors.join(", "));return}return r.el.addClass("is_complete")}}(this))},e}(I.Lightbox),I.FeedbackWidget=function(){function t(e){this.el=$(e),this.el.on("click",function(r){return function(n){var i;return i=$(n.target).data("url")+"?"+$.param({url:window.location.href}),I.Lightbox.open_remote(i,I.FeedbackLightbox)}}(this))}return t}();var Kt=I.FilterPickers=function(){t.prototype.label_padding=10;function t(e,r){var n,i;this.opts=r!=null?r:{},this.el=$(e),n=function(o){return function(){return o.el.find(".filter_picker_widget")}}(this),this.opts.label_padding&&(this.label_padding=this.opts.label_padding),i=function(o){return function(){return n().removeClass("open popup_visible").find(".filter_options").css({marginTop:""})}}(this),$(window).on("click",function(o){return function(s){if(!$(s.target).closest(".filter_picker_widget").length)return i()}}(this)),this.el.on("i:close_filter_pickers",i),this.el.on("click",".filter_picker_widget .filter_value",function(o){return function(s){var a,u,l;return s.stopPropagation(),s.preventDefault(),u=$(s.currentTarget).closest(".filter_picker_widget"),n().not(u).removeClass("open popup_visible"),u.toggleClass("open"),u.is(".open")&&(u.trigger("i:track_link"),a=u.find(".filter_value").height(),u.find(".filter_options").css({marginTop:a+o.label_padding*2+"px",minWidth:u.width()+30+"px"}),l=$(window).width()-u.position().left+u.width(),u.toggleClass("popup_left",l<200)),_.defer(function(){return u.toggleClass("popup_visible",u.is(".open"))})}}(this))}return t}(),T=window.I;T.Lightbox||console.warn("I.Lightbox is being depended on but is missing from the page");var fe=T.Lightbox,_t=function(t,e){for(var r in e)mt.call(e,r)&&(t[r]=e[r]);function n(){this.constructor=t}return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},mt={}.hasOwnProperty,gt=function(t){_t(e,t);function e(){return e.__super__.constructor.apply(this,arguments)}return e.prototype.init=function(r){return this.el.dispatch("click",{tab_btn:function(n){return function(i){var o;return o=i.data("tab"),n.el.find(".tab_content").hide().filter("[data-tab="+o+"]").show(),n.el.find(".tab_btn").removeClass("selected").filter(i).addClass("selected")}}(this),pick_image_btn:function(n){return function(i){return typeof r=="function"?r(i.data("url")):void 0}}(this),upload_image_btn:function(n){return function(i){return T.upload_image({url:T.root_url("dashboard/upload-image"),thumb_size:"original"}).progress(function(){if(!i.prop("disabled"))return i.prop("disabled",!0).addClass("disabled"),i.data("original_text",i.text()),i.text("Uploading...")}).fail(function(){return i.prop("disabled",!1).removeClass("disabled"),i.text(i.data("original_text"))}).done(function(o){var s,a;return i.prop("disabled",!1).removeClass("disabled"),i.text(i.data("original_text")),o.success?typeof r=="function"?r(o.upload.thumb_url):void 0:(s=((a=o.errors)!=null?a[0]:void 0)||"Image upload failed",T.flash(s,"error"))})}}(this)})},e}(fe),de,Te=null,vt={plugins:["source","table","alignment","video","addimage"],toolbarFixed:!1,buttons:["format","bold","italic","deleted","lists","link"],minHeight:250,linkSize:80};de=!1;var wt=function(t){return de||(de=!0,DOMPurify.addHook("uponSanitizeElement",function(e,r,n){if(e.tagName==="IFRAME"&&e.innerHTML)return e.setAttribute("data-tmp-html",e.innerHTML),e.innerHTML=""}),DOMPurify.addHook("afterSanitizeElements",function(e,r,n){if(e.tagName==="IFRAME"&&e.getAttribute("data-tmp-html"))return e.innerHTML=e.getAttribute("data-tmp-html"),e.removeAttribute("data-tmp-html")})),DOMPurify.sanitize(t,{ADD_TAGS:["iframe"],ADD_ATTR:["width","height","frameborder","allowfullscreen","title","allow","scrolling","target"]})},yt=function(t,e){var r,n,i,o;if(e==null&&(e={}),!window.location.href.match(/\bredactor=0\b/)&&!T.in_test){if(!w.fn.redactor){console.warn("tried to create redactor text element without redactor on page",t[0]);return}e=w.extend({},vt,e),e.source===!1&&(delete e.source,e.plugins=function(){var s,a,u,l;for(u=e.plugins,l=[],s=0,a=u.length;s<a;s++)i=u[s],i!=="source"&&l.push(i);return l}()),t.closest(".lightbox_widget").exists()&&e.plugins&&(e.plugins=function(){var s,a,u,l;if(i!=="addimage"){for(u=e.plugins,l=[],s=0,a=u.length;s<a;s++)i=u[s],l.push(i);return l}}()),window.DOMPurify&&(r=t.val(),o=wt(r),o!==r&&t.val(o));try{return t.redactor(e)}catch(s){return n=s,T.event("error","redactor","invalid_content"),t.parent().replaceWith(t).end().val("").redactor(e)}}};w.Redactor?Te=w.Redactor:T.libs.redactor.done(function(){return Te=w.Redactor,w.Redactor.prototype.addimage=function(){return{langs:{en:{}},init:function(){var t;return t=this.button.addAfter("image","image","Add image"),this.button.setIcon(t,'<i class="re-icon-image"></i>'),this.button.addCallback(t,this.addimage.show)},show:function(){return fe.open_remote(T.root_url("dashboard/upload-image"),gt,function(t){return function(e){var r;return T.Lightbox.close(),r=w("<img>").attr("src",e)[0].outerHTML,t.placeholder.hide(),t.buffer.set(),t.insert.html(r)}}(this))}}}});var bt=function(t,e){for(var r in e)xt.call(e,r)&&(t[r]=e[r]);function n(){this.constructor=t}return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},xt={}.hasOwnProperty,Ee=T.CollectionLightbox=function(t){bt(e,t);function e(){return e.__super__.constructor.apply(this,arguments)}return e.prototype.init=function(){var r;return T.has_follow_button(this.el),this.el.find("input[type='radio']:first").prop("checked",!0),this.el.on("click change",".collection_option",function(n){return function(i){var o;return o=$(i.currentTarget),o.find("input[type='radio']").prop("checked",!0)}}(this)),r=this.el.find("form").remote_submit(function(n){return function(i){if(i.errors){if(T.add_recaptcha_if_necessary(r,i.errors))return;r.set_form_errors(i.errors);return}return n.el.addClass("is_complete"),n.el.find(".after_submit .collection_name").text(i.title).attr("href",i.url)}}(this)),this.with_redactor(function(n){return function(){return yt(n.el.find("textarea"),{minHeight:40,source:!1,buttons:["bold","italic","deleted","lists","link"]})}}(this)),this.with_selectize(function(n){return function(){return n.el.find("select.collection_input").selectize()}}(this))},e}(fe),kt=function(t,e){return function(){return t.apply(e,arguments)}},It=I.GamePopups=function(){t.prototype.trigger_selector=".game_thumb",t.prototype.popup_selector=".popup_details",t.prototype.hover_delay=250,t.prototype.close_timeout=200,t.prototype.x_offset=0;function t(e,r){var n;r==null&&(r={}),this.deferred_for_el=kt(this.deferred_for_el,this),!I.is_mobile()&&(this.el=w(e),w.extend(this,r),n=w(document.body),this.el.on("mouseenter mouseleave",this.trigger_selector,function(i){return function(o){if(e=w(o.currentTarget),!e.closest(".jam_cell").length&&!e.closest(".disable_hover").length)return o.type==="mouseenter"?i.enter(e):i.leave(e)}}(this)),n.on("i:hide_popups",function(i){return function(o,s){return i.close_popup()}}(this)),n.on("i:hide_other_popups",function(i){return function(o,s){if(s!==i)return i.close_popup()}}(this)))}return t.prototype.deferred_for_el=function(e){var r,n,i;return(r=e.data("i:popup_defer"))?r:(n=e.closest("[data-game_id]").data("game_id"),n?(i=I.subdomain?"/-/game/popup/"+n:"/game/popup/"+n,r=w.get(i).then(function(o){return function(s){var a;return _.isObject(s)&&s.errors?w.Deferred():(a=w(s),a.data("object_id",n),new I.GamePopup(a),a)}}(this)),e.data("i:popup_defer",r),r):w.Deferred())},t.prototype.enter=function(e){var r,n;return e.data("i:inside",!0),n=_.debounce(function(i){return function(){if(e.data("i:inside"))return i.deferred_for_el(e).done(function(o){return i.show_popup(e,o)})}}(this),this.hover_delay),r=_.debounce(function(i){return function(){if(e.data("i:inside"))return i.deferred_for_el(e)}}(this),this.hover_delay/2),e.on("mousemove.hover_manager",function(){return n(),r()})},t.prototype.leave=function(e){return e.data("i:inside",!1),e.off("mousemove.hover_manager")},t.prototype.show_popup=function(e,r){var n,i,o,s,a,u,l,c;if(typeof(n=document.body).contains=="function"&&n.contains(e[0])&&this.current_popup!==r){for(this.el.trigger("i:hide_other_popups",[this]),(u=this.current_popup)!=null&&u.stop().detach(),this.current_popup=r.css({left:"0px",top:"0px"}).removeClass("visible"),c=r.find(".popup_screenshot").removeClass("visible"),w(document.body).append(r),this.position_popup(e,r),_.defer(function(d){return function(){return r.addClass("visible")}}(this)),r.data("trigger_el",e),i=function(d){return function(h){return setTimeout(function(){return h.addClass("visible")},o*100)}}(this),o=s=0,a=c.length;s<a;o=++s)l=c[o],i(w(l));return r.hide().css({opacity:""}).fadeIn("fast"),this.watch_for_popup_close()}},t.prototype.watch_for_popup_close=function(){var e,r;return r=this.trigger_selector+", "+this.popup_selector,e=null,w(document.body).on("click.hover_manager",function(n){return function(i){var o;if(o=w(i.target),!o.closest(r).length)return n.close_popup()}}(this)),w(window).on("mousemove.hover_manager",_.throttle(function(n){return function(i){var o;if(o=w(i.target).closest(r),o.length){e&&(window.clearTimeout(e),e=null);return}if(!e)return e=window.setTimeout(function(){return n.close_popup()},n.close_timeout)}}(this),50))},t.prototype.position_popup=function(e,r){var n,i,o,s,a,u,l;return o=e.offset(),l=r.outerWidth(!0),n=r.outerHeight(!0),r.removeClass("on_right"),s=e.outerHeight(),a=e.outerWidth(),u=o.top+(s-n)/2,i=o.left-l,i<0?(i=o.left+a,i+=this.x_offset,r.addClass("on_right")):i-=this.x_offset,r.css({top:Math.floor(u)+"px",left:Math.floor(i)+"px"})},t.prototype.close_popup=function(){var e;return w(window).off("mousemove.hover_manager"),w(document.body).off("click.hover_manager"),(e=this.current_popup)!=null&&e.stop(!0).detach(),this.current_popup=null},t}(),Xt=I.GamePopup=function(){function t(e){this.el=w(e),I.tracked_links(this.el,"popups",I.page_name(),"click"),this.el.find("a").on("click",function(r){return function(){var n;if(n=r.el.data("trigger_el"))return n.trigger("i:track_link")}}(this)),this.el.dispatch("click",{watch_trailer_btn:function(r){return I.event("grid",I.page_name(),"watch_trailer"),I.Lightbox.open_remote(r.data("lightbox_url"))}})}return t}(),Qt=I.WatchTrailerLightbox=function(){function t(e){this.el=w(e),this.el.dispatch("click",{add_to_collection_btn:function(r){return function(n){return I.event("grid","trailer","add_to_collection"),I.current_user?I.Lightbox.open_remote(n.attr("href"),Ee):"continue"}}(this)})}return t}(),q=window._,he=[].slice,H=T.ConversionTracker||(T.ConversionTracker=function(){function t(){}return t.types={impression:1,click:2,purchase:3,download:4,join:5},t.buffer=[],t.find_click=function(e){var r,n,i,o,s,a;for(a=new RegExp(":"+e+"$"),r=this.get_cookie(),i=0,s=r.length;i<s;i++)if(o=r[i],n=q.isArray(o)?o[0]:o,n.match(a))return o},t.strip_click=function(e){var r,n,i,o,s,a;return s=new RegExp(":"+e+"$"),r=this.get_cookie(),a=0,o=function(){var u,l,c;for(c=[],u=0,l=r.length;u<l;u++){if(i=r[u],n=q.isArray(i)?i[0]:i,n.match(s)){a+=1;continue}c.push(i)}return c}(),o.length===r.length?0:(this.write_cookie(o),a)},t.after_click_action=function(e,r){var n,i,o,s;if(n=this.find_click(r),!!n)return s=null,o=q.isArray(n)?(s=n[1],n[0]):n,i=o.replace(/^\d+/,this.types[e]),this.strip_click(r),this.push(i,s),!0},t.download=function(e){return this.after_click_action("download",e)},t.purchase=function(e){return this.after_click_action("purchase",e)},t.join=function(e){return this.after_click_action("join",e)},t.click=function(e){var r,n,i,o,s,a;i=this.types.click+":"+e,o=(a=this.get_active_splits())?[i,a]:i,this.push(i);try{for(n=function(){var u,l,c,d;for(c=this.get_cookie(),d=[],u=0,l=c.length;u<l;u++)r=c[u],s=q.isArray(r)?r[0]:r,s!==i&&d.push(r);return d}.call(this),n.push(o);n.length>100;)n.shift();return this.write_cookie(n),!0}catch(u){}},t.write_cookie=function(e){return T.set_cookie("itchio_ca",JSON.stringify(e),{expires:1})},t.get_cookie=function(){var e;if(e=window.Cookies.get("itchio_ca"),e)try{return JSON.parse(e)||[]}catch(r){try{return JSON.parse(decodeURIComponent(e))||[]}catch(n){return[]}}else return[]},t.flush_later=function(){return this.flush_later=q.throttle(this.flush_now,2e3,{leading:!1}),$(window).on("beforeunload",function(e){return function(){e.flush_now()}}(this)),this.flush_later()},t.encode_buffer=function(e){var r,n,i,o,s,a,u,l,c,d,h,g,x,m,y;for(e==null&&(e=he.call(this.buffer)),e.sort(),h=[],u=null,a=null,s=null,i=0,l=e.length;i<l;i++)n=e[i],x=n.match(/^(\d+):(\d+):(\d+):(\d+)$/),r=x[0],y=x[1],m=x[2],d=x[3],c=x[4],y&&(y!==u&&(h.push("t"+y),u=y),m!==a&&(h.push("s"+m),a=m),d!==s&&(h.push("o"+d),s=d),o=(+c).toString(36),g=String.fromCharCode("A".charCodeAt(0)+o.length),h.push(""+g+o));return h.join("")},t.get_active_splits=function(){return T.active_splits},t.flush_url=function(e){var r,n,i;return i=this.encode_buffer(),r=[{name:"x",value:i}],(n=e||this.get_active_splits())&&r.push({name:"s",value:n.join(",")}),T.root_url("ca.gif")+"?"+$.param(r)},t.flush_now_beacon=function(e){var r;if(navigator.sendBeacon==null)return this.flush_now(e);if(this.buffer.length)if(T.in_dev&&console.debug.apply(console,["ca(beacon)"].concat(he.call(q.compact([this.buffer,e])))),r=this.flush_url(e),navigator.sendBeacon(r))this.buffer=[];else return this.flush_now();return $.when()},t.flush_now=function(e){var r;return this.buffer.length?(T.in_dev&&console.debug.apply(console,["ca"].concat(he.call(q.compact([this.buffer,e])))),r=this.flush_url(e),this.buffer=[],$.Deferred(function(n){return function(i){var o,s;return s=new Image,s.src=r,o=function(){return i.resolve()},s.onerror=o,s.onload=o}}(this))):$.when()},t.push=function(e,r){var n;return this.buffer=function(){var i,o,s,a;for(s=this.buffer,a=[],i=0,o=s.length;i<o;i++)n=s[i],n!==e&&a.push(n);return a}.call(this),this.buffer.push(e),this.buffer.length>50||r?this.flush_now(r):this.flush_later()},t}()),Me,Se,re=function(t,e){return function(){return t.apply(e,arguments)}};Me=0,Se=function(){return"game_grid_"+Me++},I.GameGridSizer=function(){t.prototype.container_selector=!1,t.prototype.expected_size=315,t.prototype.aspect_ratio=315/250,t.prototype.cell_margin=10,t.prototype.min_columns=1,t.initialize=_.once(function(){var e,r,n;return e=w(document.body),r=_.debounce(function(){return e.addClass("disable_hover")},200,!0),n=_.debounce(function(){return e.removeClass("disable_hover")},200),w(window).on("scroll resize",function(){r(),n()})});function t(e){e==null&&(e={}),w.extend(this,e),this.size_callbacks=[],this.constructor.initialize(),w(window).on("resize",_.debounce(function(r){return function(){return r.resize_cells()}}(this),200)),this.resize_cells(),I.in_test||_.defer(function(r){return function(){return r.resize_cells()}}(this))}return t.prototype.get_cell_margin=function(){return typeof this.cell_margin=="function"?this.cell_margin():this.cell_margin},t.prototype.get_expected_size=function(){return typeof this.expected_size=="function"?this.expected_size():this.expected_size},t.prototype.on_size=function(e){return this.size_callbacks.push(e)},t.prototype.available_width=function(){var e,r,n,i;return this.el?(e=(i=this.el[0])!=null?i.getBoundingClientRect().width:void 0,n=e-Math.floor(e),r=Math.floor(this.el.width()),n>0&&(r-=1),r):w(window).width()},t.prototype.resize_cells=function(){var e,r,n,i,o,s,a,u,l,c,d,h,g,x;for(i=this.get_expected_size(),r=this.get_cell_margin(),h=i+r,c=this.available_width()+r,l=c/h,d=Math.ceil(l),d<this.min_columns&&(d=this.min_columns),u=c/d-r,a=u/this.aspect_ratio,u=Math.floor(u),a=Math.floor(a),this.cells_per_row=d,this._style&&(this._style.remove(),this._style=null),n="",this.container_selector&&(n+=this.container_selector+` {
  --itchio_grid_gap: `+r+`px;
  --itchio_grid_cell_width: `+u+`px;
  --itchio_grid_cell_columns: `+d+`;
  grid-template-columns: repeat(`+d+", "+u+`px);
}`),n!==""&&(this._style=w("<style itchio-grid-sizer type='text/css'>"+n+"</style>").appendTo(w("head"))),g=this.size_callbacks,x=[],o=0,s=g.length;o<s;o++)e=g[o],x.push(e(this,u,a));return x},t}(),I.GameCells=function(){function t(e){this.setup_game_tools=re(this.setup_game_tools,this),this.el=w(e),this.setup_game_tools(),this.setup_gifs()}return t.prototype.setup_game_tools=function(){return this.el.dispatch("click",{add_to_collection_btn:function(e){return function(r){return I.event("grid",I.page_name(),"add_to_collection"),I.current_user?at.open_remote(r.attr("href"),Ee):"continue"}}(this)})},t.prototype.setup_gifs=function(){return this.el.on("mouseenter",".game_cell",function(e){return function(r){var n,i;if(n=w(r.currentTarget),!n.data("grid_hovered")&&(n.data("grid_hovered",!0),i=n.find(".gif_overlay"),!!i.length))return i.css("background-image","url("+i.data("gif")+")")}}(this))},t}(),I.GameGrid=function(){function t(e,r){var n,i,o,s,a;if(r==null&&(r={}),this.refresh_images=re(this.refresh_images,this),this.add_image_loading=re(this.add_image_loading,this),this.setup_conversion_tracking=re(this.setup_conversion_tracking,this),this.el=w(e),w.extend(this,r),this.setup_lazy_images(),this.show_popups!==!1&&new It(this.el),new I.GameCells(this.el),!this.sizer&&this.sizer!==!1){for(n=["container_selector","expected_size","min_columns","cell_margin"],this.el[0]&&!this.el[0].id&&(this.el[0].id=Se()),a={el:this.el,container_selector:"#"+this.el[0].id},o=0,s=n.length;o<s;o++)i=n[o],i in r&&(a[i]=r[i]);this.sizer=new I.GameGridSizer(a),this.sizer.on_size(function(u){return function(){return u.el.trigger("i:grid_resize"),u.el.lazy_images()}}(this))}}return t.prototype.setup_lazy_images=function(){return _.defer(function(e){return function(){return e.add_image_loading()}}(this))},t.prototype.setup_conversion_tracking=function(e){return this.el.on("i:impression",function(r){return function(n){var i,o;if(i=w(n.target).closest("[data-game_id]"),o=i.data("game_id"),!!o)return H.push("1:"+e+":1:"+o)}}(this)),this.el.on("i:delegate_tracking",function(r){return function(n,i){var o;if(o=w(n.target).closest("[data-game_id]").data("game_id"),!!o)return H.click(e+":1:"+o),i(H.flush_now())}}(this))},t.prototype.add_image_loading=function(){return this.el.lazy_images({show_item:function(e){return function(r){return r.trigger("i:impression"),r.find(".image_loading").addBack(".image_loading").removeClass("image_loading")}}(this)})},t.prototype.refresh_images=function(){var e;return typeof(e=this.el.data("lazy_images"))=="function"?e():void 0},t}();var ne,pe,V,ze,_e,me,ie,Ne,W,De=[].slice,Le={}.hasOwnProperty;W={},ie={},ne="en",V=(Ne=window.itchio_locale)!=null?Ne:ne,ze=window.requestAnimationFrame||_.defer,_e=function(t,e,r){var n,i,o,s,a,u,l;for(o=0,s=e.length;o<s;o++)if(a=e[o],_.isString(a))t.push(a);else if(a[0]==="v")l=r[a[1]],l||console.warn("i18n: failed to find value for variable "+a[1]),t.push(l);else if(a[0]==="t"){if(i=r[a[1]],typeof i!="function")throw new Error("i18n: tried to interpolate tag without function: "+a[1]);n=a[2],u=[],_e(u,n||[],r),t.push(i(u.join("")))}else throw new Error("i18n: unknown interpolation node: "+JSON.stringify(a));return t},me=function(t,e){var r,n,i,o;return _.isString(t)?t:(n=function(){var s,a,u;for(u=[],s=0,a=t.length;s<a;s++)if(i=t[s],_.isString(i))u.push(i);else if(i[0]==="v")o=e[i[1]],o||console.warn("i18n: failed to find value for variable "+i[1]),u.push(o);else if(i[0]==="t"){if(r=e[i[1]],typeof r!="function")throw new Error("i18n: tried to interpolate tag without function: "+i[1]);u.push(r(me(i[2],e)))}else throw new Error("i18n: unknown interpolation node: "+JSON.stringify(i));return u}(),n.length>1?React.createElement.apply(React,[React.Fragment,{}].concat(De.call(n))):n.length===0?null:n)},pe=function(){t._cache={},t.for_locale=function(e){var r;return(r=this._cache)[e]||(r[e]=new t(e))};function t(e){this.locale=e,this.last_fetch=null,this.next_fetch=null}return t.prototype.fetch=function(e,r){return this.next_fetch?(this.next_fetch=this.next_fetch.concat(e),this.last_fetch):(this.next_fetch=e,this.last_fetch=$.Deferred(function(n){return function(i){return ze(function(){var o,s;if(!window.itchio_translations_url){console.error("missing translations url: itchio_translations_url");return}return o=n.next_fetch,s=$.get(window.itchio_translations_url,{locale:n.locale,prefixes:n.next_fetch.join(",")}),n.next_fetch=null,s.done(function(a){var u,l;for(u in a)Le.call(a,u)&&(l=a[u],!r[u]&&(r[u]=l));return i.resolve()})})}}(this)))},t}(),I.i18n={Preloader:pe,get_translations:function(){return W},get_cache:function(){return ie},get_locale:function(){return V},locale_is_english:function(){return V==="en"},insert:function(t){var e,r,n;r=[];for(e in t)Le.call(t,e)&&(n=t[e],!W[e]&&r.push(W[e]=n));return r},set_locale:function(t){return V=t,W={},ie={}},render_string:function(t,e,r){var n;if(_.isString(e))return r&&console.warn("i18n: provided variables to translation string with no interpolation: "+t),e;if(n=[],!r)throw new Error("i18n: tried to interpolate "+t+" without variables");return _e(n,e,r),n.join("")},t:function(t,e){return I.i18n.fetch_key(t).then(function(r){return function(n){return I.i18n.render_string(t,n,e)}}(this))},t_sync:function(t,e){var r;if(r=I.i18n.fetch_key_sync(t),!r)throw new Error("i18n: failed to fetch key sync: "+t);return I.i18n.render_string(t,r,e)},fetch_key_sync:function(t){return V==="debug"?"[["+t+"]]":W[t]},fetch_key:function(t){var e,r;if(V==="debug")return $.when("[["+t+"]]");if(e=t.split("."),e.pop(),!e.length)throw new Error("i18n: empty prefix");return r=e.join("."),I.i18n.preload(r).then(function(n){return function(){var i;if(i=W[t],i)return i;if(V===ne)throw new Error("i18n: failed to find key: "+t);return I.i18n.preload(r,ne).then(function(){if(i=W[t],!i)throw new Error("i18n: failed to find key: "+t);return i})}}(this))},preload:function(t,e){var r,n,i,o,s,a,u;if(e==null&&(e=V),e!=="debug"){if(r=ie,_.isString(t)&&(t=[t]),u=function(){var l,c,d;for(d=[],l=0,c=t.length;l<c;l++)a=t[l],r[e]||(r[e]={}),!r[e][a]&&d.push(a);return d}(),n=function(){var l,c,d,h;for(h=[],l=0,c=t.length;l<c;l++)a=t[l],((d=r[e][a])!=null?d.state():void 0)==="pending"&&h.push(r[e][a]);return h}(),!u.length)return $.when.apply($,n);for(i=pe.for_locale(e).fetch(u,W),o=0,s=u.length;o<s;o++)a=u[o],r[e][a]||(r[e][a]=i);return n.length?$.when.apply($,[i].concat(De.call(n))):i}},react_class:function(t){return createReactClass({displayName:"i18n:"+t,getInitialState:function(){var e;return e=function(){var r;return(r=I.i18n.fetch_key_sync(t))?{text:r}:null},e.isReactClassApproved=!0,e}(),componentWillUnmount:function(){return this.unmounted=!0},componentDidMount:function(){if(!(this.state&&this.state.text))return I.i18n.fetch_key(t).done(function(e){return function(r){if(!e.unmounted)return e.setState({text:r})}}(this))},render:function(){return this.state?_.isString(this.state.text)?this.state.text:me(this.state.text,this.props):"\u2026"}})}};var Zt=be(xe()),Oe,J;J=null,Oe=function(t){var e,r,n,i,o,s,a,u,l,c,d,h,g,x,m,y,f,p,v,b,C,j,M,Y,O;return v=ReactDOMFactories.table,j=ReactDOMFactories.thead,b=ReactDOMFactories.tbody,O=ReactDOMFactories.tr,C=ReactDOMFactories.td,e=ReactDOMFactories.abbr,n=ReactDOMFactories.details,p=ReactDOMFactories.summary,s=ReactDOMFactories.div,u=ReactDOMFactories.h2,r=ReactDOMFactories.code,h=ReactDOMFactories.pre,d=ReactDOMFactories.p,l=ReactDOMFactories.input,i=ReactDOMFactories.dialog,c=ReactDOMFactories.label,M=t.query_log.length,Y=t.query_log.reduce(function(z,S){return z+S[1]},0),x=React.useState(t.query_log),g=x[0],f=x[1],m=React.useState(!1),a=m[0],y=m[1],o=React.useRef(null),React.useEffect(function(z){return function(){var S;return(S=o.current)!=null?S.showModal():void 0}}(this),[]),i({ref:o,className:"perf_query_log_widget",onClick:function(z){var S,D,P;S=(D=o.current)!=null?D.getBoundingClientRect():void 0,S&&(z.clientX<S.left||z.clientX>S.right||z.clientY<S.top||z.clientY>S.bottom)&&(P=o.current)!=null&&P.close()},style:{width:"100%",maxWidth:"800px"}},u({style:{margin:"0"}},"Query log"),d({style:{margin:"0"}},"Queries: ",r({},M),", Time: ",r({},(Y*1e3).toFixed(2)+"ms")),s({style:{display:"flex",gap:"10px",alignItems:"center"}},l({type:"text",placeholder:"Filter queries...",style:{width:"auto",flex:"auto",borderSizinng:"border-box"},onChange:function(z){return function(S){var D;return D=S.target.value.toLowerCase().split(/\s+/),f(t.query_log.filter(function(P){var B,te;return B=P[0],te=B.toLowerCase(),D.every(function(Vt){return te.includes(Vt)})}))}}(this)}),c({style:{display:"flex",alignItems:"center"}},"Slow",l({type:"checkbox",value:a,onChange:function(z){return function(S){return y(S.target.checked)}}(this)}))),v({className:"nice_table",style:{width:"100%"}},j({},O({},C({},"Query"),C({},"Timing"))),b({},g.filter(function(z){var S,D;return S=z[0],D=z[1],a?D>.01:!0}).map(function(z){return function(S,D){var P,B,te;return P=S[0],te=S[1],B=te*1e3,O({key:D},C({},n({},p({},r({style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",display:"inline-block",maxWidth:"600px"}},P)),h({style:{whiteSpace:"pre-wrap"}},P))),C({},r({style:{fontWeight:B>10?"bold":void 0,color:B>100?"white":B>50?"red":void 0,backgroundColor:B>100?"red":void 0}},B.toFixed(2)+"ms")))}}(this)))))},I.PerfPanel=function(){function t(e){var r;e.layout_time||(e.layout_time=0),e.view_time||(e.view_time=0),e.db_time||(e.db_time=0),e.db_count||(e.db_count=0),r=_.template(`<div class="perf_panel">
  <div class="stat_row">
    <strong>Queries:</strong> {{ db_count }}
  </div>

  <div class="stat_row">
    <strong>Query time:</strong> {{ Math.floor(db_time * 1000) }}ms
  </div>

  <div class="stat_row">
    <strong>View time:</strong> {{ Math.floor((view_time + layout_time) * 1000) }}ms
  </div>

  <div class="stat_row">
    <strong>Rest:</strong> {{ Math.floor((total_time - (view_time + layout_time + db_time)) * 1000) }}ms
  </div>

</div>`),this.data=e,this.el=$(r(e)).appendTo(document.body),this.el.on("click",function(n){return function(){return n.render_queries()}}(this))}return t.prototype.render_queries=function(){var e,r,n;if(this.data.query_log)return r=function(){var i,o,s,a,u;for(s=this.data.query_log,u=[],i=0,o=s.length;i<o;i++)a=s[i],e=a[0],n=a[1],u.push({query:e,time:n*1e3});return u}.call(this),console.table(r),I.add_react().done(function(i){return function(){var o,s;return o=0,s=React.createElement(Oe,{query_log:i.data.query_log}),J||(J=document.createElement("div"),J.classList.add("perf_lightbox_container"),document.body.appendChild(J)),ReactDOM.render(null,J),ReactDOM.render(s,J)}}(this))},t}(),I.SaleList=function(){function t(e){this.el=$(e),this.el.lazy_images({})}return t}();var oe,Pe,Q=function(t,e){return function(){return t.apply(e,arguments)}},Fe=function(t,e){for(var r in e)$t.call(e,r)&&(t[r]=e[r]);function n(){this.constructor=t}return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},$t={}.hasOwnProperty,qe=function(t){var e,r;return r=function(n){return n.errors?$.Deferred().reject(n):n},e=function(n){return $.Deferred().reject({errors:["Server error ("+n.status+")","Please contact support if the error persists"]})},t.then(r,e)};I.prepare_upload=function(t,e){return $.when(e).then(function(r){return qe($.ajax({url:t+"/upload/prepare",type:"post",data:I.with_csrf(r),dataType:"json"}))})},Pe=function(t){var e,r,n,i,o;return e=new DataView(t,0,5),n=e.getUint8(0,!0),i=e.getUint8(1,!0),r=n.toString(16)+i.toString(16),o={8950:"image/png",4749:"image/gif","424d":"image/bmp",ffd8:"image/jpeg"},o[r]},oe="You selected an image type we don't recognize. It's possible it has the wrong file extension for the format it is saved as. Please use an image editing program to convert it to a PNG, JPEG, or GIF.",I.test_image_format=function(t){return $.Deferred(function(e){return function(r){var n;return window.FileReader?(n=new FileReader,n.readAsArrayBuffer(t),n.onerror=function(){return r.reject("Failed to read image from disk")},n.onload=function(){var i;return i=Pe(n.result),i==="image/bmp"?r.reject("You selected a BMP file that has a wrong extension. Please use an image editing program to convert it to a PNG, JPEG, or GIF."):i?r.resolve():r.reject(oe)}):r.resolve()}}(this))},I.image_dimensions=function(t){return $.Deferred(function(e){var r,n;return n=typeof URL!="undefined"&&URL!==null&&typeof URL.createObjectURL=="function"?URL.createObjectURL(t):void 0,n?(r=new Image,r.src=n,r.onload=function(){return e.resolve([r.width,r.height])},r.onerror=function(){return e.reject(oe)}):e.reject(oe)})},I.video_dimensions=function(t){return $.Deferred(function(e){var r,n;return n=document.createElement("video"),r=typeof URL!="undefined"&&URL!==null&&typeof URL.createObjectURL=="function"?URL.createObjectURL(t):void 0,r?(n.src=r,n.onloadedmetadata=function(){return e.resolve([n.videoWidth,n.videoHeight])},n.onerror=function(){return e.reject("Invalid video file")}):e.reject("Invalid video file")})},I.test_image_dimensions=function(t,e,r){return e==null&&(e=3840),r==null&&(r=2160),I.image_dimensions(t).then(function(n){return function(i){var o,s;return s=i[0],o=i[1],$.Deferred(function(a){return s>e||o>r?a.reject("Image is greater than the maximum dimensions of "+e+"x"+r+" (you selected a "+s+"x"+o+" image)"):a.resolve()})}}(this))};var er=I.Upload||(I.Upload=function(){t.prototype.kind="upload";function t(e,r){this.file=e,this.opts=r!=null?r:{},this.save_upload=Q(this.save_upload,this),this.decrement=Q(this.decrement,this),this.increment=Q(this.increment,this)}return t.prototype.upload_params=function(){return{kind:this.kind,filename:this.file.name}},t.prototype.progress=function(e,r){},t.prototype.save_params=function(){return{}},t.prototype.save_url=function(){return this.upload_data.success_url},t.prototype.start_upload=function(){if(this.upload_data){console.warn("Attempt to start_upload when upload has already been started");return}return this.prepare_upload().then(function(e){return function(){return e.start_xhr_upload()}}(this)).then(function(e){return function(){return e.save_upload()}}(this))},t.prototype.increment=function(){var e,r;if((e=this.constructor).active_uploads||(e.active_uploads=0),this.constructor.active_uploads++,this.constructor.active_uploads===1)return typeof(r=this.opts).start_uploading=="function"?r.start_uploading():void 0},t.prototype.decrement=function(){var e;if(this.constructor.active_uploads--,this.constructor.active_uploads===0)return typeof(e=this.opts).stop_uploading=="function"?e.stop_uploading():void 0},t.prototype.prepare_upload=function(){return $.when(this.opts.upload_prefix||this.upload_prefix).then(function(e){return function(r){return I.prepare_upload(r,e.upload_params()).done(function(n){e.upload_data=n})}}(this))},t.prototype.start_xhr_upload=function(){var e,r,n,i,o,s;if(!this.upload_data)throw"missing upload data";this.increment(),e=$.Deferred().always(function(a){return function(){return a.decrement()}}(this)),r=new FormData,i=this.upload_data.post_params;for(n in i)o=i[n],r.append(n,o);return r.append("file",this.file),s=new XMLHttpRequest,s.upload.addEventListener("progress",function(a){return function(u){if(u.lengthComputable)return a.progress(u.loaded,u.total)}}(this)),s.upload.addEventListener("error",function(a){return function(u){return I.event("upload","xhr error",a.kind)}}(this)),s.upload.addEventListener("abort",function(a){return function(u){return I.event("upload","xhr abort",a.kind)}}(this)),s.addEventListener("readystatechange",function(a){return function(u){var l;if(s.readyState===4){if(Math.floor(s.status/100)===2)return I.event("upload","save",a.kind),e.resolve();if(l="Failed upload.",s.responseXML)try{l=s.responseXML.querySelector("Error Message").innerHTML}catch(c){u=c}else l=s.responseText;return I.event("upload_error","server error "+s.status+": "+l,a.kind),e.reject({errors:[l]})}}}(this)),s.open("POST",this.upload_data.action),s.send(r),e},t.prototype.save_upload=function(){if(!this.upload_data){console.warn("attempted to call save_upload without upload_data");return}return qe($.ajax({url:this.save_url(),data:I.with_csrf(this.save_params()),dataType:"json",type:"post"}))},t}()),tr=I.ImageUpload=function(t){Fe(e,t);function e(){return e.__super__.constructor.apply(this,arguments)}return e.prototype.kind="image",e.prototype.upload_params=function(){return $.when(e.__super__.upload_params.call(this)).then(function(r){return function(n){return I.image_dimensions(r.file).then(function(i){var o,s;return s=i[0],o=i[1],Object.assign({width:s,height:o},n)},function(){return n})}}(this))},e}(I.Upload),rr=function(t){Fe(e,t),e.prototype.stage_delay=500,e.prototype.mock_prepare_result={id:123},e.prototype.mock_save_result={success:!0};function e(){this.save_upload=Q(this.save_upload,this),this.start_xhr_upload=Q(this.start_xhr_upload,this),this.prepare_upload=Q(this.prepare_upload,this),e.__super__.constructor.apply(this,arguments),console.warn("MockUpload:new "+this.file.name)}return e.prototype.prepare_upload=function(){return console.warn("MockUpload: prepare_upload"),$.when(this.opts.upload_prefix()).then(function(r){return function(){return $.Deferred(function(n){return setTimeout(function(){return r.upload_data=r.mock_prepare_result,n.resolve(r.upload_data)},r.stage_delay)})}}(this))},e.prototype.start_xhr_upload=function(){var r,n,i,o,s,a;return console.warn("MockUpload: start_xhr_upload"),r=20,i=(o=this.file.size)!=null?o:1234,n=0,a=0,s=this.stage_delay/r*5,$.Deferred(function(u){return function(l){var c;return u.progress(0,i),c=function(){return setTimeout(function(){return a+=1,u.progress(Math.floor(a/r*i),i),a===r?l.resolve():c()},s)},c()}}(this))},e.prototype.save_upload=function(){return console.warn("MockUpload: save_upload"),$.Deferred(function(r){return function(n){return setTimeout(function(){return n.resolve(r.mock_save_result)},r.stage_delay)}}(this))},e}(I.Upload);I.pick_files=function(t){var e;return t==null&&(t={}),e=t.input?$(t.input):($("input.pick_files_input").remove(),$("<input type='file' class='pick_files_input' />").hide().insertAfter("body")),$.Deferred(function(r){return function(n){return t.multiple&&e.attr("multiple",!0),t.accept&&e.attr("accept",t.accept),e.on("change",function(i){var o,s,a,u,l,c,d;for(l=function(){var h,g,x,m;for(x=i.target.files,m=[],h=0,g=x.length;h<g;h++)a=x[h],m.push(a);return m}(),s=[],typeof t.on_pick_files=="function"&&t.on_pick_files(l),c=0,d=l.length;c<d;c++)u=l[c],t.max_size&&u.size>t.max_size&&s.push(["Image is greater than the max file size "+I.format_bytes(t.max_size)+" (you selected a "+I.format_bytes(u.size)+" file)",u]);if(s.length){n.reject(s,l);return}return t.test_file?(o=l.map(function(h){return $.Deferred(function(g){return t.test_file(h,g),setTimeout(function(){if(n.state()==="pending")return g.reject("Timed out checking file, are you sure it was an image file?")},1e3)}).catch(function(g){throw[g,h]})}),$.when.apply($,o).done(function(h){return n.resolve(l)}).fail(function(h){return n.reject([h])})):n.resolve(l)}),e.click()}}(this))},I.xhr_upload=function(t,e){return $.Deferred(function(r){return function(n){var i,o,s,a,u;i=new FormData,s=e.post_params;for(o in s)a=s[o],i.append(o,a);return i.append("file",t),u=new XMLHttpRequest,u.upload.addEventListener("progress",function(l){if(l.lengthComputable)return n.notify("progress",l.loaded,l.total)}),u.upload.addEventListener("error",function(l){return n.reject("xhr error")}),u.upload.addEventListener("abort",function(l){return n.reject("xhr aborted")}),u.addEventListener("readystatechange",function(l){var c;if(u.readyState===4){if(Math.floor(u.status/100)===2)return n.resolve();if(c="Failed upload.",u.responseXML)try{c=u.responseXML.querySelector("Error Message").innerHTML}catch(d){l=d}else c=u.responseText;return n.reject(c)}}),u.open("POST",e.action),u.send(i)}}(this))},I.upload_image=function(t){return t==null&&(t={}),t.accept||(t.accept="image/png,image/jpeg,image/gif"),t.file_params=function(e){return e.type==="video/mp4"?I.video_dimensions(e).then(function(r){return function(n){var i,o;return o=n[0],i=n[1],{width:o,height:i}}}(this)):I.image_dimensions(e).then(function(r){return function(n){var i,o;return o=n[0],i=n[1],{width:o,height:i}}}(this))},I.upload_file(t)};var Ct=function(t,e){var r,n,i;return e==null&&(e={}),n=e.on_start_upload,i=e.test_file,r=e.file_params?e.file_params(t):{},$.when(r).then(function(o){return function(s){return $.ajax({url:e.url,type:"post",xhrFields:{withCredentials:!0},data:I.with_csrf(Object.assign({filename:t.name,thumb_size:e.thumb_size,action:"prepare"},s))}).then(function(a){var u;return u=I.xhr_upload(t,a).then(function(){return $.ajax({url:a.success_url,type:"post",xhrFields:{withCredentials:!0},data:I.with_csrf()})}),typeof n=="function"&&n(t,a,u),u})}}(this))};I.upload_file=function(t){var e,r,n,i,o;if(t==null&&(t={}),!t.url)throw new Error("missing url for upload image");return e=t.accept,r=t.max_size,n=t.multiple,i=t.on_pick_files,o=t.test_file,I.pick_files({accept:e,max_size:r,multiple:n,test_file:o,on_pick_files:i}).then(function(s){return function(a){var u,l;if(!n&&a.length>1)throw"Got multiple files for single upload";return l=function(){var c,d,h,g;for(h=a.slice(0,6),g=[],c=0,d=h.length;c<d;c++)u=h[c],g.push(Ct(u,t));return g}(),n?$.when.apply($,l):l[0]}}(this))},I.make_upload_button=function(t,e){var r,n;return r=null,n=t.data("max_size"),t.on("click",function(i){return function(o){var s;return r&&r.remove(),r=$("<input type='file' multiple />").hide().insertAfter(t),(s=t.data("accept"))&&r.attr("accept",s),r.on("change",function(){var a,u,l,c,d;for(c=r[0].files,d=[],u=0,l=c.length;u<l;u++){if(a=c[u],n!=null&&a.size>n){I.flash(a.name+" is greater than max size of "+I.format_bytes(n),"error");continue}d.push(typeof e=="function"?e(a):void 0)}return d}),r.insertAfter(t),r.click()}}(this))},I.VideoEmbed=function(){function t(e){this.el=$(e),this.el.lazy_images({selector:".video_drop",show_item:function(r){return function(n){return n.replaceWith(n.data("template"))}}(this)})}return t}();var k=window.React,Rt=window.ReactDOM,A=window.ReactDOMFactories,jt=window.ReactTransitionGroup,E=window.PropTypes,N=window.classNames,U=window.R;k||T.libs.react.done(function(){return k=window.React,Rt=window.ReactDOM,A=window.ReactDOMFactories,jt=window.ReactTransitionGroup,E=window.PropTypes,N=window.classNames,U=window.R});var G=function(t){return T.libs.react.done(t)};G(function(){var t,e,r,n,i,o,s;return t=A,r=t.button,o=t.span,n=t.img,e=t.a,i=t.label,s=PropTypes,R("FollowButton",{propTypes:{following:s.bool,show_name:s.bool,name:s.string,login_url:s.string,user_id:s.number,follow_data:s.object,follow_url:s.string.isRequired,unfollow_url:s.string.isRequired},getInitialState:function(){return{following:this.props.following}},getDefaultProps:function(){return{show_name:!0}},componentDidUpdate:function(a,u){if(!u.animate_class&&this.state.animate_class)return this.timer&&(window.clearTimeout(this.timer),delete this.timer),this.timer=setTimeout(function(l){return function(){return l.setState({animate_class:null})}}(this),500)},componentWillUnmount:function(){if(this.timer)return window.clearTimeout(this.timer),delete this.timer},render:function(){var a;return a=this.state.following?this.state.hovering?this.props.show_name?this.tt("misc.follow_button.unfollow_username",{username:this.props.name}):this.tt("misc.follow_button.unfollow"):React.createElement(React.Fragment,{},o({className:"icon icon-checkmark","aria-hidden":!0,role:"img"}),this.props.show_name?this.tt("misc.follow_button.following_username",{username:this.props.name}):this.tt("misc.follow_button.following")):React.createElement(React.Fragment,{},o({className:"icon icon-plus","aria-hidden":!0,role:"img"}),this.props.show_name?this.tt("misc.follow_button.follow_username",{username:this.props.name}):this.tt("misc.follow_button.follow")),this.enclose({component:this.props.login_url?"a":"button",href:this.props.login_url,"data-label":"follow_btn",className:classNames("button",{is_following:this.state.following,disabled:this.state.loading},this.state.animate_class,this.props.className),onMouseEnter:function(u){return function(){return u.setState({hovering:!0})}}(this),onMouseLeave:function(u){return function(){return u.setState({hovering:!1})}}(this),target:this.props.target,onClick:function(u){return function(l){var c,d;if(!u.props.login_url&&!u.state.loading)return l.preventDefault(),c=$(l.target),u.setState({loading:!0,animate_class:null}),u.container().trigger("i:track_link"),d=u.state.following?u.props.unfollow_url:u.props.follow_url,$.post(d,I.with_csrf(u.props.follow_data),function(h){if(h.errors){I.flash(h.errors.join(", ")),u.setState({loading:!1});return}return c.trigger("i:follow_updated",[{user_id:u.props.user_id,following:h.following,btn:c}]),u.setState({following:h.following,animate_class:h.following?"animate_bounce":"animate_drop_down",loading:!1},function(){var g;return typeof(g=u.props).on_follow_change=="function"?g.on_follow_change(u.state.following):void 0})})}}(this)},a)}})});var Ae=null;G(function(){var t,e;return t=A,e=t.div,Ae=U.component("SlideDown",{getInitialState:function(){return{}},getDefaultProps:function(){return{duration:200,delay:1}},componentDidMount:function(){return this.timer=window.setTimeout(function(r){return function(){var n;return n=r.wrapper_ref.current,r.setState({height:n.scrollHeight||!1}),r.timer=window.setTimeout(function(){return r.setState({animated:!0})},r.props.duration+50),n.scrollTop=0}}(this),this.props.delay)},componentWillUnmount:function(){if(this.timer)return window.clearTimeout(this.timer),delete this.timer},render:function(){var r;return r=this.state.height===!1?null:this.state.height?this.state.animated?null:{height:this.state.height+"px",overflow:"hidden",transition:"height "+this.props.duration/1e3+"s ease"}:{height:0,overflow:"scroll",transition:"height 0.2s ease"},e({style:r,className:this.props.className,ref:this.wrapper_ref||(this.wrapper_ref=k.createRef())},this.props.children)}})});var se=null;G(function(){var t,e;return t=A,e=t.img,se=function(r){return k.createElement("svg",{className:"svgicon icon_down_tick2",strokeLinecap:"round",stroke:"currentColor",role:"img",version:"1.1",viewBox:"0 0 24 24",strokeWidth:"2",width:"22",height:"22",strokeLinejoin:"round","aria-hidden":!0,fill:"none"},k.createElement("polyline",{points:"6 9 12 15 18 9"}))},se=k.createElement.bind(null,k.memo(se))});var Tt=[].slice,Et=null,ee=null;G(function(){var t,e,r,n,i,o,s,a,u,l,c,d,h,g,x,m,y;return e=A,o=e.input,n=e.div,s=e.label,g=e.span,m=e.textarea,c=e.p,l=e.option,u=e.optgroup,h=e.select,y=e.ul,a=e.li,d=e.section,i=e.form,x=e.strong,r=function(f){return function(){return U.component.apply(U,arguments)}}(this),r("CSRF",{pure:!0,render:function(){return o({type:"hidden",name:"csrf_token",value:$("meta[name=csrf_token]").attr("value")})}}),t=U.package("Forms"),t("InputRow",{pure:!0,propTypes:{title:E.string,sub:E.any},render:function(){var f;return f=k.createElement(k.Fragment,{},n({className:"label",key:"label"},this.props.title,this.props.sub?g({className:"sub"}," \u2014 ",this.props.sub):void 0),this.props.children),this.props.multi||(f=s({children:f})),n({className:N("input_row",this.props.className),id:this.props.id},f)}}),t("TextInputRow",{pure:!0,propTypes:{title:E.string,sub:E.any,name:E.string.isRequired,value:E.any},focus:function(){return this.input_ref.current.focus()},get_input_el:function(){return this.input_ref.current},get_value:function(){return this.input_ref.current.value},componentDidMount:function(){var f;if(this.props.money_input){if(!this.props.currency)throw new Error("missing currency");I.money_input(this.input_ref.current,{currency:this.props.currency})}if(this.props.slug_input&&I.slug_input($(this.input_ref.current)),this.props.integer_input)return f=/[0-9]/g,$(this.input_ref.current).on("keypress",function(p){return function(v){var b;if(v.keyCode>=32&&(b=String.fromCharCode(v.keyCode),!b.match(f)))return!1}}(this))},render:function(){var f,p;return f=function(){switch(this.props.type){case"textarea":return function(v){return function(b){return m(Object.assign({rows:v.props.rows,cols:v.props.cols},b))}}(this);default:return function(v){return function(b){return o(Object.assign({type:v.props.type||"text"},b))}}(this)}}.call(this),p=[n({className:"label",key:"label"},this.props.title,this.props.sub?g({className:"sub"}," \u2014 ",this.props.sub):void 0),f({key:"input",name:this.props.name,ref:this.input_ref||(this.input_ref=k.createRef()),value:this.props.value,defaultValue:this.props.defaultValue,onChange:this.props.onChange,onKeyUp:this.props.onKeyUp,onFocus:this.props.onFocus,onBlur:this.props.onBlur,onClick:this.props.onClick,readOnly:this.props.readonly,className:N({has_error:this.props.has_error}),required:this.props.required,placeholder:this.props.placeholder,disabled:this.props.disabled,pattern:this.props.pattern,dir:this.props.dir}),this.props.error_message?c({key:"error",className:"input_error"},this.props.error_message):void 0],this.props.multi||(p=s({children:p})),n({className:N("input_row",this.props.className)},p)}}),t("SimpleSelect",{pure:!0,propTypes:{options:E.array.isRequired,name:E.string},getInitialState:function(){return{value:this.props.defaultValue?this.props.defaultValue:void 0}},render:function(){var f,p,v,b,C;return p=this.current_option(),C=this.props.render_current_option,C||(C=function(j){return function(M){return M.short_name||M.name}}(this)),f=[],v=null,b=function(){if(v)return f.push(u.apply(null,[{label:v.label,key:"group-"+f.length}].concat(Tt.call(v.children)))),v=null},this.props.options.map(function(j){return function(M,Y){var O;return O=l({key:""+Y,value:M.value},M.name),M.group?((v!=null?v.label:void 0)!==M.group&&b(),v||(v={label:M.group,children:[]}),v.children.push(O)):(b(),f.push(O))}}(this)),b(),this.enclose({className:N({focused:this.state.focused,disabled:this.props.disabled,has_value:p!==this.props.options[0]})},n({className:"selected_option"},g({className:"selected_option_name"},C(p)),se({})),h({ref:this.select_ref||(this.select_ref=k.createRef()),disabled:this.props.disabled,value:p.value,name:this.props.name,onFocus:function(j){return function(M){return j.setState({focused:!0})}}(this),onBlur:function(j){return function(M){return j.setState({focused:!1})}}(this),onChange:this.on_change,children:f}))},on_change:function(f){var p;return p=f.target.value,this.props.onChange?p===this.props.value?void 0:this.props.onChange(p):p===this.state.value?void 0:this.setState({value:p})},find_option:function(f){var p,v,b,C;for(C=this.props.options,p=0,v=C.length;p<v;p++)if(b=C[p],b.value===f)return b},current_option:function(){var f,p;return p=this.props.value||this.state.value,f=p!=null?this.find_option(p):void 0,f||this.props.options[0]}}),t("Select",{propTypes:{name:E.string.isRequired,value:E.any,options:E.array.isRequired},componentDidMount:function(){var f,p;if(this.props.selectize&&(p=this.props.selectize===!0?{}:this.props.selectize,f=$(this.input_ref.current),f.selectize(p),this.props.onChange))return f.on("change",function(v){return function(b){return v.props.onChange(b)}}(this))},render:function(){return h({ref:this.input_ref||(this.input_ref=k.createRef()),name:this.props.name,onChange:this.props.onChange,value:this.props.value,defaultValue:this.props.defaultValue,children:this.props.options.map(function(f){return function(p){var v,b;return b=p[0],v=p[1],l({key:b,value:b},v||b)}}(this))})}}),t("RadioButtons",{propTypes:{name:E.string.isRequired,value:E.any,options:E.array.isRequired},getValue:function(){var f,p,v,b,C,j;for(b=this.container().find("input").serializeArray(),f=0,p=b.length;f<p;f++)return C=b[f],v=C.name,j=C.value,j},render:function(){return y({className:"radio_list",children:this.props.options.map(function(f){return function(p,v){var b,C,j,M;return M=p[0],b=p[1],j=p[2],C=p[3],a({key:M!=null?M:v,className:N({disabled:C!=null?C.disabled:void 0})},s({},o(Object.assign({type:"radio",required:f.props.required,name:f.props.name,value:M,defaultChecked:f.props.defaultValue?M===f.props.defaultValue:void 0,checked:f.props.value!=null?M===f.props.value:void 0,onChange:f.props.onChange},C)),g({className:"radio_label"},b),j?g({className:"sub"}," \u2014 ",j):void 0))}}(this))})}}),Et=t("FormErrors",{pure:!0,getDefaultProps:function(){return{animated:!1,scroll_into_view:!1}},propTypes:{title:E.string,errors:E.array.isRequired},render:function(){var f;return f=this.props.animated?Ae:d,f({className:N(this.enclosing_class_name(),"form_errors"),role:"alert"},n({ref:this.props.scroll_into_view?function(p){if(p)return typeof p.scrollIntoView=="function"?p.scrollIntoView():void 0}:void 0},this.props.title!==!1?c({},x({},this.props.title||this.tt("misc.forms.form_errors"))):void 0,y({},this.props.errors.map(function(p){return function(v){var b;return b=v==="recaptcha"?"Please complete the CAPTCHA to continue":v,a({key:v},b)}}(this))),this.props.children))}}),ee=function(f){var p;return p=k.createRef(),k.useEffect(function(){I.with_recaptcha(function(v){return function(){var b;if(b=p.current)return grecaptcha.render(b,{sitekey:f.sitekey})}}(this))},[]),n({ref:p,className:"g-recaptcha"})},ee.propTypes={sitekey:E.string.isRequired},ee=k.createElement.bind(null,k.memo(ee)),t.RecaptchaInput=ee});var ae=null;G(function(){var t,e;return t=A,e=t.img,ae=function(r){var n,i,o,s,a;return r==null&&(r={}),a=(i=r.width)!=null?i:24,n=(o=r.height)!=null?o:a,k.createElement("svg",{className:"svgicon icon_close",strokeLinecap:"round",stroke:"currentColor",role:"img",version:"1.1",viewBox:"0 0 24 24",strokeWidth:(s=r.stroke_width)!=null?s:"2",width:a,height:n,strokeLinejoin:"round","aria-hidden":!0,fill:"none"},k.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),k.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))},ae=k.createElement.bind(null,k.memo(ae))});var Ue=null,He=null,Mt=null;G(function(){var t,e,r,n,i,o,s,a;return t=A,e=t.a,i=t.div,r=t.button,s=t.label,o=t.h2,a=t.p,n=null,Mt=function(u){return u.promise?(I.Lightbox.open_loading(),u.then(function(l){return function(c){return I.Lightbox.open(c)}}(this),function(l){return function(c){return I.Lightbox.open(He({errors:c.errors}))}}(this))):I.Lightbox.open(u)},Ue=U.component("Lightbox",{is_modal_dialog:function(){var u;return u=document.getElementById("lightbox_container"),u!=null?u.contains(this.container_ref.current):void 0},getInitialState:function(){return{}},componentDidMount:function(){if(this.is_modal_dialog())return n?console.warn("A dialog already has the focus trap"):(this.detect_focus=function(u){return function(l){var c;if(c=u.container_ref.current,c&&"contains"in c&&!(c===l.target||c.contains(l.target)))return c.focus()}}(this),$(document.body).on("focusin",this.detect_focus),n=this),this.setState({previously_focused:document.activeElement,is_modal_dialog:!0},function(){return _.defer(function(u){return function(){var l;return(l=u.container_ref.current)!=null?l.focus():void 0}}(this))})},componentWillUnmount:function(){var u;return this.detect_focus&&($(document.body).off("focusin",this.detect_focus),n=null,delete this.detect_focus),(u=this.state.previously_focused)!=null?u.focus():void 0},close:function(){return I.Lightbox.close()},render:function(){return i({className:classNames("lightbox",this.props.className),style:this.props.style,role:this.state.is_modal_dialog?"dialog":void 0,"aria-modal":this.state.is_modal_dialog?"true":void 0,tabIndex:this.state.is_modal_dialog?-1:void 0,ref:this.container_ref||(this.container_ref=React.createRef())},this.props.close!==!1?r({className:"close_button",type:"button","aria-label":"Close Dialog"},ae({width:18})):void 0,this.props.children)}}),He=U.component("ErrorLightbox",{propTypes:{title:E.string,errors:E.array.isRequired},render:function(){return Ue({className:classNames(this.enclosing_class_name(),"compact")},o({},this.props.title||this.tt("misc.lightboxes.error_title")),U.Forms.FormErrors({title:!1,errors:this.props.errors}),a({className:"buttons"},r({className:"button",type:"button",onClick:function(u){return function(){return I.Lightbox.close()}}(this)},"Close")))}})});var ge=function(t,e){return function(){return t.apply(e,arguments)}},St=T.HoverManager=function(){t.prototype.default_opts={timeout:1e3,enter_timeout:150,fade_timeout:400,show:function(){},hide:function(){},after_fade:function(){}};function t(e){this._clear_enter_timeout=ge(this._clear_enter_timeout,this),this._clear_fade_timeout=ge(this._clear_fade_timeout,this),this._clear_leave_timeout=ge(this._clear_leave_timeout,this),this.opts=$.extend({},this.default_opts,e),this.open=!1}return t.prototype.enter=function(){if(!this.enter_timeout)return this._clear_leave_timeout(),this._clear_fade_timeout(),this.enter_timeout=window.setTimeout(function(e){return function(){var r;return delete e.enter_timeout,e.open||typeof(r=e.opts).show=="function"&&r.show(),e.open=!0}}(this),this.opts.enter_timeout)},t.prototype.leave=function(){if(!this.leave_timeout)return this._clear_enter_timeout(),this.leave_timeout=window.setTimeout(function(e){return function(){return delete e.leave_timeout,e.hide()}}(this),this.opts.timeout)},t.prototype.hide=function(){var e;return this._clear_enter_timeout(),this._clear_leave_timeout(),this.open&&typeof(e=this.opts).hide=="function"&&e.hide(),this.open=!1,this.hiding=!0,this.fade_timeout=window.setTimeout(function(r){return function(){var n;return typeof(n=r.opts).after_fade=="function"&&n.after_fade(),r.hiding=!1}}(this),this.opts.fade_timeout)},t.prototype.show=function(){var e;return typeof(e=this.opts).show=="function"?e.show():void 0},t.prototype._clear_leave_timeout=function(){if(this.leave_timeout)return window.clearTimeout(this.leave_timeout),delete this.leave_timeout},t.prototype._clear_fade_timeout=function(){if(this.fade_timeout)return window.clearTimeout(this.fade_timeout),delete this.fade_timeout},t.prototype._clear_enter_timeout=function(){if(this.enter_timeout)return window.clearTimeout(this.enter_timeout),delete this.enter_timeout},t}(),zt=function(t,e){return function(){return t.apply(e,arguments)}},ue=function(){t.prototype.track_category="header";function t(e){this.setup_browse_menu=zt(this.setup_browse_menu,this);var r,n;this.el=w(e),T.tracked_links(this.el,this.track_category,"click"),this.el.is(".sticky")&&w("html").css({scrollPaddingTop:this.el.outerHeight()+"px"}),w("#maincontent").length||(r=w(document.body).find(".page_widget"),r.length?(n=r.attr("id"))?(r.attr("tabindex","-1"),this.el.find(".skip_to_main").attr("href","#"+n)):r.attr("id","maincontent").attr("tabindex","-1"):(T.in_dev&&console.warn("no main content, removing skip link"),this.el.find(".skip_to_main").remove())),this.el.on("click",".menu_tick",function(i){return function(o){var s,a,u;if(!w(o.target).closest(".drop_menu").length)return u=w(o.currentTarget),a=u.closest(".drop_menu_wrap").toggleClass("open"),s=w(document.body),a.is(".open")?(u.trigger("i:track_link"),s.on("click.drop_menu",function(l){if(!w(l.target).closest(a).length)return a.removeClass("open"),s.off("click.drop_menu")})):s.off("click.drop_menu")}}(this)),this.setup_browse_menu()}return t.prototype.setup_browse_menu=function(){var e,r;return e=[],r=function(n){return function(i,o){var s,a,u;return s=n.el.find(i),a=n.el.find(o),u=new St({timeout:750,enter_timeout:100,show:function(){return a.addClass("open"),s.addClass("open"),setTimeout(function(){return a.addClass("visible")},1),e.forEach(function(l){if(l!==u)return l.hide()})},hide:function(){return s.removeClass("open"),a.removeClass("visible")},after_fade:function(){return a.removeClass("open")}}),n.el.on("mouseenter mouseleave",i+", "+o,function(l){return l.type==="mouseenter"?u.enter():u.leave()}),u}}(this),e.push(r(".browse_btn",".header_dropdown[data-target=browse]")),e.push(r(".active_site_promotion_btn",".header_dropdown[data-target=site_promotion]")),w(document.body).on("click",function(n){return function(i){var o;o=w(i.target).closest(".browse_btn, .active_site_promotion_btn, .header_dropdown"),!o.length&&e.forEach(function(s){return s.hide()})}}(this))},t}(),Nt=function(t,e){for(var r in e)Dt.call(e,r)&&(t[r]=e[r]);function n(){this.constructor=t}return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},Dt={}.hasOwnProperty,Lt=function(t){Nt(e,t);function e(){e.__super__.constructor.apply(this,arguments),this.el.dispatch("click",{edit_theme_btn:function(r){return function(){return r.toggle_theme_editor()}}(this)}),window.location.hash.match(/\bedit_theme\b/)&&q.defer(function(r){return function(){return r.toggle_theme_editor()}}(this))}return e.prototype.toggle_theme_editor=function(){var r;return r=!T.theme_editor.state.open,T.theme_editor.setState({open:r}),$(document.body).toggleClass("theme_editor_open",r)},e}(ue);window.init_GameHeader=function(t,e){new Lt(t,e)};var We=[].slice,F=null,Ot=null,Pt=null,Ft=null,qt=null,At=null,Ut=null,Ge=null,Be=null,Ve=null,Je=null;G(function(){var t,e,r;return t=A,e=t.a,r=t.img,F=function(){var n,i,o;return o=arguments[0],i=2<=arguments.length?We.call(arguments,1):[],o==null&&(o={}),k.createElement.bind(null,k.memo(n=function(s){var a,u,l,c,d;return k.createElement.apply(k,["svg",{className:N("svgicon",s.className,o.className),role:"img",version:"1.1",viewBox:"0 0 24 24",width:(a=s.width)!=null?a:"24",height:(u=s.height)!=null?u:"24",fill:(l=(c=s.fill)!=null?c:o.fill)!=null?l:"none",stroke:"currentColor",strokeWidth:(d=s.strokeWidth)!=null?d:"2",strokeLinejoin:"round",strokeLinecap:"round","aria-hidden":!0}].concat(We.call(i)))}))},Ot=F({className:"icon_tri_up",fill:"currentColor"},k.createElement("polygon",{points:"2 18 12 6 22 18"})),Pt=F({className:"icon_tri_down",fill:"currentColor"},k.createElement("polygon",{points:"2 6 12 18 22 6"})),Ft=F({className:"icon_filter"},k.createElement("polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"})),qt=F({className:"icon_edit"},k.createElement("path",{d:"M20 14.66V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h5.34"}),k.createElement("polygon",{points:"18 2 22 6 12 16 8 16 8 12 18 2"})),At=F({className:"icon_external_link"},k.createElement("path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"}),k.createElement("polyline",{points:"15 3 21 3 21 9"}),k.createElement("line",{x1:"10",y1:"14",x2:"21",y2:"3"})),Ut=F({className:"icon_help"},k.createElement("circle",{cx:"12",cy:"12",r:"10"}),k.createElement("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),k.createElement("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})),Ge=F({className:"icon_search"},k.createElement("circle",{cx:"11",cy:"11",r:"8"}),k.createElement("line",{x1:"21",y1:"21",x2:"16.65",y2:"16.65"})),Be=F({className:"icon_tag"},k.createElement("path",{d:"M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"}),k.createElement("line",{x1:"7",y1:"7",x2:"7",y2:"7"})),Je=F({className:"icon_browse_category"},k.createElement("path",{d:"M4 4h16v2H4zM4 10h10v2H4zM4 16h14v2H4z"})),Ve=F({className:"icon_verified"},k.createElement("title",{},"Verified Account"),k.createElement("path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14"}),k.createElement("polyline",{points:"22 4 12 14.01 9 11.01"}))});var Ye=T.ReferrerTracker||(T.ReferrerTracker=function(){function t(){}return t.MAX_ITEMS=20,t.get_cookie=function(){return window.Cookies.getJSON("itchio_refs")||[]},t.write_cookie=function(e){return T.set_cookie("itchio_refs",e,{expires:14})},t.has_ref=function(e,r){var n,i,o,s,a,u;for(r=+r,a=t.get_cookie(),o=0,s=a.length;o<s;o++)if(u=a[o],i=u[0],n=u[1],i===e&&n===r)return!0;return!1},t.push=function(e,r,n){var i,o;if(n&&n.length>0){for(r=+r,o=function(){var s,a,u,l;for(u=this.get_cookie(),l=[],s=0,a=u.length;s<a;s++)i=u[s],!(i[0]===e&&i[1]===r)&&l.push(i);return l}.call(t),T.in_dev&&console.log("pushing referrer",[e,r,n]),o.unshift([e,r,n]);o.length>t.MAX_ITEMS;)o.pop();return t.write_cookie(o)}},t}()),le=null;G(function(){var t,e,r,n,i;return e=A,r=e.a,i=e.p,n=e.button,t={},le=U.component("ConversionLink",{pure:!0,propTypes:{source:E.number,object_type:E.number,object_id:E.number,href:E.string,target:E.string,class:E.any,impression:E.bool,impression_delay:E.number,on_click:E.func,on_impression:E.func},click:function(){return this.link_ref.current.click()},getInitialState:function(){return{seen:!1}},get_suffix:function(){return this.props.source+":"+this.props.object_type+":"+this.props.object_id},on_click:function(o){var s,a,u,l;if(o.type==="click"&&(s=this.link_ref.current,u=o.metaKey||o.ctrlKey||o.shiftKey||this.props.target==="_blank",i=this.handle_click(),!u&&i.state()!=="resolved"))return o.preventDefault(),l=new Date,a=function(c){return function(){if(a)return a=null,window.location=c.props.href}}(this),setTimeout(function(c){return function(){return typeof a=="function"?a():void 0}}(this),200),i.done(a)},on_mouse_up:function(o){if(o.button===1)return this.handle_click()},handle_click:function(){var o,s;return this.props.object_type&&this.props.source&&this.props.object_id?this.state.clicked?this.state.clicked:(s=H.types.click+":"+this.get_suffix(),t[s]?$.when():(t[s]=!0,H.click(this.get_suffix()),i=H.flush_now_beacon(),this.props.track_referrer&&Ye.push.apply(Ye,this.props.track_referrer),this.setState({clicked:i}),typeof(o=this.props).on_click=="function"&&o.on_click(),i)):$.when()},componentDidMount:function(){var o;if(this.props.object_type&&this.props.source&&this.props.object_id&&this.props.impression!==!1&&!t[H.types.impression+":"+this.get_suffix()])return o=function(s){return function(){var a;if(!s.unmounted)return a=s.link_ref.current,s.unbind_visibility=$(a).lazy_images({elements:[a],show_images:function(){var u,l;return l=H.types.impression+":"+s.get_suffix(),t[l]||(H.push(l),t[l]=!0),typeof(u=s.props).on_impression=="function"&&u.on_impression(),s.setState({seen:!0}),typeof s.unbind_visibility=="function"&&s.unbind_visibility(),s.unbind_visibility=null}})}}(this),this.props.impression_delay?setTimeout(o,this.props.impression_delay):o()},componentWillUnmount:function(){return this.unmounted=!0,typeof this.unbind_visibility=="function"?this.unbind_visibility():void 0},render:function(){var o;return r({ref:this.link_ref||(this.link_ref=k.createRef()),href:this.props.href,className:this.props.class||this.props.className,onClick:this.on_click,onMouseUp:this.on_mouse_up,target:this.props.target,tabIndex:(o=this.props.tabindex)!=null?o:this.props.tabIndex,"aria-hidden":this.props["aria-hidden"]},this.props.children)}})});var Ht={}.hasOwnProperty,ve=null;G(function(){var t,e,r,n,i,o,s,a,u,l,c,d,h,g,x;return e=A,i=e.div,o=e.fragment,a=e.input,n=e.button,u=e.label,r=e.a,d=e.section,x=e.ul,l=e.li,g=e.strong,h=e.span,s=e.img,o=k.createElement.bind(null,k.Fragment),o.type=k.fragment,t=U.package("Search"),c=function(m){if(m)return i({className:"sub_text"},m.map(function(y){return function(f,p){return o({key:p},p>0?", ":void 0,f.verified?o({},Ve({width:12})," "):void 0,f.name)}}(this)))},ve=t("AutocompleteInput",{getInitialState:function(){return{query:this.props.defaultValue}},componentDidMount:function(){if(this.input_wrapper_ref.current.parentNode.classList.add(this.enclosing_class_name()),$(document.body).on("focusin focusout",this.detect_focus),this.props.autofocus&&this.input_ref.current.focus(),this.props.defaultValue)return this.on_change()},componentWillUnmount:function(){return $(document.body).off("focusin focusout",this.detect_focus)},render:function(){var m,y,f,p,v,b;return o({},function(){var C,j;if(this.props.search_params){C=this.props.search_params,j=[];for(m in C)Ht.call(C,m)&&(b=C[m],j.push(a({key:m,type:"hidden",name:m,value:b})));return j}}.call(this),i({className:"input_wrapper",ref:this.input_wrapper_ref||(this.input_wrapper_ref=k.createRef())},a({type:"text",required:!0,name:"q",className:"search_input autocomplete_search_input",placeholder:(y=(f=this.props.i18n)!=null?f.search_placeholder:void 0)!=null?y:this.t("layout.header.search_placeholder"),defaultValue:this.props.defaultValue,autoComplete:"off",onChange:this.on_change,onKeyDown:this.on_key_down,onBlur:this.on_blur,ref:this.input_ref||(this.input_ref=k.createRef())}),this.render_autocomplete_results()),this.props.show_submit!==!1?n({className:N("submit_btn",{is_active:this.state.query}),"aria-label":(p=(v=this.props.i18n)!=null?v.search:void 0)!=null?p:this.t("layout.header.search")},Ge({width:18,strokeWidth:3})):void 0)},detect_focus:function(){this.detect_focus=q.debounce(function(m){return function(){var y;if(y=m.input_wrapper_ref.current.contains(document.activeElement),m.state.in_focus!==y)return m.setState({in_focus:y})}}(this),100),this.detect_focus()},on_blur:function(m){if(this.state.selected_idx!=null)return this.setState({selected_idx:null})},on_key_down:function(m){var y;switch(m.keyCode){case 38:this.result_list&&this.state.selected_idx!=null&&(m.preventDefault(),this.setState(function(f){return{selected_idx:Math.max(0,f.selected_idx-1)}}));break;case 40:this.result_list&&(m.preventDefault(),this.setState(function(f){var p;return{selected_idx:Math.min(((p=f.selected_idx)!=null?p:-1)+1,this.result_list.length-1)}}));break;case 13:this.state.selected_idx!=null&&(m.preventDefault(),y=this.result_list[this.state.selected_idx],y.ref.current.click());break;case 27:m.preventDefault(),this.clear_search(),this.input_ref.current.value=""}},on_change:function(m){var y,f,p;if(f=$.trim(this.input_ref.current.value),f===""){this.clear_search();return}return this.setState({query:f,selected_idx:null}),this.query_cache||(this.query_cache={}),p=(y=this.query_cache)[f]||(y[f]=$.getJSON(this.props.autocomplete_url,{query:f})),this.current_request=p,p.done(function(v){return function(b){if(p===v.current_request)return v.setState({selected_idx:null,results:b})}}(this)),p.always(function(v){return function(){if(p===v.current_request)return delete v.current_request}}(this))},clear_search:function(){return delete this.current_request,delete this.result_list,this.setState({query:null,results:null,selected_idx:null})},render_autocomplete_results:function(){if(this.state.results&&this.state.in_focus)return this.result_list=[],i({className:"autocomplete_results_popup"},this.props.search_url?d({className:"autocomplete_result_set"},x({},l({},r({href:this.props.search_url+"?q="+window.encodeURIComponent(this.state.query),className:"forward_link"},"See results for '"+this.state.query+"'")))):void 0,this.state.results.group_order?this.state.results.group_order.map(function(m){return function(y){switch(y){case"featured_tags":return m.render_tag_results();case"jams":return m.render_jam_results();case"games":return m.render_game_results()}}}(this)):(this.render_tag_results(),this.render_jam_results(),this.render_game_results()))},render_tag_results:function(){var m;if((m=this.state.results.tags)!=null&&m.length)return d({className:"autocomplete_result_set",key:"featured_tags"},g({className:"result_header"},"Browse"),x({className:"tag_results"},this.state.results.tags.map(function(y){return function(f,p){var v,b,C;return y.result_list.push(f),v=function(){switch(f.type){case"browse_facets":return this.props.ca_types.browse_facet;case"featured_tags":return this.props.ca_types.featured_tag}}.call(y),C=function(){switch(f.default_classification){case"game_mod":return this.tt("misc.classifications.game_mod");case"physical_game":return"Tabletop";case"assets":return this.tt("misc.classifications.assets")}}.call(y),l({className:"autocomplete_result tag_result",key:f.url},le({ref:f.ref||(f.ref=k.createRef()),href:f.url,object_id:f.id,object_type:v,source:y.props.ca_source,impression_delay:400,className:N({selected:y.state.selected_idx===y.result_list.length-1})},function(){switch(f.type){case"browse_facets":return Je({});default:return Be({})}}(),i({className:"result_content"},i({className:"title"},f.name),f.stats!=null&&f.stats.count?h({className:"sub_text"},T.number_format(((b=f.stats)!=null?b.count:void 0)||0)," ",function(){var j;switch((j=f.stats)!=null?j.count_unit:void 0){case"jams":return"Jams";default:return"Projects"}}()):void 0),C?i({className:"tag_classification"},C):void 0))}}(this))))},render_game_results:function(){var m;if((m=this.state.results.games)!=null&&m.length)return d({className:"autocomplete_result_set",key:"games"},g({className:"result_header"},"Games & Projects"),x({className:"game_results"},this.state.results.games.map(function(y){return function(f,p){return y.result_list.push(f),l({className:"autocomplete_result game_result",key:y.props.ca_types.game+":"+f.id},le({ref:f.ref||(f.ref=k.createRef()),href:f.url,object_id:f.id,object_type:y.props.ca_types.game,source:y.props.ca_source,impression_delay:400,className:N({selected:y.state.selected_idx===y.result_list.length-1})},i({className:"result_icon"},f.cover&&f.cover!==""?s({loading:"lazy",srcSet:f.cover+" 1x, "+f.cover2x+" 2x",width:30,height:30,src:f.cover}):void 0),i({className:"result_content"},i({className:"title"},f.name),c(f.contributors))))}}(this))))},render_jam_results:function(){var m;if((m=this.state.results.jams)!=null&&m.length)return d({className:"autocomplete_result_set",key:"jams"},g({className:"result_header"},"Jams"),x({className:"jam_results"},this.state.results.jams.map(function(y){return function(f,p){var v;return y.result_list.push(f),v=function(){switch(f.state){case"during_voting":case"during_submit":return"Ongoing";case"finished":return"Ended";case"before":return"Upcoming"}}(),l({className:"autocomplete_result jam_result",key:p},le({ref:f.ref||(f.ref=k.createRef()),href:f.url,object_id:f.id,object_type:y.props.ca_types.jam,source:y.props.ca_source,impression_delay:400,className:N({selected:y.state.selected_idx===y.result_list.length-1})},i({className:"result_icon"},f.cover&&f.cover!==""?s({loading:"lazy",srcSet:f.cover+" 1x, "+f.cover2x+" 2x",width:30,height:30,src:f.cover}):void 0),i({className:"result_content"},i({className:"title"},f.name),c(f.contributors)),v?i({className:N("jam_state","state_"+f.state)},v):void 0))}}(this))))}})}),window.init_Header=function(t,e){let r=null;I.libs.react.done(function(){e.autocomplete_props&&r&&(e.autocomplete_props.defaultValue=r.val(),r.is(":focus")&&(e.autocomplete_props.autofocus=!0))}),new ue(t),e.autocomplete_props&&R.render(t+" .game_search",()=>ve(e.autocomplete_props)),window.ReactDOM||(r=$(t).find(".game_search .search_input").on("focus",function(){setTimeout(()=>I.add_react(),500)}))},window.init_JamLayoutHeader=function(t,e){let r=null;I.libs.react.done(function(){e.autocomplete_props&&r&&(e.autocomplete_props.defaultValue=r.val(),r.is(":focus")&&(e.autocomplete_props.autofocus=!0))}),new ue(t),e.autocomplete_props&&R.render(t+" .game_search",()=>ve(e.autocomplete_props)),window.ReactDOM||(r=$(t).find(".game_search .search_input").on("focus",function(){setTimeout(()=>I.add_react(),500)}))};var Wt=function(t,e){for(var r in e)Gt.call(e,r)&&(t[r]=e[r]);function n(){this.constructor=t}return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},Gt={}.hasOwnProperty,Bt=function(t){Wt(e,t);function e(){e.__super__.constructor.apply(this,arguments),this.el.dispatch("click",{edit_theme_btn:function(r){return function(){return r.toggle_theme_editor()}}(this)}),window.location.hash.match(/\bedit_theme\b/)&&q.defer(function(r){return function(){return r.toggle_theme_editor()}}(this))}return e.prototype.toggle_theme_editor=function(){var r;return r=!T.theme_editor.state.open,T.theme_editor.setState({open:r}),$(document.body).toggleClass("theme_editor_open",r)},e}(ue);window.init_UserProfileHeader=function(t,e){new Bt(t,e)}})();
