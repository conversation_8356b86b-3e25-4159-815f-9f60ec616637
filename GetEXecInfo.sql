SET ANSI_NULLS ON
SET NOCOUNT ON

--==============================================================================================================================================
--  Name:               GetExecutionStepInfo_CWC_EXT
--  Editor Tab Spacing: 4
--==============================================================================================================================================
--  DESCRIPTION:
--  Custom version of SQL service GetExecutionStepInfo for CWC Extended version.
--
--	Validation codes:
--	-2	Invalid JSON format
--==============================================================================================================================================
--	EDIT HISTORY:
------------------------------------------------------------------------------------------------------------------------------------------------
--	Revision		Date			Who					What
--	========		====			===					====
--	1.0	    		--   			--					Initial Development
--	1.1				2024-08-26		L. Beliveau			Fix logic for StepLogSheetLink
--	1.2				2024-11-06		N. Pitsakis			Updates for multi certificate compatibility
--	1.3				2024-12-02		L. Beliveau			Update logic for manual entry step improvements
--	1.4				2024-12-09		L. Beliveau			Add CompletedOn to manual entry step info
--	1.5				2024-12-12		L. Beliveau			Add RejectOutdated logic for AttributeGroup
--	1.6				2025-02-04		A. Senska			Inclusive Limits for Smart Tools
--	1.7				2025-02-10		Globensky G.		Update for TW 9.7
--	1.8				2025-02-14		A. Senska			Add Distinct to result (Nikki fix)
--	1.9				2025-02-25		Globensky G.		Bugfix - Remove unused join with note that duplicated serial results
-- 	2.0 			2025-05-12 		Olivier S. 			Improve performance & fix issue with wrong limits getting retrieved when Limits are set to CurrentTime
--	2.1				2025-06-13		A. Senska			Part Validation Extended
--	2.11			2025-06-26		Globensky G.		Bugfixes bad _ap tables columns crashed service
--	2.2				2025-07-03		A. Senska			Fix consumption data
--==============================================================================================================================================
--  BUSINESS RULES:
--  Enter the business rules in this section...
--  1.  Declare input variables
--  2.  Declare Variables
--  3.  Declare Tables
--  4.  Initialize Variables
--	5.	Extract and validate filters
--  6.  Get Work Definition Informations
--  7.  Get Target Cycle Time Informations
--	8.	Get All Steps Execution Time
--	9.	Prepare GenericStepDataJSON
--	10. Get Smart Tool Step Information
--	11. Get Manual Entry Step Information
--	12. Get Verification Step Information
--	13. Get List Step Information
--	14. Get Calculation Step Information
--	15. Get Instructional Step Information
--	16. Get Part Validation Step Information
--	17.	Prepare DetailStepDataJSON
--  18.	Trap Errors
--==============================================================================================================================================
--  EXEC STATEMENT
------------------------------------------------------------------------------------------------------------------------------------------------
/*	
DECLARE
@IsDebugging						BIT,
@IsComplete							BIT,
@FilterJSON					        NVARCHAR(MAX)

	--Single:
    SET @IsDebugging    = 0
    SET @FilterJSON		= '{
        "WorkDefinitionUID": [
            11
        ],
        "ExecutionLimitsReferenceTime": "CurrentTime",
		"IsPartValidationEXTEnabled": true
    }'


	Parallel:
    SET @IsDebugging    = 0
    SET @FilterJSON		= '{
        "WorkDefinitionUID": [
            1105,
            1106,
            1107,
            1108
        ],
        "ExecutionLimitsReferenceTime": "CurrentTime"
    }'
	*/
--==============================================================================================================================================
--  DECLARE INPUT VARIABLES
--  The following variables are coming from the input of the SQL Service
--==============================================================================================================================================

DECLARE
@IsDebugging						BIT,
@IsComplete							BIT,
@FilterJSON					        NVARCHAR(MAX)

--SET @IsDebugging    = COALESCE([[IsDebugging]], 0)
--SET @FilterJSON		= COALESCE([[FilterJSON]], N'')
SET @IsDebugging    = 0
SET @FilterJSON		= '{
    "WorkDefinitionUID": [
        3
    ],
    "ExecutionLimitsReferenceTime": "CurrentTime",
	"IsPartValidationEXTEnabled": true
}'

--==============================================================================================================================================
--  DECLARE VARIABLES
--  The following variables will be used as internal variables.
--==============================================================================================================================================
DECLARE
@ValidationCode				INT,
@ValidationMessage			NVARCHAR(MAX),
@NestedValidationCode		INT,
@NestedValidationMessage	NVARCHAR(MAX),
@ValidationToken			NVARCHAR(2000),
@IsUserError				BIT,
@ErrorGUID					UNIQUEIDENTIFIER,
@ErrorSection				NVARCHAR(255),
@Parameters					NVARCHAR(MAX),
@NestedObjectName			NVARCHAR(255),
@NestingLevel				INT,
@ErrorSeverityLevel			INT,
@ErrorSeverity				INT,
@ErrorState					INT,
@CertificateJSON			NVARCHAR(MAX)
--==============================================================================================================================================
--  DECLARE VARIABLE CONSTANTS
--  The following variables will be used as internal constants.
--==============================================================================================================================================
DECLARE
@ERROR_CRITICAL					INT				= -1,
@ERROR_SEVERITY					INT				= 11,
@ERROR_STATE					INT				= 1,
@ERROR_NONE						INT				= 0,
@ERROR_INFO     				INT 			= 2,
@ERROR_USER						INT				= 3,
@OBJECT_NAME					NVARCHAR(255)	= N'GetExecutionStepInfo_CWC_EXT',
@TARGET_CYCLE_TIME_CC			NVARCHAR(255)	= N'TargetCycleTime',
@CWC_CC_TYPE					NVARCHAR(255)	= N'CWC',
@STEP_TYPE_SMART_TOOL			NVARCHAR(255)	= N'SmartTool',
@STEP_TYPE_MANUALENTRY			NVARCHAR(255)	= N'ManualEntry',
@STEP_TYPE_VERIFICATION			NVARCHAR(255)	= N'Verification',
@STEP_TYPE_LIST					NVARCHAR(255)	= N'List',
@STEP_TYPE_CALCULATION			NVARCHAR(255)	= N'Calculation',
@STEP_TYPE_INSTRUCTIONAL		NVARCHAR(255)	= N'Instructional',
@STEP_TYPE_PARTVALIDATION		NVARCHAR(255)	= N'PartValidation',
@CONTEXT_CWC_WORK_INSTRUCTIONS	NVARCHAR(255)	= N'CWC-WorkInstruction',
@CONTEXT_SMART_TOOLS			NVARCHAR(255)	= N'SmartTools',
@ATTRIBUTE_SOURCE_MANUAL_ENTRY	NVARCHAR(255)	= N'ManualEntry',
@ATTRIBUTE_TYPE_STREAD			NVARCHAR(255)	= N'SmartToolRead',
@ATTRIBUTE_TYPE_STWRITE			NVARCHAR(255)	= N'SmartToolWrite',
@NUMBER_DATA_TYPE               NVARCHAR(255)   = N'NUMBER',
@BOOLEAN_DATA_TYPE              NVARCHAR(255)   = N'BOOLEAN',
@LIST_DATA_TYPE					NVARCHAR(255)	= N'LIST',
@DATE_DATA_TYPE					NVARCHAR(255)	= N'DATE',
@DATETIME_DATA_TYPE				NVARCHAR(255)	= N'DATETIME',
@RETRIEVAL_TYPE_CURRENT_TIME    NVARCHAR(255)   = N'CurrentTime',
@RETRIEVAL_TYPE_APPROVAL_TIME   NVARCHAR(255)   = N'WorkInstructionApprovalTime',
@EMPTY_STRING					NVARCHAR(255)	= N'',
@HIGH_CONTROL_SUFFIX			NVARCHAR(2)     = N'HC',
@HIGH_ENTRY_SUFFIX				NVARCHAR(2)     = N'HE',
@HIGH_REJECT_SUFFIX				NVARCHAR(2)     = N'HR',
@HIGH_USER_SUFFIX				NVARCHAR(2)     = N'HU',
@HIGH_WARNING_SUFFIX			NVARCHAR(2)     = N'HW',
@LOW_CONTROL_SUFFIX				NVARCHAR(2)     = N'LC',
@LOW_ENTRY_SUFFIX				NVARCHAR(2)     = N'LE',
@LOW_REJECT_SUFFIX				NVARCHAR(2)     = N'LR',
@LOW_USER_SUFFIX				NVARCHAR(2)     = N'LU',
@LOW_WARNING_SUFFIX				NVARCHAR(2)     = N'LW',
@NOMINAL_SUFFIX					NVARCHAR(2)     = N'N',
@CURRENT_TIME					DATETIMEOFFSET 		= SYSUTCDATETIME() AT TIME ZONE 'UTC',
--==============================================================================================================================================
--	DECLARE VARIABLES
--==============================================================================================================================================
@WorkDefinitionUID					BIGINT,
@TargetCycleTimeCCUID				BIGINT,
@CurrentStepTargetCycleTime			NVARCHAR(50),
@CurrentStepTargetCycleTimeSeconds	INT,
@AllStepExecutionTime				NVARCHAR(50),
@PlannedEndTime						DATETIMEOFFSET,
@MaxDuration						FLOAT,
@GenericStepDataJSON				NVARCHAR(MAX),
@DetailStepDataJSON					NVARCHAR(MAX),
@ExecutionNotesJSON					NVARCHAR(MAX),
@ControlCharacteristicInputJSON		NVARCHAR(MAX),
@ControlCharacteristicOutputJSON	NVARCHAR(MAX),
@ListStepInfo						NVARCHAR(MAX),
@SmartToolStepInfo					NVARCHAR(MAX),
@ExecutionLimitsReferenceTime       VARCHAR(MAX),
@SerialNumberCount					INT,
@CompletedSerialNumberCount			INT,
@CurrentStepSequenceNumber			INT,
@NbWorkDefinitions					INT,
@TotalTime							BIGINT,
@Days								INT,
@OnlyAttribute                      BIT,
@OnlyAttributeGroup                 BIT,
@MeasureSingleValue                 BIT,
@StartPosition						INT,
@NextStartPosition					INT,
@EndPosition						INT,
@ProductUID							BIGINT,
@ProductTypeUID						BIGINT,
@WDMUID								BIGINT,
@WDMGroupUID						BIGINT,
@IdIndex							BIGINT,
@ContentText						NVARCHAR(MAX),
@Placeholder						NVARCHAR(MAX),
@PlaceholderAttUID					NVARCHAR(MAX),
@PlaceholderLimitSuffix				NVARCHAR(MAX),
@PlaceholderIndex					BIGINT,
@LimitTimestamp						DATETIMEOFFSET,
@IsPartValidationEXTEnabled			BIT
--==============================================================================================================================================
--	DECLARE TABLES
--==============================================================================================================================================
DECLARE @MessagePlaceHolder TABLE
(
	PlaceHolder						INT IDENTITY(0,1),
	[Value]							NVARCHAR(MAX)
)

DECLARE @MessageToken TABLE
(
	TokenName						NVARCHAR(1000),
	ValidationCode					INT
)

DECLARE @WorkDefinitionInfo TABLE
(
	WorkDefinitionUID					BIGINT,
	StepName							NVARCHAR(255),
	StepDescription						NVARCHAR(4000),	
	StepLongDescription					NVARCHAR(MAX),
	RejectMessage						NVARCHAR(MAX),
	StepStatusDefinitionName			NVARCHAR(255),
	Duration							INT,
	StepIsCompleted						BIT DEFAULT(0),
	SerialNumberUID						BIGINT,
    SerialNumberValue					NVARCHAR(255),
	WorkDefinitionRootUID				BIGINT,	
	WorkDefinitionRootMasterVersionUID	BIGINT,
	VersionUID							BIGINT,
	VersionApprovedOn					DATETIMEOFFSET,
	StepCompletedOn						DATETIMEOFFSET,
	CompletedOn							NVARCHAR(50),
	WorkDefinitionMasterUID				BIGINT,
	WorkDefinitionMasterGroupUID		BIGINT,
	WorkDefinitionMasterTypeUID			BIGINT,
	WorkDefinitionMasterTypeName		NVARCHAR(255),
	IsAutoWorkDefinitionForward			BIT,
    IsBypassable						BIT,
	IsCurrent							BIT,
	ParallelExecutionSequenceNumber		INT,
	StepSequenceNumber					INT,
	CertificateJSON						NVARCHAR(MAX),
	InstructionalText					NVARCHAR(MAX),
	LogSheetWorkDocumentRootName		NVARCHAR(MAX),
	VerificationStepCompleted			BIT,
	SelectionListItemUID				BIGINT,
	ProductionEventUID					BIGINT,
	ProductUID							BIGINT,
	ProductTypeUID						BIGINT,
	EquipmentUID						BIGINT,
	ProductionPathVersionLinkUID			BIGINT,
	IsConditionalSkipped					BIT,
	WorkDefinitionMasterConditionTypeName	NVARCHAR(255),
	SecondarySignoffRequired				BIT,
	SecondarySignoffUserGroup				NVARCHAR(255)
)

DECLARE @AllStepExecutionInfo TABLE
(
	WorkDefinitionMasterGroupUID	BIGINT,
	SequenceNumber					INT,
	Duration						INT,
	TargetCycleTime					FLOAT,
	VersionUID						BIGINT,
	VersionApprovedOn				DATETIMEOFFSET 
)

DECLARE @GenericStepDataInfo TABLE
(
	StepName			            NVARCHAR(255),
    StepDescription                 NVARCHAR(4000),
    StepLongDescription             NVARCHAR(MAX),
	StepStatusDefinitionName		NVARCHAR(255),
    StepTargetCycleTime             NVARCHAR(50),
	StepTargetCycleTimeSeconds      INT,
    StepExecutionTime               INT,
    AllStepExecutionTime            NVARCHAR(50),
    IsAutoWorkDefinitionForward	    BIT,
    IsBypassable                    BIT,
	IsCurrent						BIT,
    WorkDefinitionMasterTypeName    NVARCHAR(255),
	CertificateJSON					NVARCHAR(MAX),
	InstructionalText				NVARCHAR(MAX),
	LogSheetWorkDocumentRootName	NVARCHAR(MAX),
	VerificationStepCompleted		BIT,
    MeasureSingleValue     					BIT,
	IsConditionalSkipped					BIT,
	WorkDefinitionMasterConditionTypeName	NVARCHAR(255),
	SecondarySignoffRequired				BIT,
	SecondarySignoffUserGroup				NVARCHAR(255)
)

DECLARE @DetailStepDataInfo TABLE
(
	Id								INT IDENTITY(1,1),
	WorkDefinitionMasterTypeName    NVARCHAR(255),
	NoteUID							BIGINT,
	NoteText						NVARCHAR(MAX),
	SerialNumberUID					BIGINT,
    SerialNumberValue				NVARCHAR(255),
	WorkDefinitionUID				BIGINT,
	WorkDefinitionRootUID			BIGINT,
	ParallelExecutionSequenceNumber	INT,
	SerialNumberStepIsComplete		BIT,
	SerialNumberIsSelected			BIT,
	EstimatedEndTime				NVARCHAR(50),
	ManualEntryStepInfo				NVARCHAR(MAX),
	SmartToolStepInfo				NVARCHAR(MAX),
	CalculationStepInfo				NVARCHAR(MAX),
	PartsValidationStepInfo			NVARCHAR(MAX),
	ListStepInfo					NVARCHAR(MAX),
	InstructionalText				NVARCHAR(MAX),
	LogSheetWorkDocumentRootName	NVARCHAR(MAX),
	VerificationStepCompleted		BIT,
	SelectionListItemUID			BIGINT,
	RejectMessage					NVARCHAR(MAX),
	IsReworkRoute					BIT DEFAULT 0
)

DECLARE @ExecutionNotesInfo TABLE
(
	NoteUID							BIGINT,
	NoteText						NVARCHAR(MAX),
	WorkDefinitionUID				BIGINT,
	SerialNumberUID					BIGINT
)

DECLARE @SelectionList TABLE
(
	SelectionListUID				BIGINT,
	SelectionListDisplayName		NVARCHAR(255)
)

DECLARE @SelectionListContent TABLE
(
	SelectionListUID				BIGINT,
	SelectionListItemUID			BIGINT,
	SelectionListItemDisplayName	NVARCHAR(255)
)

DECLARE @AttributeSelectionList TABLE
(
	AttributeUID					BIGINT,
	SelectionListUID				BIGINT,
	SelectionListDisplayName		NVARCHAR(255)
)

DECLARE @AttributeSelectionListContent TABLE
(
	AttributeUID					BIGINT,
	SelectionListUID				BIGINT,
	SelectionListItemUID			BIGINT,
	SelectionListItemDisplayName	NVARCHAR(255)
)

DECLARE @AttributeGroups TABLE
(
	RcdIdx				BIGINT IDENTITY(1,1),
	AttRcdIdx			BIGINT,
	[UID]				BIGINT,
	[Name]				NVARCHAR(255),
	RejectOutdated		BIT,
	SelectionListUID	BIGINT
)

DECLARE @Attributes TABLE
(
	RcdIdx										BIGINT IDENTITY(1,1),
	SmartToolUID								BIGINT,
	SmartToolAliasName							NVARCHAR(255),
	AttributeUID								BIGINT,
	AttributeName								NVARCHAR(255),
	AttributeDisplayName						NVARCHAR(255),
	AttributeParallelExecutionGroupingUID		BIGINT,
	WDUID										BIGINT,
	WDRUID										BIGINT,
	SequenceNumber								INT,
	ParallelExecutionSequenceNumber				INT,
	SourceName									NVARCHAR(255),
	UnitOfMeasureName							NVARCHAR(255),
	UnitOfMeasureDisplayName					NVARCHAR(255),
	DataFormatName								NVARCHAR(255),
	AttributeValue								NVARCHAR(255),
	TimestampResult								DATETIMEOFFSET,
	CalculationName								NVARCHAR(255),
	Formula										NVARCHAR(255),
	ValueToWrite								NVARCHAR(255),
	OverriddenBy								NVARCHAR(255),
	IsSmartToolWrite							BIT,
	IsParallelExecution							BIT DEFAULT(0),
	IsCompleted									BIT DEFAULT(0),
	IsOverridden								BIT DEFAULT(0),
	RetrieveControlCharacteristics				BIT DEFAULT(1),
	LowRejectValue								NVARCHAR(255),
	LowWarningValue								NVARCHAR(255),
	NominalValue								NVARCHAR(255),
	HighWarningValue							NVARCHAR(255),
	HighRejectValue								NVARCHAR(255),
	WorkDefinitionUID							BIGINT,
	WorkDefinitionName							NVARCHAR(255),
	WorkDefinitionDescription					NVARCHAR(255),
	RejectOutdated								BIT,
	IsLimitInclusive							BIT
)

DECLARE @ControlCharacteristicRequests TABLE
(
	RcdIdx									BIGINT IDENTITY(1,1),
	AttGroupRcdIdx							BIGINT,
	AttributeUID							BIGINT,
	AttributeParallelExecutionGroupingUID	BIGINT,
	ProductUID								BIGINT,
	ProductTypeUID							BIGINT,
	EquipmentUID							BIGINT,
	WorkDefinitionMastergroupUID			BIGINT,
	WorkDefinitionMasterUID					BIGINT,
	WorkDefinitionMasterRootUID				BIGINT,
	StartTime								DATETIMEOFFSET,
	IsSmartTool								BIT,
	limitSuffix								NVARCHAR(MAX),
	placeHolder								NVARCHAR(MAX),
	endTime									DATETIMEOFFSET
)

DECLARE @ControlCharacteristics TABLE
(
	TrendId									INT,
	LimitRequestIdentifier					BIGINT,
	StartTime								DATETIMEOFFSET,
	EndTime									DATETIMEOFFSET,
	ControlCharacteristicLinkUID			BIGINT,
	ControlCharacteristicUID				BIGINT,
	ControlCharacteristicName				NVARCHAR(255),
	ControlCharacteristicDisplayName		NVARCHAR(255),
	UnitOfMeasureUID						BIGINT,
	UnitOfMeasureName						NVARCHAR(255),
	UnitOfMeasureDisplayName				NVARCHAR(255),
	DataFormatUID							BIGINT,
	DataFormatName							NVARCHAR(255),
	EquipmentUID							BIGINT,
	EquipmentName							NVARCHAR(255),
	EquipmentDisplayName					NVARCHAR(255),
	ProductUID								BIGINT,
	ProductName								NVARCHAR(255),
	ProductTypeUID							BIGINT,
	ProductTypeName							NVARCHAR(255),
	ProductTypeDisplayName					NVARCHAR(255),
	AttributeUID							BIGINT,
	AttributeName							NVARCHAR(255),
	AttributeDisplayName					NVARCHAR(255),
	AttributeParallelExecutionGroupingUID	BIGINT,
	AttributeParallelExecutionGroupingName	NVARCHAR(255),
	WorkDefinitionMasterUID					BIGINT,
	WorkDefinitionMasterName				NVARCHAR(255),
	WorkDefinitionMasterRootUID				BIGINT,
	WorkDefinitionMasterRootName			NVARCHAR(255),
	WorkDefinitionMasterRootDisplayName		NVARCHAR(255),
	[Version]								NVARCHAR(255),
	[Status]								NVARCHAR(255),
	SmartToolUID							BIGINT,
	SmartToolName							NVARCHAR(255),
	SmartToolDisplayName					NVARCHAR(255),
	WorkDocumentUID							BIGINT,
	WorkDocumentName						NVARCHAR(255),
	WorkDocumentDisplayName					NVARCHAR(255),
	LotNumberUID							BIGINT,
	LotNumberName							NVARCHAR(255),
	IsInherited								BIT,
	LowEntry								NVARCHAR(25),
	LowReject								NVARCHAR(25),
	LowWarning								NVARCHAR(25),
	LowUser									NVARCHAR(25),
	NominalValue							NVARCHAR(25),
	HighUser								NVARCHAR(25),
	HighWarning								NVARCHAR(25),
	HighReject								NVARCHAR(25),
	HighEntry								NVARCHAR(25),
	TotalCount								BIGINT,
	[Priority]								INT
)

DECLARE @ControlCharacteristicLinkResult TABLE
(
	ControlCharacteristicLinkOutputJSON	NVARCHAR(MAX)
)

DECLARE @SmartToolStep TABLE
(
	AllowValueOverride					BIT,
	AllowSubstitution					BIT,
	SmartToolUID						BIGINT,
	SmartToolTypeUIDAllowed				BIGINT,
	SmartToolName						NVARCHAR(255),
	SmartToolDisplayName				NVARCHAR(255),
	WorkDefinitionMasterSmartToolUID	BIGINT
)

DECLARE @CalculationInputs TABLE
(
	AttributeUID								BIGINT,
	AttributeValueLinkUID						BIGINT,
	AttributeCalculationMasterInputName			NVARCHAR(255),
	AttributeCalculationMasterInputDisplayName	NVARCHAR(255),
	AttributeValue								NVARCHAR(255),
	WDUID										BIGINT,
	WorkDefinitionName							NVARCHAR(255),
	WorkDefinitionDescription					NVARCHAR(MAX),
	IsConditionalSkipped						BIT,
	WorkDefinitionMasterUID						BIGINT,
	EquipmentUID								BIGINT,
	ProductUID									BIGINT,
	JobOrderUID									BIGINT,
	WorkDefinitionMasterConditionTypeName		NVARCHAR(255),
	WorkDefinitionRootDisplay					NVARCHAR(255),
	IsCurrentWorkDefinitionRoot					BIT,
	WorkDefinitionValueToWrite					NVARCHAR(MAX),
	WorkDefinitionWriteTimestamp				DATETIMEOFFSET,
	InputCounts									INT,
	MaxWriteTimestamp							DATETIMEOFFSET
)

DECLARE @Parts TABLE
(
	SequenceNumber					INT,
	BillOfMaterialItemUID			BIGINT,
	BillOfMaterialItemDisplayName	NVARCHAR(255),
	ProductUID						BIGINT,	
	ProductName						NVARCHAR(255),
	ProductDisplayName				NVARCHAR(255),
	RequiredQuantity				DECIMAL(19,5),
	ActualProductName				NVARCHAR(255),
	ActualQuantity					DECIMAL(19,5),
	ConsumedQuantity				DECIMAL(19,5)	DEFAULT 0,
	IsCompleted						BIT,
	IsQuantity						BIT DEFAULT 0,
	IsSerialized					BIT DEFAULT 0,
	UniqueSerialNumberSource		BIT DEFAULT 0
)

DECLARE @PartSubstitutions TABLE
(
	BillOfMaterialItemUID			BIGINT,
	ProductUID						BIGINT,
	ProductName						NVARCHAR(255),
	ProductDisplayName				NVARCHAR(255),
	RequiredQuantity				DECIMAL(19,5)
)

DECLARE @PartConsumptions TABLE
(
	WorkDefinitionSerialNumberConsumptionUID	BIGINT,
	WorkDefinitionUID				BIGINT,
	BillOfMaterialItemUID			BIGINT,
	BillOfMaterialSubstitutionUID	BIGINT,
	ProductUID						BIGINT,
	ProductName						NVARCHAR(255),
	ProductDisplayName				NVARCHAR(255),
	ConsumedQuantity				DECIMAL(19,5),
	FactorOfConversion				NUMERIC(18,5),
	SerialNumberUID					BIGINT,
	SerialNumberValue				NVARCHAR(255)
)

DECLARE @CalculationInputDependencies TABLE
(
	AttributeUID					BIGINT,
	WorkDefinitionUID				BIGINT,
	WorkDefinitionName				NVARCHAR(255),
	WorkDefinitionDescription		NVARCHAR(255)
)

DECLARE @ControlCharactertisticPlaceHolders TABLE
(	
	Idx									INT IDENTITY(1,1),
	ControlCharacteristicUID			BIGINT,
	ControlCharacteristicLinkUID		BIGINT,
	LimitSuffix							NVARCHAR(MAX),
	PlaceHolder							NVARCHAR(MAX),
	LimitName							NVARCHAR(255),
	LimitValue							NVARCHAR(25),
	ControlCharacteristicDisplayName	NVARCHAR(255),
	WorkDefinitionMasterUID				BIGINT
)

DECLARE	@Certificates	TABLE
(
	CertificateUID	BIGINT,
	CertificateName	NVARCHAR(255)
)

DECLARE @InclusiveLimitConfigurationWorkTable AS TABLE
(
	RowID					BIGINT	IDENTITY(1,1),
	InclusiveLimits			BIT,
	DataformatName			NVARCHAR(255),
	StartTime				DATETIMEOFFSET(7),
	EndTime					DATETIMEOFFSET(7)
)

DECLARE @LogSheetConfiguration TABLE
(
    WorkDefinitionUID       BIGINT,
	WorkDocumentUID			BIGINT,
    LogSheetUID             BIGINT,
    RequiresValue           BIT,
    RequiresEmptySignOff    BIT
)


--==============================================================================================================================================
--  ASSIGN VARIABLES
--  The following variables will be used as internal variables.
--==============================================================================================================================================
SET @ValidationCode     =	NULL
SET @ValidationMessage  =	NULL
SET @ValidationToken    =	NULL
SET @IsUserError		=	0
SET @NestingLevel   	= 	1
--==============================================================================================================================================
--  BEGIN LOGIC
--==============================================================================================================================================
BEGIN TRY
    --------------------------------------------------------------------------------------------------------------------------------------------
    --	Set Defaults
    --------------------------------------------------------------------------------------------------------------------------------------------   
    SET @Parameters	= 	N'Parameters: '
					+	N'@FilterJSON = ' + COALESCE(@FilterJSON, N'NULL')

    IF @IsDebugging = 1
	BEGIN
		SET @ErrorSection = N'Debug section 1'

		EXECUTE	dbo.MnfgCommon_LogErrorMessage_SP
				@op_ErrorGUID			= @ErrorGUID,
				@p_NestingLevel			= @NestingLevel,
				@p_NestedObjectName		= @NestedObjectName,
				@p_ObjectName			= @OBJECT_NAME,
				@p_ErrorSection			= @ErrorSection,
				@p_ErrorMessage			= @Parameters,
				@p_ErrorSeverityLevel	= @ERROR_INFO
	END
	--==========================================================================================================================================
    --  Extract and validate filters
    --==========================================================================================================================================
	SET @ErrorSection		= N'Extract and validate filters'
    SET @NestedObjectName	= N''

	IF  ISJSON(@FilterJSON)  = 0
    BEGIN
        SET @ValidationCode     = -2
        SET @ValidationMessage  = N'Invalid JSON=[' + COALESCE(@FilterJSON, N'NULL') + N']'

        RAISERROR(@ValidationMessage, @ERROR_SEVERITY, @ERROR_STATE)
    END
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Prepare data
	--------------------------------------------------------------------------------------------------------------------------------------------
	INSERT INTO @InclusiveLimitConfigurationWorkTable
	(
		InclusiveLimits,
		DataformatName,
		StartTime
	)
	SELECT  ext.inclusivelimits AS [inclusivelimits], 
			df.[name] AS [dataformatname], 
			ext.modified AS [starttime]
	FROM    dbo.ext_inclusivelimitsconfiguration    ext
	JOIN    dbo.dataformat  df  ON  ext.DataFormatUID = df.[UID]

	UPDATE  ilc
	SET     ilc.EndTime = (SELECT MIN(StartTime) FROM @InclusiveLimitConfigurationWorkTable ilc2 WHERE ilc2.StartTime > ilc.StartTime AND ilc2.DataformatName = ilc.DataformatName AND ilc2.InclusiveLimits <> ilc.InclusiveLimits)
	FROM    @InclusiveLimitConfigurationWorkTable  ilc

	DELETE	ilc
	FROM	@InclusiveLimitConfigurationWorkTable	ilc
	LEFT
	JOIN	@InclusiveLimitConfigurationWorkTable	ilc2	ON	ilc.dataformatname = ilc2.dataformatname
															AND	ilc.InclusiveLimits = ilc2.InclusiveLimits
															AND	ilc.StartTime >= ilc2.StartTime
															AND	ilc.EndTime <= ilc2.EndTime
															AND	ilc.RowID <> ilc2.RowID
	WHERE	ilc2.RowID IS NOT NULL

	DELETE	ilc
	FROM	@InclusiveLimitConfigurationWorkTable	ilc
	LEFT
	JOIN	@InclusiveLimitConfigurationWorkTable	ilc2	ON	ilc.dataformatname = ilc2.dataformatname
															AND	ilc.InclusiveLimits = ilc2.InclusiveLimits
															AND	ilc.StartTime >= ilc2.StartTime
															AND	ilc.EndTime IS NULL
															AND ilc2.EndTime IS NULL
															AND	ilc.RowID <> ilc2.RowID
	WHERE	ilc2.RowID IS NOT NULL

	INSERT INTO @InclusiveLimitConfigurationWorkTable
	(
		InclusiveLimits,
		DataformatName,
		StartTime,
		EndTime
	)
	SELECT  InclusiveLimits,
			N'DATE',
			StartTime,
			EndTime
	FROM    @InclusiveLimitConfigurationWorkTable
	WHERE   DataformatName = N'DATETIME'

	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Extract filters from JSON
	--------------------------------------------------------------------------------------------------------------------------------------------
	INSERT INTO @WorkDefinitionInfo
	(
		WorkDefinitionUID
	)
	SELECT	[value]
	FROM	OPENJSON (@FilterJSON, '$.WorkDefinitionUID')

	SET @NbWorkDefinitions =	(
									SELECT	COUNT(1)
									FROM	@WorkDefinitionInfo
								)

    SELECT	@ExecutionLimitsReferenceTime = COALESCE(ExecutionLimitsReferenceTime,@RETRIEVAL_TYPE_APPROVAL_TIME),
			@IsPartValidationEXTEnabled = COALESCE(IsPartValidationEXTEnabled, 0)
	FROM	OPENJSON (@FilterJSON)
	WITH 
	(
        ExecutionLimitsReferenceTime    NVARCHAR(MAX)	N'$.ExecutionLimitsReferenceTime',
		IsPartValidationEXTEnabled		BIT				N'$.IsPartValidationEXTEnabled'
	)
    --==========================================================================================================================================
    --  Get Work Definition Informations
    --==========================================================================================================================================
	SET @ErrorSection = N'Get Work Definition Informations'

	IF (
			EXISTS (
				SELECT	1
				FROM 	INFORMATION_SCHEMA.TABLES 
				WHERE 	TABLE_NAME = 'EXT_StepLogSheetLink'
			)
	)
	BEGIN
		UPDATE	wdi
		SET		wdi.StepName							= wd.[name],
				wdi.StepDescription						= wd.[description],
				wdi.StepLongDescription					= wd.longdescription,
				wdi.RejectMessage						= wd.rejectmessage,
				wdi.WorkDefinitionRootUID				= wd.workdefinitionrootuid,
				wdi.WorkDefinitionMasterUID				= wd.workdefinitionmasteruid,
				wdi.StepCompletedOn						= wd.completedon,
				wdi.CompletedOn							= CONVERT(NVARCHAR(50),wd.completedon,127),
				wdi.WorkDefinitionRootMasterVersionUID	= wdr.workdefinitionmasterrootversionuid,
				wdi.WorkDefinitionMasterGroupUID		= wdm.workdefinitionmastergroupuid,
				wdi.WorkDefinitionMasterTypeUID			= wd.workdefinitionmastertypeuid,
				wdi.WorkDefinitionMasterTypeName		= wdmt.[name],
				wdi.IsAutoWorkDefinitionForward			= wd.autoworkdefinitionforward,
				wdi.IsBypassable						= wd.bypassable,
				wdi.IsCurrent							= wd.iscurrent,
				wdi.StepSequenceNumber					= wd.sequencenumber,
				wdi.VersionUID							= v.[UID],
				wdi.VersionApprovedOn					= v.approvedon,
				wdi.InstructionalText					= wdm.instructionalsteptext,
				wdi.VerificationStepCompleted			= wd.verificationstepcompleted,
				wdi.ProductionEventUID					= wdr.productioneventuid,
				wdi.ProductUID							= wdr.productuid,
				wdi.ProductTypeUID						= p.producttypeuid,
				wdi.EquipmentUID						= wdr.equipmentuid,
				wdi.SelectionListItemUID				= wd.selectionlistitemuid,
				wdi.ProductionPathVersionLinkUID		= wdr.productionpathversionlinkuid,
				wdi.IsConditionalSkipped					= wd.isconditionalskipped,
				wdi.WorkDefinitionMasterConditionTypeName	= wdmct.[name],
				wdi.LogSheetWorkDocumentRootName			= wdocr.[Name]
		FROM	@WorkDefinitionInfo wdi
		JOIN	dbo.workdefinition wd						ON wd.[UID]		= wdi.WorkDefinitionUID
		JOIN	dbo.workdefinitionmaster wdm				ON wdm.[UID]	= wd.workdefinitionmasteruid
		JOIN	dbo.workdefinitionmastertype wdmt			ON wdmt.[UID]	= wd.workdefinitionmastertypeuid
		JOIN	dbo.workdefinitionroot wdr					ON wdr.[UID]	= wd.workdefinitionrootuid
		JOIN	dbo.workdefinitionmasterrootversion wdmrv	ON wdmrv.[UID]	= wdr.workdefinitionmasterrootversionuid
		JOIN	dbo.[version] v								ON v.[UID]		= wdmrv.versionuid
		JOIN	dbo.[product] p								ON p.[uid]		= wdr.productuid
		LEFT
		JOIN	dbo.workdefinitionmasterconditiontype wdmct	ON wdmct.[uid]	= wdm.workdefinitionmasterconditiontypeuid
		LEFT
		JOIN	dbo.EXT_StepLogSheetLink slsl				ON wd.WorkDefinitionMasterUID = slsl.WorkDefinitionMasterUID
		LEFT
		JOIN	dbo.WorkDocument wdoc						ON wdoc.[UID] = slsl.WorkDocumentUID 
		LEFT
		JOIN	dbo.WorkDocumentroot wdocr					ON wdocr.[UID] = wdoc.WorkDocumentRootUID
		
	END
	ELSE
	BEGIN
		UPDATE	wdi
		SET		wdi.StepName							= wd.[name],
				wdi.StepDescription						= wd.[description],
				wdi.StepLongDescription					= wd.longdescription,
				wdi.RejectMessage						= wd.rejectmessage,
				wdi.WorkDefinitionRootUID				= wd.workdefinitionrootuid,
				wdi.WorkDefinitionMasterUID				= wd.workdefinitionmasteruid,
				wdi.CompletedOn							= wd.completedon,
				wdi.WorkDefinitionRootMasterVersionUID	= wdr.workdefinitionmasterrootversionuid,
				wdi.WorkDefinitionMasterGroupUID		= wdm.workdefinitionmastergroupuid,
				wdi.WorkDefinitionMasterTypeUID			= wd.workdefinitionmastertypeuid,
				wdi.WorkDefinitionMasterTypeName		= wdmt.[name],
				wdi.IsAutoWorkDefinitionForward			= wd.autoworkdefinitionforward,
				wdi.IsBypassable						= wd.bypassable,
				wdi.IsCurrent							= wd.iscurrent,
				wdi.StepSequenceNumber					= wd.sequencenumber,
				wdi.VersionUID							= v.[UID],
				wdi.VersionApprovedOn					= v.approvedon,
				wdi.InstructionalText					= wdm.instructionalsteptext,
				wdi.VerificationStepCompleted			= wd.verificationstepcompleted,
				wdi.ProductionEventUID					= wdr.productioneventuid,
				wdi.ProductUID							= wdr.productuid,
				wdi.EquipmentUID						= wdr.equipmentuid,
				wdi.SelectionListItemUID				= wd.selectionlistitemuid,
				wdi.ProductionPathVersionLinkUID		= wdr.productionpathversionlinkuid,
				wdi.IsConditionalSkipped					= wd.isconditionalskipped,
				wdi.WorkDefinitionMasterConditionTypeName	= wdmct.[name]
		FROM	@WorkDefinitionInfo wdi
		JOIN	dbo.workdefinition wd						ON wd.[UID]		= wdi.WorkDefinitionUID
		JOIN	dbo.workdefinitionmaster wdm				ON wdm.[UID]	= wd.workdefinitionmasteruid
		JOIN	dbo.workdefinitionmastertype wdmt			ON wdmt.[UID]	= wd.workdefinitionmastertypeuid
		JOIN	dbo.workdefinitionroot wdr					ON wdr.[UID]	= wd.workdefinitionrootuid
		JOIN	dbo.workdefinitionmasterrootversion wdmrv	ON wdmrv.[UID]	= wdr.workdefinitionmasterrootversionuid
		JOIN	dbo.[version] v								ON v.[UID]		= wdmrv.versionuid
		LEFT
		JOIN	dbo.workdefinitionmasterconditiontype wdmct	ON wdmct.[uid]	= wdm.workdefinitionmasterconditiontypeuid
	END

	-- Get LogSheet configuration for Instructional steps
	INSERT INTO @LogSheetConfiguration
	(
		WorkDefinitionUID,
		WorkDocumentUID,
		RequiresValue,
		RequiresEmptySignOff
	)
	SELECT  DISTINCT
			wdi.WorkDefinitionUID,
			wd.[UID],
			COALESCE(slslt.RequiresValue, 0),
			COALESCE(slslt.RequiresEmptySignOff, 0)
	FROM    @WorkDefinitionInfo wdi
	JOIN    dbo.EXT_StepLogSheetLink slsl       ON slsl.WorkDefinitionMasterUID = wdi.WorkDefinitionMasterUID
	JOIN    dbo.EXT_StepLogSheetLinkType slslt  ON slsl.EXT_StepLogSheetLinkTypeUID = slslt.[UID]
	JOIN    dbo.WorkDocument wd                 ON wd.[UID] = slsl.WorkDocumentUID
	WHERE   wdi.WorkDefinitionMasterTypeName = @STEP_TYPE_INSTRUCTIONAL

	-- Update completion status for Instructional steps  
	UPDATE  wdi
	SET     wdi.StepIsCompleted = 
			CASE
				-- No WorkDocument linked or Optional configuration
				WHEN NOT EXISTS (
					SELECT  1 
					FROM    @LogSheetConfiguration lsc 
					WHERE   lsc.WorkDefinitionUID = wdi.WorkDefinitionUID
				) THEN 1

				-- Mandatory configuration (RequiresValue = 1, RequiresEmptySignOff = 0)
				WHEN EXISTS (
					SELECT  1 
					FROM    @LogSheetConfiguration lsc
					WHERE   lsc.WorkDefinitionUID = wdi.WorkDefinitionUID
					AND     lsc.RequiresValue = 1
					AND     lsc.RequiresEmptySignOff = 0
				) THEN
					CASE WHEN EXISTS (
						-- Check for attribute values
						SELECT  1
						FROM    @LogSheetConfiguration lsc
						JOIN    dbo.WorkDocumentAttributeLink wdal ON wdal.WorkDocumentUID = lsc.WorkDefinitionUID
						JOIN    dbo.AttributeValue av             ON av.AttributeUID = wdal.AttributeUID
																AND av.WorkDocumentUID = wdal.WorkDocumentUID
						JOIN    dbo.WorkDefinitionRun wdr         ON wdr.WorkDefinitionUID = wdi.WorkDefinitionUID
						WHERE   lsc.WorkDefinitionUID = wdi.WorkDefinitionUID
						AND     wdal.Enabled = 1
						AND     av.TimestampResult BETWEEN wdr.StartTime AND COALESCE(wdr.EndTime, @CURRENT_TIME)
						AND     av.Value IS NOT NULL
					) THEN 1 ELSE 0 END

				-- EmptySignOff configuration (RequiresValue = 1, RequiresEmptySignOff = 1)
				WHEN EXISTS (
					SELECT  1 
					FROM    @LogSheetConfiguration lsc
					WHERE   lsc.WorkDefinitionUID = wdi.WorkDefinitionUID
					AND     lsc.RequiresValue = 1
					AND     lsc.RequiresEmptySignOff = 1
				) THEN
					CASE WHEN EXISTS (
						-- Check for attribute values
						SELECT  1
						FROM    @LogSheetConfiguration lsc
						JOIN    dbo.WorkDocumentAttributeLink wdal ON wdal.WorkDocumentUID = lsc.WorkDocumentUID
						JOIN    dbo.AttributeValue av             ON av.AttributeUID = wdal.AttributeUID
																AND av.WorkDocumentUID = wdal.WorkDocumentUID
						JOIN    dbo.WorkDefinitionRun wdr         ON wdr.WorkDefinitionUID = wdi.WorkDefinitionUID
						WHERE   lsc.WorkDefinitionUID = wdi.WorkDefinitionUID
						AND     wdal.Enabled = 1
						AND     av.TimestampResult BETWEEN wdr.StartTime AND COALESCE(wdr.EndTime, @CURRENT_TIME)
						AND     av.Value IS NOT NULL
					) OR EXISTS (
						-- Check for empty signoff
						SELECT  1
						FROM    @LogSheetConfiguration lsc
						JOIN    dbo.WorkDefinitionRun wdr      ON wdr.WorkDefinitionUID = wdi.WorkDefinitionUID
						JOIN    dbo.ext_esignaturedata esd         ON esd.WorkDefinitionUID = wdi.WorkDefinitionUID
															AND esd.ext_esignaturetypeuid = (SELECT [UID] FROM dbo.ext_esignaturetype WHERE Name = 'EmptySignOff')
															AND esd.signedon BETWEEN wdr.StartTime AND COALESCE(wdr.EndTime, @CURRENT_TIME)
						WHERE   lsc.WorkDefinitionUID = wdi.WorkDefinitionUID
					) THEN 1 ELSE 0 END

				ELSE 0
			END
	FROM    @WorkDefinitionInfo wdi
	WHERE   wdi.WorkDefinitionMasterTypeName = @STEP_TYPE_INSTRUCTIONAL
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Calculate duration
	--------------------------------------------------------------------------------------------------------------------------------------------
	UPDATE	wdi
	SET		wdi.Duration =	(
								SELECT	SUM(DATEDIFF(second, wdr.starttime, COALESCE(wdr.endtime, @CURRENT_TIME)))
								FROM	dbo.workdefinitionrun wdr	
								WHERE	wdr.workdefinitionuid = wdi.WorkDefinitionUID
							)
	FROM	@WorkDefinitionInfo wdi
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Get most recent run status
	--------------------------------------------------------------------------------------------------------------------------------------------
	;WITH WDStarts
	(
		WorkDefinitionUID,
		StartTime
	)
	AS
	(
		SELECT	wdr.workdefinitionuid,
				MAX(wdr.starttime)
		FROM	@WorkDefinitionInfo wdi
		JOIN	dbo.workdefinitionrun wdr	ON	wdr.workdefinitionuid = wdi.WorkDefinitionUID
		GROUP
		BY		wdr.workdefinitionuid
	),
	WDRuns
	(
		WorkDefinitionUID,
		StatusDefinitionUID,
		StatusDefinitionName
	)
	AS
	(
		SELECT	s.workdefinitionuid,
				wdr.statusdefinitionuid,
				sd.internalname
		FROM	WDStarts s
		JOIN	dbo.workdefinitionrun wdr	ON	wdr.workdefinitionuid	= s.WorkDefinitionUID
											AND	wdr.starttime			= s.StartTime
		JOIN	dbo.statusdefinition sd		ON	sd.[uid]				= wdr.statusdefinitionuid
	)
	UPDATE	wdi
	SET		wdi.StepStatusDefinitionName = r.StatusDefinitionName
	FROM	@WorkDefinitionInfo wdi
	JOIN	WDRuns r					ON	r.WorkDefinitionUID = wdi.WorkDefinitionUID
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	We use the maximum execution time it took to execute the step. That way, if execution is in parallel mode, the execution time 
	--	will be the maximum time shown. In majority of cases, execution time will be the same for all serial numbers. It will differ if a
	--	serial number is stopped than restart alongside another stopped serial number, but coming from a different execution.
	--------------------------------------------------------------------------------------------------------------------------------------------
	SET @MaxDuration =	(
							SELECT	MAX(Duration)
							FROM	@WorkDefinitionInfo
						)

	UPDATE	wdi 
	Set		wdi.ParallelExecutionSequenceNumber = wdr.parallelexecutionsequencenumber,
			wdi.SerialNumberUID					= wdr.serialnumberuid,
            wdi.SerialNumberValue				= sn.[value],
			wdi.Duration						= @MaxDuration
	FROM	@WorkDefinitionInfo wdi	
	JOIN	dbo.workdefinitionroot wdr 	ON wdr.[UID] = wdi.WorkDefinitionRootUID
    LEFT
	JOIN	dbo.serialnumber sn			ON	sn.[uid] = wdr.serialnumberuid

	SET @CurrentStepSequenceNumber =	(
											SELECT	TOP 1 
													StepSequenceNumber
											FROM	@WorkDefinitionInfo
											ORDER
											BY		ParallelExecutionSequenceNumber ASC
										)
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Get Secondary Signoff information
	--------------------------------------------------------------------------------------------------------------------------------------------
	UPDATE 	wdi
	SET 	SecondarySignoffRequired = 1,
			SecondarySignoffUserGroup = usergroupname
	FROM	@WorkDefinitionInfo wdi
	JOIN 	dbo.workdefinitionmasterscheduledesignature wdmss 	ON 	wdmss.workdefinitionmasteruid 	= wdi.WorkDefinitionMasterUID
																AND wdmss.serialnumberuid 			= wdi.SerialNumberUID
    --==========================================================================================================================================
    --	Get Target Cycle Time Informations
    --==========================================================================================================================================
	SET @ErrorSection = N'Get Target Cycle Time Informations'
   
	SET @TargetCycleTimeCCUID = (
									SELECT	cc.[UID]
									FROM	dbo.controlcharacteristic cc
									JOIN	dbo.controlcharacteristictypelink cctl	ON cctl.controlcharacteristicuid	= cc.[UID]
									JOIN	dbo.controlcharacteristictype cct		ON cct.[UID]						= cctl.controlcharacteristictypeuid
									WHERE	cc.[name]	= @TARGET_CYCLE_TIME_CC
									AND		cct.[name]	= @CWC_CC_TYPE
								)
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	We use top 1 because all work definitions from a parallel execution will have the same Target Cycle Time
	--------------------------------------------------------------------------------------------------------------------------------------------
	SET @CurrentStepTargetCycleTimeSeconds = (
		SELECT	TOP 1 
				ccl.nominalvalue
		FROM	@WorkDefinitionInfo wdi
		JOIN	dbo.controlcharacteristiclink ccl	ON	ccl.workdefinitionmastergroupuid = wdi.WorkDefinitionMasterGroupUID
		WHERE	ccl.StartTime					< wdi.VersionApprovedOn
		AND		ccl.controlcharacteristicuid	= @TargetCycleTimeCCUID
		AND		(
					ccl.EndTime					>= wdi.VersionApprovedOn
					OR
					ccl.EndTime					IS NULL
				)
		ORDER
		BY		ccl.StartTime DESC
	)

	SET @CurrentStepTargetCycleTime = CONVERT(NVARCHAR(50), DATEADD(ms, CONVERT(FLOAT, @CurrentStepTargetCycleTimeSeconds * 1000.0), 0), 108)
	--==========================================================================================================================================
    --  Get All Steps Execution Time
    --==========================================================================================================================================
	SET @ErrorSection = N'Get All Steps Execution Time'
    --------------------------------------------------------------------------------------------------------------------------------------------
	--	We use the maximum execution time it took to execute the step. That way, if execution is in parallel mode, the total execution time 
	--	will be the maximum time shown. In majority of cases, execution time will be the same for all serial numbers. It will differ if a
	--	serial number is stopped than restart alongside another stopped serial number, but coming from a different execution.
	--------------------------------------------------------------------------------------------------------------------------------------------
	INSERT INTO @AllStepExecutionInfo
	(
		WorkDefinitionMasterGroupUID,
		SequenceNumber,
		Duration,
		VersionUID
	)
	SELECT	wdm.workdefinitionmastergroupuid,
			wd.sequencenumber,
			MAX(wd.duration),
			wdi.VersionUID
	FROM	@WorkDefinitionInfo wdi
	JOIN	dbo.workdefinition wd			ON	wd.workdefinitionrootuid	= wdi.WorkDefinitionRootUID
	JOIN	dbo.workdefinitionmaster wdm	ON	wdm.[UID]					= wd.workdefinitionmasteruid
	GROUP 
	BY		wd.sequencenumber,
			wdm.workdefinitionmastergroupuid,
			wdi.VersionUID
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Get when the version was approved
	--------------------------------------------------------------------------------------------------------------------------------------------
	UPDATE	aei
	SET		aei.VersionApprovedOn = v.approvedon
	FROM	@AllStepExecutionInfo aei 
	JOIN	dbo.[version] v				ON v.[uid] = aei.VersionUID
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Get most recent target cycle time for each step group
	--------------------------------------------------------------------------------------------------------------------------------------------
	;WITH TargetCycleTimeLimits 
	AS 
	(
		SELECT	ccl.workdefinitionmastergroupuid,
				ccl.nominalvalue,
				ROW_NUMBER() OVER	(
										PARTITION BY	ccl.workdefinitionmastergroupuid 
										ORDER BY		ccl.starttime DESC
									) AS [RowNumber]
		FROM	@AllStepExecutionInfo asei			
		JOIN	dbo.controlcharacteristiclink ccl	ON	ccl.WorkDefinitionMasterGroupUID = asei.WorkDefinitionMasterGroupUID
		WHERE	ccl.controlcharacteristicuid	= @TargetCycleTimeCCUID
		AND		ccl.StartTime					< asei.VersionApprovedOn
		AND		(
					ccl.EndTime					>=	asei.VersionApprovedOn
					OR
					ccl.EndTime					IS NULL
				)
	)
	UPDATE	ei
	SET		ei.TargetCycleTime = tctl.nominalvalue
	FROM	@AllStepExecutionInfo ei 
	JOIN	TargetCycleTimeLimits tctl	ON	tctl.workdefinitionmastergroupuid = ei.WorkDefinitionMasterGroupUID
	WHERE	tctl.rownumber = 1
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Get planned time
	--------------------------------------------------------------------------------------------------------------------------------------------
	SET @PlannedEndTime =		(
									SELECT	DATEADD(SECOND, SUM(TargetCycleTime), @CURRENT_TIME)
									FROM	@AllStepExecutionInfo
									WHERE	SequenceNumber >= @CurrentStepSequenceNumber
								)
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Calculate the total execution time
	--------------------------------------------------------------------------------------------------------------------------------------------
	SET @TotalTime =			(
									SELECT	CONVERT(BIGINT, SUM(Duration), 0)
									FROM	@AllStepExecutionInfo
								)
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Transform the execution time into a HH:MM:SS string
	--------------------------------------------------------------------------------------------------------------------------------------------
	SET @AllStepExecutionTime = (
									SELECT	CONVERT(NVARCHAR(50), DATEADD(SECOND, SUM(Duration), 0), 108)
									FROM	@AllStepExecutionInfo
								)
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	If more than one day elapsed, add the number of days in front of the execution time
	--------------------------------------------------------------------------------------------------------------------------------------------
	IF @TotalTime >= 86400
	BEGIN
		SET @Days					= @TotalTime / 86400.0
		SET @AllStepExecutionTime	= CONVERT(VARCHAR(5), @Days) + 'd ' + @AllStepExecutionTime
	END
  
  	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Set if we only use the AttributeUID in RoutingItemAttributeLink
	--------------------------------------------------------------------------------------------------------------------------------------------
	IF EXISTS   (
                    SELECT	1
                    FROM    @WorkDefinitionInfo wdi
                    JOIN    dbo.WorkDefinitionRoot                      wdr     ON  wdr.[UID]                                       = wdi.WorkDefinitionRootUID
                    JOIN    dbo.RoutingItem                             ri      ON  ri.ProductionPathVersionLinkUID                 = wdr.ProductionPathVersionLinkUID
                    JOIN    dbo.RoutingItemWorkDefinitionMasterRootLink riwdmrl ON  riwdmrl.RoutingItemUID                          = ri.[UID]
                    JOIN    dbo.RoutingItemAttributeLink                rial    ON  rial.WorkDefinitionMasterUID                    = wdi.WorkDefinitionMasterUID
                                                                                AND rial.RoutingItemWorkDefinitionMasterRootLinkUID = riwdmrl.[UID]
                    WHERE   rial.AttributeUID   IS NOT NULL
                )
    BEGIN
        SET @OnlyAttribute = 1
    END
    --------------------------------------------------------------------------------------------------------------------------------------------
	--	Set if we only use the AttributeParallelExecutionGroupingUID in RoutingItemAttributeLink
	--------------------------------------------------------------------------------------------------------------------------------------------
    IF EXISTS   (
                    SELECT	1
                    FROM    @WorkDefinitionInfo wdi
                    JOIN    dbo.WorkDefinitionRoot                      wdr     ON  wdr.[UID]                                       = wdi.WorkDefinitionRootUID
                    JOIN    dbo.RoutingItem                             ri      ON  ri.ProductionPathVersionLinkUID                 = wdr.ProductionPathVersionLinkUID
                    JOIN    dbo.RoutingItemWorkDefinitionMasterRootLink riwdmrl ON  riwdmrl.RoutingItemUID                          = ri.[UID]
                    JOIN    dbo.RoutingItemAttributeLink                rial    ON  rial.WorkDefinitionMasterUID                    = wdi.WorkDefinitionMasterUID
                                                                                AND rial.RoutingItemWorkDefinitionMasterRootLinkUID = riwdmrl.[UID]
                    WHERE   rial.AttributeParallelExecutionGroupingUID  IS NOT NULL
                )
    BEGIN
        SET @OnlyAttributeGroup = 1
    END
    --------------------------------------------------------------------------------------------------------------------------------------------
	--	If only AttributeUID are linked (not null), we set MeasureSingleValue to false
	--------------------------------------------------------------------------------------------------------------------------------------------
	IF @OnlyAttribute = 1
	BEGIN
		SET @MeasureSingleValue = 1
	END
    ELSE IF @OnlyAttributeGroup = 1
    BEGIN
		SET @MeasureSingleValue = 0
	END

    --==========================================================================================================================================
	--  Put the list of placeholders in a table
    --==========================================================================================================================================
	-- Concept of Dynamic Content is not usable in parallel mode
	IF	(
			SELECT	COUNT(1)
			FROM	@WorkDefinitionInfo wdi
		)	= 1
	BEGIN
		SET @StartPosition	= 1
		SET @EndPosition	= 1
		SET	@ContentText =	(
								SELECT	wdi.StepLongDescription
								FROM	@WorkDefinitionInfo wdi
							)
		SET	@WDMUID =	(
							SELECT	wdi.WorkDefinitionMasterUID
							FROM	@WorkDefinitionInfo wdi
						)

		SET @WDMGroupUID =	(
								SELECT	wdi.WorkDefinitionMasterGroupUID
								FROM	@WorkDefinitionInfo wdi
							)

		SET	@ProductUID =	(
								SELECT	wdi.ProductUID
								FROM	@WorkDefinitionInfo wdi
							)
                            
		SET	@ProductTypeUID =	(
									SELECT	p.producttypeuid
									FROM	dbo.product p
									WHERE 	p.[uid] = @ProductUID
								)
                            
        IF @ExecutionLimitsReferenceTime = @RETRIEVAL_TYPE_CURRENT_TIME
        BEGIN
            SET	@LimitTimestamp = (   
									SELECT	COALESCE(wdi.StepCompletedOn, @CURRENT_TIME)
									FROM	@WorkDefinitionInfo wdi
								)
        END
        ELSE IF @ExecutionLimitsReferenceTime = @RETRIEVAL_TYPE_APPROVAL_TIME
        BEGIN
            SET	@LimitTimestamp =   (   
                                        SELECT	COALESCE(wdi.VersionApprovedOn, @CURRENT_TIME)
								        FROM	@WorkDefinitionInfo wdi
                                    )
        END
        ELSE
        BEGIN
            SET	@LimitTimestamp =   (   
                                        SELECT	COALESCE(wdi.VersionApprovedOn, @CURRENT_TIME)
								        FROM	@WorkDefinitionInfo wdi
                                    )
        END

		IF	CHARINDEX('{{{', @ContentText, @StartPosition) <> 0 
		AND CHARINDEX('}}}', @ContentText, @StartPosition) <> 0
		BEGIN
			------------------------------------------------------------------------------------------------------------------------------------
			--  Put the list of placeholders in a table
			------------------------------------------------------------------------------------------------------------------------------------
			WHILE	@StartPosition <= LEN(@ContentText)
			BEGIN
				SET @StartPosition	= CHARINDEX('{{{', @ContentText, @StartPosition)
				SET @EndPosition	= CHARINDEX('}}}', @ContentText, @StartPosition)

				IF	@StartPosition = 0 
                OR 	@EndPosition = 0
				BEGIN
					BREAK
				END

				SET @NextStartPosition = CHARINDEX('{{{', @ContentText, @StartPosition + 1)
				IF	@EndPosition > @NextStartPosition
                AND @NextStartPosition != 0
				BEGIN
					SET @StartPosition = @StartPosition + 1
					CONTINUE
				END

				SET @Placeholder			=	SUBSTRING	(
																@ContentText,  
																@StartPosition,  
																@EndPosition - @StartPosition + Len('{{{')
															)

				SET @PlaceholderIndex		=	CHARINDEX ('_', @Placeholder)
				IF @PlaceholderIndex = 0
				BEGIN 
					SET @StartPosition = @EndPosition + 1
					CONTINUE
				END

				SET @PlaceholderAttUID		=	SUBSTRING	(
																@Placeholder, 
																CHARINDEX ('{{{', @Placeholder) + 3, 
																LEN(@Placeholder) - 
																LEN(LEFT(@Placeholder, CHARINDEX ('{{{', @Placeholder))) - 
																LEN(RIGHT(@Placeholder, LEN(@Placeholder) - 
																@PlaceholderIndex)) - 3
															)

				SET @PlaceholderLimitSuffix =	SUBSTRING	(
																@Placeholder, 
																@PlaceholderIndex + 1, 
																CHARINDEX('}}}',@Placeholder) - @PlaceholderIndex - 1
															)
				--------------------------------------------------------------------------------------------------------------------------------
				-- Get every control characteristicuid from the placeholders
				--------------------------------------------------------------------------------------------------------------------------------
				INSERT INTO @ControlCharacteristicRequests
				(
					AttributeUID,
					WorkDefinitionMasterUID,
					limitSuffix,
					placeHolder,
					productUID,
					ProductTypeUID,
					WorkDefinitionMastergroupUID,
					startTime,
					endTime
				)
				VALUES 
				(
					TRY_CONVERT(BIGINT, @PlaceholderAttUID),
					@WDMUID,
					@PlaceholderLimitSuffix,
					@Placeholder,
					@ProductUID,
					@ProductTypeUID,
					@WDMGroupUID,
					@LimitTimestamp,
					@LimitTimestamp
				)

				SET @StartPosition = @EndPosition + 1
			END
			------------------------------------------------------------------------------------------------------------------------------------
			--	Prepare control characteristic limit request JSON
			------------------------------------------------------------------------------------------------------------------------------------
			-- If execution retrieval time is the work instruction approval time, we can use the SP to retrieve limits for placeholder
			IF (@ExecutionLimitsReferenceTime = @RETRIEVAL_TYPE_APPROVAL_TIME)
			BEGIN
			SET @ControlCharacteristicInputJSON	=	(
														SELECT 	CONVERT(BIT,'false') AS [UsePagination],
																CONVERT(BIT,'false') AS [FromLimitConfigurationMashup],
																CONVERT(BIT,'false') AS [IsExport],
																(
																	SELECT	DISTINCT
																			@CONTEXT_CWC_WORK_INSTRUCTIONS	AS [ApplicationContext],
																			1								AS [LimitRequestIdentifier],
																			CONVERT(DATETIME,startTime)		AS [StartTime],
																			CONVERT(DATETIME,endTime)		AS [EndTime],
																			(
																				SELECT	DISTINCT
																						AttributeUID			AS [UID],
																						CONVERT(BIT,N'false')	AS [IsAttributeGroup]
																				FROM	@ControlCharacteristicRequests
																				WHERE	AttributeUID IS NOT NULL
																				FOR JSON PATH
																			) AS [Attributes],
																			(
																				SELECT	DISTINCT
																						productUID AS [UID]
																				FROM	@ControlCharacteristicRequests
																				FOR JSON PATH
																			) AS [Products],
																			(
																				SELECT	DISTINCT
																						WorkDefinitionMasterUID AS [UID],
																						IIF(@ExecutionLimitsReferenceTime = @RETRIEVAL_TYPE_APPROVAL_TIME,CONVERT(BIT,N'false'),CONVERT(BIT,N'true'))	AS [IncludeDraft]
																				FROM	@ControlCharacteristicRequests
																				FOR JSON PATH
																			) AS [WorkDefinitionMasters]
																	FROM	@ControlCharacteristicRequests
																	FOR JSON PATH
																) AS [LimitRequests]
															FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
														)
			------------------------------------------------------------------------------------------------------------------------------------
			--	Get control characteristic limits
			------------------------------------------------------------------------------------------------------------------------------------
			SET @NestedObjectName = 'dbo.PTC_FSU_CORE_CCC_GetControlCharacteristicLimits_SP'

			INSERT INTO @ControlCharacteristics
			(	
				LimitRequestIdentifier					,
				StartTime								,
				EndTime									,
				ControlCharacteristicLinkUID			,
				ControlCharacteristicUID				,
				ControlCharacteristicName				,
				ControlCharacteristicDisplayName		,
				UnitOfMeasureUID						,
				UnitOfMeasureName						,
				UnitOfMeasureDisplayName				,
				DataFormatUID							,
				DataFormatName							,
				EquipmentUID							,
				EquipmentName							,
				EquipmentDisplayName					,
				ProductUID								,
				ProductName								,
				ProductTypeUID							,
				ProductTypeName							,
				ProductTypeDisplayName					,
				AttributeUID							,
				AttributeName							,
				AttributeDisplayName					,
				AttributeParallelExecutionGroupingUID	,
				AttributeParallelExecutionGroupingName	,
				WorkDefinitionMasterUID					,
				WorkDefinitionMasterName				,
				WorkDefinitionMasterRootUID				,
				WorkDefinitionMasterRootName			,
				WorkDefinitionMasterRootDisplayName		,
				[Version]								,
				[Status]								,
				SmartToolUID							,
				SmartToolName							,
				SmartToolDisplayName					,
				WorkDocumentUID							,
				WorkDocumentName						,
				WorkDocumentDisplayName					,
				LotNumberUID							,
				LotNumberName							,
				IsInherited								,
				LowEntry								,
				LowReject								,
				LowWarning								,
				LowUser									,
				NominalValue							,
				HighUser								,
				HighWarning								,
				HighReject								,
				HighEntry								,
				TotalCount								
			)
			EXEC dbo.PTC_FSU_CORE_CCC_GetControlCharacteristicLimits_SP
					@op_ErrorGUID			= @ErrorGUID				OUTPUT,
					@op_ValidationCode		= @ValidationCode			OUTPUT,
					@op_ValidationMessage	= @ValidationMessage		OUTPUT,
					@p_InputJSON			= @ControlCharacteristicInputJSON
			END

			-- If execution retrieval time is current time, we need to find which limits were active at the time of the attribute value timestamp
			ELSE IF (
					@ExecutionLimitsReferenceTime = @RETRIEVAL_TYPE_CURRENT_TIME 
			)
			BEGIN
				INSERT INTO @ControlCharacteristics
				(
					LimitRequestIdentifier					,
					StartTime								,
					EndTime									,
					ControlCharacteristicLinkUID			,
					AttributeUID							,
					LowReject								,
					LowWarning								,
					NominalValue							,
					HighWarning								,
					HighReject								,
					[Priority]
				)
				SELECT	ccr.RcdIdx,
						ccl.starttime,
						ccl.endtime,
						ccl.[uid],
						ccl.attributeuid,
						ccl.lowreject,
						ccl.lowwarning,
						ccl.nominalvalue,
						ccl.highwarning,
						ccl.highreject,
						CASE 
							WHEN ccl.workdefinitionmastergroupuid IS NOT NULL AND ccl.productuid IS NOT NULL THEN 1
							WHEN ccl.workdefinitionmastergroupuid IS NOT NULL AND ccl.producttypeuid IS NOT NULL THEN 2
							WHEN ccl.workdefinitionmastergroupuid IS NOT NULL THEN 3
							WHEN ccl.productuid IS NOT NULL THEN 4
							WHEN ccl.producttypeuid IS NOT NULL THEN 5
							ELSE 6
						END
				FROM	@ControlCharacteristicRequests ccr
				JOIN	dbo.controlcharacteristiclink ccl	ON  (ccl.attributeuid = ccr.AttributeUID)
															AND	(ccl.productuid = ccr.ProductUID OR ccl.productuid IS NULL)
															AND	(ccl.producttypeuid = ccr.ProductTypeUID OR ccl.producttypeuid IS NULL)
															AND	(ccl.workdefinitionmastergroupuid = ccr.WorkDefinitionMasterGroupUID OR ccl.WorkDefinitionMasterGroupUID IS NULL)
															AND		ccl.starttime				<=	@LimitTimestamp
															AND		(
																		ccl.endtime				> @LimitTimestamp
																		OR
																		ccl.endtime				IS NULL
																	)

				;WITH LimitsCTE
				AS 
				( 
					SELECT	ROW_NUMBER() OVER (PARTITION BY ccd.LimitRequestIdentifier ORDER BY ccd.[Priority] ASC) AS 'RowNumber',
							ccd.ControlCharacteristicLinkUID,
							ccd.LowReject,
							ccd.LowWarning,
							ccd.NominalValue,
							ccd.HighWarning,
							ccd.HighReject
					FROM	@ControlCharacteristics ccd
				)

				DELETE	cc
				FROM 	@ControlCharacteristics cc
				JOIN	LimitsCTE cte	ON cte.ControlCharacteristicLinkUID = cc.ControlCharacteristicLinkUID
				WHERE 	cte.RowNumber <> 1
			END

			------------------------------------------------------------------------------------------------------------------------------------
			--  Put the list of characteristics in a table
			------------------------------------------------------------------------------------------------------------------------------------
			INSERT	INTO @ControlCharactertisticPlaceHolders
			(
					ControlCharacteristicUID,
					ControlCharacteristicLinkUID,
					ControlCharacteristicDisplayName,
					WorkDefinitionMasterUID,
					LimitSuffix,
					PlaceHolder,
					LimitName
			)
			SELECT	cclr.AttributeUID,
					ccrov.controlCharacteristicLinkUID,
					ccrov.controlCharacteristicDisplayName,
					@WDMUID,
					cclr.limitSuffix,
					cclr.placeHolder,
					CASE 
						WHEN 	cclr.limitSuffix = @LOW_CONTROL_SUFFIX
								THEN	N'LowControl'
							WHEN	cclr.limitSuffix = @LOW_ENTRY_SUFFIX
								THEN	N'LowEntry'
							WHEN	cclr.limitSuffix = @LOW_REJECT_SUFFIX
								THEN	N'LowReject'
							WHEN	cclr.limitSuffix = @LOW_WARNING_SUFFIX
								THEN	N'LowWarning'
							WHEN	cclr.limitSuffix = @LOW_USER_SUFFIX
								THEN	N'LowUser'
							WHEN	cclr.limitSuffix = @NOMINAL_SUFFIX
								THEN	N'Nominal'
							WHEN	cclr.limitSuffix = @HIGH_CONTROL_SUFFIX
								THEN	N'HighControl'
							WHEN	cclr.limitSuffix = @HIGH_USER_SUFFIX
								THEN	N'HighUser'
							WHEN	cclr.limitSuffix = @HIGH_WARNING_SUFFIX
								THEN	N'HighWarning'
							WHEN	cclr.limitSuffix = @HIGH_REJECT_SUFFIX
								THEN	N'HighReject'
							WHEN	cclr.limitSuffix = @HIGH_ENTRY_SUFFIX
								THEN	N'HighEntry'
							ELSE	
								N''
					END
			FROM	@ControlCharacteristicRequests			cclr
			JOIN	@ControlCharacteristics	ccrov	ON	cclr.AttributeUID	= ccrov.AttributeUID

			UPDATE	ccs
			SET		ccs.LimitValue = CASE 
							WHEN	ccs.limitSuffix = @LOW_ENTRY_SUFFIX
								THEN	ccrov.lowentry
							WHEN	ccs.limitSuffix = @LOW_REJECT_SUFFIX
								THEN	ccrov.lowreject
							WHEN	ccs.limitSuffix = @LOW_WARNING_SUFFIX
								THEN	ccrov.lowwarning
							WHEN	ccs.limitSuffix = @LOW_USER_SUFFIX
								THEN	ccrov.lowuser
							WHEN	ccs.limitSuffix = @NOMINAL_SUFFIX
								THEN	ccrov.nominalvalue
							WHEN	ccs.limitSuffix = @HIGH_USER_SUFFIX
								THEN	ccrov.highuser
							WHEN	ccs.limitSuffix = @HIGH_WARNING_SUFFIX
								THEN	ccrov.highwarning
							WHEN	ccs.limitSuffix = @HIGH_REJECT_SUFFIX
								THEN	ccrov.highreject
							WHEN	ccs.limitSuffix = @HIGH_ENTRY_SUFFIX
								THEN	ccrov.highentry
							ELSE	
								N''
					END
			FROM	@ControlCharactertisticPlaceHolders ccs
			JOIN	@ControlCharacteristics	ccrov	ON ccrov.controlCharacteristicLinkUID	= ccs.ControlCharacteristicLinkUID
			JOIN	dbo.limittype lt				ON lt.[name]							= ccs.LimitName
			------------------------------------------------------------------------------------------------------------------------------------
			--  Change limit in the content text
			------------------------------------------------------------------------------------------------------------------------------------
			SET		@IdIndex = (
									SELECT	MIN(cs.Idx)
									FROM	@ControlCharactertisticPlaceHolders cs
								) 

			WHILE	@IdIndex IS NOT NULL
			BEGIN
				--------------------------------------------------------------------------------------------------------------------------------
				--  Find and replace the content of the WorkDocument using their limits
				--------------------------------------------------------------------------------------------------------------------------------
				IF EXISTS
				(
					SELECT	1
					FROM	@ControlCharactertisticPlaceHolders ccs
					WHERE	ccs.ControlCharacteristicLinkUID IS NOT NULL
					AND		ccs.LimitValue IS NOT NULL
					AND		ccs.Idx = @IdIndex
				)
				BEGIN
					UPDATE	wdi
					SET		wdi.StepLongDescription = REPLACE(wdi.StepLongDescription, ccs.PlaceHolder,ccs.LimitValue )
					FROM	@WorkDefinitionInfo wdi
					JOIN	@ControlCharactertisticPlaceHolders ccs	ON	ccs.WorkDefinitionMasterUID = wdi.WorkDefinitionMasterUID
					WHERE	ccs.Idx = @IdIndex
				END

				SET	 @IdIndex = (
									SELECT	MIN(Idx)
									FROM	@ControlCharactertisticPlaceHolders
									WHERE	Idx > @IdIndex
								)	
			END
		
			------------------------------------------------------------------------------------------------------------------------------------
			--  Remove information use for the limit retrieval for that dynamic content section
			------------------------------------------------------------------------------------------------------------------------------------
			IF	EXISTS
				( 
					SELECT 1 
					FROM @ControlCharacteristicRequests
				)
			BEGIN
				DELETE FROM @ControlCharacteristicRequests
			END
		
			IF	EXISTS
				( 
					SELECT 1 
					FROM @ControlCharacteristics
				)
			BEGIN
				DELETE FROM @ControlCharacteristics
			END
		END
	END

	-- Parallel Execution
	ELSE IF (
		SELECT	COUNT(1)
		FROM	@WorkDefinitionInfo wdi
	)	> 1
	BEGIN 
        IF @ExecutionLimitsReferenceTime = @RETRIEVAL_TYPE_CURRENT_TIME
        BEGIN
            SET	@LimitTimestamp = (   
									SELECT	TOP 1 COALESCE(wdi.StepCompletedOn, @CURRENT_TIME)
									FROM	@WorkDefinitionInfo wdi
									ORDER
									BY		wdi.WorkDefinitionUID ASC
								)
        END
        ELSE IF @ExecutionLimitsReferenceTime = @RETRIEVAL_TYPE_APPROVAL_TIME
        BEGIN
            SET	@LimitTimestamp =   (   
                                        SELECT	TOP 1 COALESCE(wdi.VersionApprovedOn, @CURRENT_TIME)
								        FROM	@WorkDefinitionInfo wdi
										ORDER
										BY		wdi.WorkDefinitionUID ASC
                                    )
        END
        ELSE
        BEGIN
            SET	@LimitTimestamp =   (   
                                        SELECT	TOP 1 COALESCE(wdi.VersionApprovedOn, @CURRENT_TIME)
								        FROM	@WorkDefinitionInfo wdi
										ORDER
										BY		wdi.WorkDefinitionUID ASC
                                    )
        END
	END
    --==========================================================================================================================================
    --  Prepare GenericStepDataJSON
    --==========================================================================================================================================
	SET @ErrorSection = N'Prepare GenericStepDataJSON'

	INSERT INTO	@Certificates
	(
		CertificateUID,
		CertificateName
	)
	SELECT	c.uid,
			c.name
	FROM	dbo.certificate c
	JOIN	dbo.EXT_CertificateStepLink csl	ON	csl.certificateuid	=	c.uid
	JOIN	dbo.workdefinitionmaster wdm	ON	wdm.uid	=	csl.workdefinitionmasteruid
	JOIN	@WorkDefinitionInfo wdi			ON	wdi.WorkDefinitionMasterUID =	wdm.uid
	
	SET	@CertificateJSON =	(
								SELECT	CertificateUID,
										CertificateName
								FROM	@Certificates	FOR JSON PATH
							)


	INSERT INTO @GenericStepDataInfo
    (
        StepName,
        StepDescription,
        StepLongDescription,
		StepStatusDefinitionName,
        StepTargetCycleTime,
		StepTargetCycleTimeSeconds,
        StepExecutionTime,
        AllStepExecutionTime,
        IsAutoWorkDefinitionForward,
        IsBypassable,
		IsCurrent,
        WorkDefinitionMasterTypeName,
		CertificateJSON,
		InstructionalText,
		LogSheetWorkDocumentRootName,
		VerificationStepCompleted,
        MeasureSingleValue,
		WorkDefinitionMasterConditionTypeName
    )
    SELECT  TOP 1
			wdi.StepName,
            wdi.StepDescription,
			wdi.StepLongDescription,
			wdi.StepStatusDefinitionName,
			@CurrentStepTargetCycleTime,
			@CurrentStepTargetCycleTimeSeconds,
			Duration,
			@AllStepExecutionTime,
			wdi.IsAutoWorkDefinitionForward,
			wdi.IsBypassable,
			wdi.IsCurrent,
			wdi.WorkDefinitionMasterTypeName,
			@CertificateJSON,
			wdi.InstructionalText,
			wdi.LogSheetWorkDocumentRootName,
			wdi.VerificationStepCompleted,
            @MeasureSingleValue,
			wdi.WorkDefinitionMasterConditionTypeName
    FROM    @WorkDefinitionInfo wdi
	ORDER
	BY		wdi.ParallelExecutionSequenceNumber ASC

	IF EXISTS (
		SELECT	1
		FROM 	@WorkDefinitionInfo
		WHERE 	IsConditionalSkipped = 1
	)
	BEGIN
		UPDATE 	@GenericStepDataInfo
		SET 	IsConditionalSkipped = 1
	END

	IF EXISTS (
		SELECT	1
		FROM 	@WorkDefinitionInfo
		WHERE 	SecondarySignoffRequired = 1
	)
	BEGIN
		UPDATE 	@GenericStepDataInfo
		SET 	SecondarySignoffRequired = 1,
				SecondarySignoffUserGroup = (
					SELECT 	TOP 1 
							SecondarySignoffUserGroup
					FROM 	@WorkDefinitionInfo
					WHERE	SecondarySignoffRequired = 1
				)
	END

	SET @GenericStepDataJSON =	(
									SELECT 	StepName,
											StepDescription,
											StepLongDescription,
											StepStatusDefinitionName,
											StepTargetCycleTime,
											StepTargetCycleTimeSeconds,
											StepExecutionTime,
											AllStepExecutionTime,
											IsAutoWorkDefinitionForward,
											IsBypassable,
											IsCurrent,
											WorkDefinitionMasterTypeName,
											MeasureSingleValue,
											JSON_QUERY(CertificateJSON) AS 'CertificateJSON',
											IsConditionalSkipped,
											WorkDefinitionMasterConditionTypeName,
											SecondarySignoffRequired,
											SecondarySignoffUserGroup
									FROM 	@GenericStepDataInfo
									FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
								)
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Remove curly braces from empty array 
	--------------------------------------------------------------------------------------------------------------------------------------------
	SET @GenericStepDataJSON = REPLACE(@GenericStepDataJSON, '[{}]', '[]')
	--==========================================================================================================================================
	--	Get verification steo information
	--==========================================================================================================================================
	SET @ErrorSection = N'Get Verification Step Information'

	IF EXISTS
	(
		SELECT	1
		FROM	@WorkDefinitionInfo
		WHERE	WorkDefinitionMasterTypeName = @STEP_TYPE_VERIFICATION
	)
	BEGIN
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Set step completed based on if the step has been completed
		----------------------------------------------------------------------------------------------------------------------------------------
		UPDATE	@WorkDefinitionInfo
		SET		StepIsCompleted					= VerificationStepCompleted
		WHERE	WorkDefinitionMasterTypeName	= @STEP_TYPE_VERIFICATION		
	END
    --==========================================================================================================================================
    --  Get Manual Entry Step Information
    --==========================================================================================================================================
	SET @ErrorSection = N'Get Manual Entry Step Information'
	
	IF EXISTS
	(
		SELECT	1
		FROM	@WorkDefinitionInfo
		WHERE	WorkDefinitionMasterTypeName = @STEP_TYPE_MANUALENTRY
	)
	BEGIN
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Get attributes
		----------------------------------------------------------------------------------------------------------------------------------------
		INSERT INTO @Attributes
		(
			AttributeUID,
			AttributeDisplayName,
			AttributeParallelExecutionGroupingUID,
			WDUID,
			SourceName,
			ParallelExecutionSequenceNumber,
			UnitOfMeasureName,
			UnitOfMeasureDisplayName,
			DataFormatName,
			RejectOutdated
		)
		SELECT	DISTINCT  
				a.[uid],		
				a.displayname,
				a.attributeparallelexecutiongroupinguid,
				wdi.WorkDefinitionUID,
				@ATTRIBUTE_SOURCE_MANUAL_ENTRY,
				a.parallelexecutionsequencenumber,
				uom.[name],
				uom.displayname,
				df.[name],
				aap.ext_rejectoutdated
		FROM	@WorkDefinitionInfo wdi
		JOIN	dbo.workdefinitionattributelink wdal		ON	wdal.workdefinitionuid					= wdi.WorkDefinitionUID
		JOIN	dbo.attribute a								ON	a.uid = wdal.attributeuid	
		JOIN	dbo.dataformat df							ON	df.[uid]							= a.dataformatuid
		JOIN	dbo.attributesource aa						ON	aa.[uid]							= a.attributesourceuid
		LEFT
		JOIN	dbo.unitofmeasure uom						ON	uom.[uid]							= a.unitofmeasureuid
		LEFT
		JOIN	dbo.attribute_ap aap						ON aap.[uid] = a.[uid]
		WHERE	aa.[name] = @ATTRIBUTE_SOURCE_MANUAL_ENTRY
		/*
		UNION
		ALL		-- Grab all attribute set on parallel execution stations
		SELECT	DISTINCT 
				a.[uid],		
				a.displayname,
				a.attributeparallelexecutiongroupinguid,
				wdi.WorkDefinitionUID,
				@ATTRIBUTE_SOURCE_MANUAL_ENTRY,
				a.parallelexecutionsequencenumber,
				uom.[name],
				uom.displayname,
				df.[name],
				aap.ext_rejectoutdated
		FROM	@WorkDefinitionInfo wdi
		JOIN	dbo.workdefinitionmaster wdm				ON	wdm.[uid]								= wdi.WorkDefinitionMasterUID
		JOIN	dbo.workdefinitionmasterattributelink wdmal	ON	wdmal.workdefinitionmasteruid			= wdm.[uid]
		JOIN	dbo.workdefinitionmasterrootversion wdmrv	ON	wdmrv.[uid]								= wdm.workdefinitionmasterrootversionuid
		JOIN	dbo.attribute a								ON	a.workdefinitionmasterrootuid			= wdmrv.workdefinitionmasterrootuid
															AND	a.attributeparallelexecutiongroupinguid	= wdmal.attributeparallelexecutiongroupinguid
															AND	a.parallelexecutionsequencenumber		= wdi.ParallelExecutionSequenceNumber
		JOIN	dbo.workdefinitionattributelink wdal		ON	wdal.workdefinitionuid					= wdi.WorkDefinitionUID
															AND	wdal.attributeuid						= a.[uid]
		JOIN	dbo.dataformat df							ON	df.[uid]								= a.dataformatuid
		JOIN	dbo.attributesource aa						ON	aa.[uid]								= a.attributesourceuid
		LEFT
		JOIN	dbo.unitofmeasure uom						ON	uom.[uid]								= a.unitofmeasureuid
		LEFT
		JOIN	dbo.attributevalue av						ON	av.attributeuid							= a.[uid]
															AND	av.productioneventuid					= wdi.ProductionEventUID
		LEFT
		JOIN	dbo.attributevaluelink avl					ON	avl.attributevalueuid				= av.[uid]
        													AND	avl.workdefinitionattributelinkuid	= wdal.[uid]
		LEFT
		JOIN	dbo.attribute_ap aap						ON aap.[uid] = a.[uid]
		WHERE	aa.[name] = @ATTRIBUTE_SOURCE_MANUAL_ENTRY	
		*/
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Get attribute list (For List data format)
		----------------------------------------------------------------------------------------------------------------------------------------
		IF EXISTS
		(
			SELECT	1
			FROM	@Attributes
			WHERE	DataFormatName = @LIST_DATA_TYPE
		)
		BEGIN
			INSERT INTO @AttributeSelectionList
			(
				AttributeUID,
				SelectionListUID,
				SelectionListDisplayName
			)
			SELECT	DISTINCT 
        			a.AttributeUID,
					sl.[uid],
					sl.displayname
			FROM	@Attributes a
			JOIN	dbo.attribute_ap aap	ON	aap.[UID] = a.AttributeUID
			JOIN	dbo.selectionlist sl	ON	sl.[UID] = aap.ext_selectionlistuid
			WHERE	a.DataFormatName = @LIST_DATA_TYPE
			GROUP
			BY		a.AttributeUID,
					sl.[uid],
					sl.displayname
			----------------------------------------------------------------------------------------------------------------------------------------
			--	Get attribute list content (For List data format)
			----------------------------------------------------------------------------------------------------------------------------------------
			INSERT INTO @AttributeSelectionListContent
			(
				AttributeUID,
				SelectionListUID,
				SelectionListItemUID,
				SelectionListItemDisplayName
			)
			SELECT	DISTINCT
					sl.AttributeUID,
        			sli.selectionlistuid,
					sli.[uid],
					sli.displayname
			FROM	@AttributeSelectionList sl
			JOIN	dbo.selectionlistitem sli	ON	sli.selectionlistuid = sl.SelectionListUID		
			GROUP
			BY		sl.AttributeUID,
					sli.selectionlistuid,
					sli.[uid],
					sli.displayname
		END

		-- Once get all attribute associates to the steps, find their values and timestamps informations.
		UPDATE	a
		SET		a.AttributeValue	= av.[value],
				a.TimestampResult	= av.timestampresult,
				a.IsCompleted		= IIF(COALESCE(av.[value], @EMPTY_STRING) = @EMPTY_STRING,0,1)
		FROM	@Attributes a
		JOIN	@WorkDefinitionInfo wdi						ON wdi.WorkDefinitionUID				= a.WDUID
		JOIN	dbo.attributevalue av						ON	av.attributeuid						= a.AttributeUID
		JOIN	dbo.attributevaluelink avl					ON	avl.attributevalueuid				= av.[uid]
		JOIN	dbo.workdefinitionattributelink wdal		ON	wdal.[uid]							= avl.workdefinitionattributelinkuid
															AND	wdal.workdefinitionuid				= a.WDUID
		WHERE	a.SourceName			= @ATTRIBUTE_SOURCE_MANUAL_ENTRY
		AND		(
					a.IsParallelExecution	= 0
					OR
					av.productioneventuid	= wdi.ProductionEventUID
				)
	END
    --==========================================================================================================================================
    --  Get List Step Information
    --==========================================================================================================================================
	SET @ErrorSection = N'Get List Step Information'

	IF EXISTS
	(
		SELECT	1
		FROM	@WorkDefinitionInfo
		WHERE	WorkDefinitionMasterTypeName = @STEP_TYPE_LIST			
	)
	BEGIN
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Get list associated with work definition
		----------------------------------------------------------------------------------------------------------------------------------------
		INSERT INTO @SelectionList
		(
			SelectionListUID,
			SelectionListDisplayName
		)
		SELECT	DISTINCT 
        		sl.[uid],
				sl.displayname
		FROM	@WorkDefinitionInfo wdi
		JOIN	dbo.workdefinitionselectionlistlink wdsll	ON	wdsll.workdefinitionuid = wdi.WorkDefinitionUID
		JOIN	dbo.selectionlist sl						ON	sl.[uid]				= wdsll.selectionlistuid
        GROUP
        BY		sl.[uid],
				sl.displayname
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Get list content
		----------------------------------------------------------------------------------------------------------------------------------------
		INSERT INTO @SelectionListContent
		(
			SelectionListUID,
			SelectionListItemUID,
			SelectionListItemDisplayName
		)
		SELECT	DISTINCT 
        		sli.selectionlistuid,
				sli.[uid],
				sli.displayname
		FROM	@SelectionList sl
		JOIN	dbo.selectionlistitem sli	ON	sli.selectionlistuid = sl.SelectionListUID		
        GROUP
        BY		sli.selectionlistuid,
				sli.[uid],
				sli.displayname
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Set step completed based on if a value has been selected
		----------------------------------------------------------------------------------------------------------------------------------------
		UPDATE	@WorkDefinitionInfo
		SET		StepIsCompleted = 1
		WHERE	SelectionListItemUID			IS NOT NULL
		AND		WorkDefinitionMasterTypeName	= @STEP_TYPE_LIST			
	END
    --==========================================================================================================================================
    --  Get Calculation Step Information
    --==========================================================================================================================================
	SET @ErrorSection = N'Get Calculation Step Information'
	
	IF EXISTS
	(
		SELECT	1
		FROM	@WorkDefinitionInfo
		WHERE	WorkDefinitionMasterTypeName = @STEP_TYPE_CALCULATION			
	)
	BEGIN
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Get calculation configuration and result
		----------------------------------------------------------------------------------------------------------------------------------------
		INSERT INTO @Attributes
		(
			AttributeUID,
			AttributeName,
			AttributeDisplayName,
			AttributeParallelExecutionGroupingUID,
			WDUID,
			WDRUID,
			SequenceNumber,
			UnitOfMeasureName,
			UnitOfMeasureDisplayName,
			DataFormatName,
			CalculationName,
			RetrieveControlCharacteristics,
			Formula
		)
		SELECT	a.[uid],
				a.[name],
				a.displayname,
				a.attributeparallelexecutiongroupinguid,
				wdi.WorkDefinitionUID,
				wdi.WorkDefinitionRootUID,
				wdal.sequencenumber,
				uom.[name],
				uom.displayname,
				df.[name],
				acm.[name],
				1,
				acmv.Formula
		FROM	@WorkDefinitionInfo wdi
		JOIN	dbo.workdefinitioncalculationlink wdcl			ON	wdcl.workdefinitionuid				= wdi.WorkDefinitionUID
		JOIN	dbo.attributecalculationmasterversion acmv		ON	acmv.[uid]							= wdcl.attributecalculationmasterversionuid
		JOIN	dbo.attributecalculationmaster acm				ON	acm.[uid]							= acmv.attributecalculationmasteruid
		JOIN	dbo.attribute a									ON	a.[uid]								= wdcl.attributeuid
		JOIN	dbo.workdefinitionattributelink wdal			ON	wdal.attributeuid					= a.[uid]
																AND	wdal.workdefinitionuid				= wdi.WorkDefinitionUID
		JOIN	dbo.dataformat df								ON	df.[uid]							= a.dataformatuid
		LEFT
		JOIN	dbo.unitofmeasure uom							ON	uom.[uid]							= a.unitofmeasureuid

		-- Once get all attribute associates to the steps, find their values and timestamps informations.
		UPDATE	a
		SET		a.AttributeValue	= av.[value],
				a.TimestampResult	= av.timestampresult
		FROM	@Attributes a
		JOIN	dbo.workdefinitionattributelink wdal		ON 	wdal.workdefinitionuid				= a.WDUID
		JOIN	dbo.attributevaluelink avl					ON	avl.workdefinitionattributelinkuid	= wdal.[uid]
		JOIN	dbo.attributevalue av						ON	av.[uid]							= avl.attributevalueuid
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Get calculation inputs and their value
		----------------------------------------------------------------------------------------------------------------------------------------
		INSERT INTO @CalculationInputs
		(
			AttributeUID,
			AttributeValueLinkUID,
			AttributeCalculationMasterInputName,
			AttributeCalculationMasterInputDisplayName,
			WDUID,
			WorkDefinitionName,
			WorkDefinitionDescription,
			WorkDefinitionMasterConditionTypeName,
			IsConditionalSkipped,
			WorkDefinitionMasterUID,
			ProductUID,
			JobOrderUID,
			EquipmentUID,
			WorkDefinitionRootDisplay,
			IsCurrentWorkDefinitionRoot,
			WorkDefinitionValueToWrite,
			WorkDefinitionWriteTimestamp
		)
		SELECT	a.[uid],
				avl.attributevalueuid,
				acmi.[name],
				acmi.displayname,
				wd.[uid],
				wd.[name],
				wd.[description],
				wdmct.[name],
				wd.isconditionalskipped,
				wd.workdefinitionmasteruid,
				wdr.productuid,
				wdr.joborderuid,
				wdr.equipmentuid,
				wdr.displayname,
				CASE
					WHEN wdr.[uid] = wdi.WorkDefinitionRootUID
						THEN 1
					ELSE
						0
				END,
				wdal.ValueToWrite,
				wdal.WriteValueTimestamp
		FROM	@WorkDefinitionInfo wdi
		JOIN	dbo.workdefinitioncalculationlink wdcl			ON	wdcl.workdefinitionuid					= wdi.WorkDefinitionUID
		JOIN	dbo.workdefinitioncalculationInputlink wdcil	ON	wdcil.workdefinitioncalculationlinkuid	= wdcl.[uid]
		JOIN	dbo.workdefinition wd							ON	wd.[uid]								= wdcil.workdefinitionuid
		JOIN	dbo.workdefinitionmaster wdm					ON	wdm.[uid]								= wd.workdefinitionmasteruid
		JOIN	dbo.workdefinitionroot wdr						ON	wdr.[uid]								= wd.workdefinitionrootuid
																AND	wdr.serialnumberuid						= wdi.SerialNumberUID
		JOIN	dbo.attributecalculationmasterinput acmi		ON	acmi.[uid]								= wdcil.attributecalculationmasterinputuid
		JOIN	dbo.attribute a									ON	a.[uid]									= wdcil.attributeuid
		JOIN	dbo.workdefinitionattributelink wdal			ON	wdal.workdefinitionuid					= wdcil.workdefinitionuid
																AND	wdal.attributeuid						= a.[uid] -- using this but shouldnt we have a sequence number in this wdal table instead?
		LEFT
		JOIN	dbo.attributevaluelink avl						ON avl.workdefinitionattributelinkuid		= wdal.[uid]
		LEFT
		JOIN	dbo.unitofmeasure uom							ON	uom.[uid]								= a.unitofmeasureuid
		LEFT
		JOIN	dbo.workdefinitionmasterconditiontype wdmct		ON	wdmct.[uid]								= wdm.workdefinitionmasterconditiontypeuid

		-- If the found calculation inputs includes conditional execution steps that were skipped, 
		-- then we need to find the last conditional execution steps that were executed.
		IF EXISTS (
			SELECT	1 
			FROM	@CalculationInputs
			WHERE	IsConditionalSkipped = 1
			AND		WorkDefinitionMasterConditionTypeName IS NOT NULL
		)
		BEGIN
			;WITH ConditionalSteps AS (
				SELECT	ci.WDUID AS oldworkdefinitionuid,
						wd.[uid] AS newworkdefinitionuid,
						ROW_NUMBER() OVER	(
												PARTITION
												BY			ci.WDUID
												ORDER
												BY			wd.completedon DESC
											) AS rownumber
				FROM	dbo.workdefinition wd
				JOIN	dbo.workdefinitionroot wdr	ON	wdr.[uid] = wd.workdefinitionrootuid
				JOIN	@CalculationInputs ci		ON	ci.WorkDefinitionMasterUID = wd.workdefinitionmasteruid
													AND	(
															(
																ci.ProductUID = wdr.productuid 
																AND 
																ci.WorkDefinitionMasterConditionTypeName = 'ProductChange'
															)
															OR 
															(
																ci.JobOrderUID = wdr.joborderuid 
																AND 
																ci.WorkDefinitionMasterConditionTypeName = 'WorkOrderChange'
															)
														)
													AND ci.EquipmentUID = wdr.equipmentuid
				WHERE	wd.IsConditionalSkipped = 0
				AND		ci.IsConditionalSkipped = 1
				AND		ci.WorkDefinitionMasterConditionTypeName IS NOT NULL
			)

			UPDATE	ci
			SET		WDUID = cs.newworkdefinitionuid,
					AttributeValueLinkUID = avl.attributevalueuid
			FROM	@CalculationInputs ci
			JOIN	ConditionalSteps cs		ON cs.oldworkdefinitionuid = ci.WDUID
			JOIN	dbo.workdefinitionattributelink wdal	ON	wdal.attributeuid = ci.AttributeUID
															AND	wdal.workdefinitionuid = cs.newworkdefinitionuid
			JOIN	dbo.attributevaluelink avl				ON avl.workdefinitionattributelinkuid = wdal.[uid]
			WHERE	cs.rownumber = 1
		END

		-- Once get all attribute associates to the steps, find their values and timestamps informations.
		UPDATE	ci
		SET		ci.AttributeValue	= COALESCE(av.[value], ci.WorkDefinitionValueToWrite),
				ci.WorkDefinitionWriteTimestamp = COALESCE(ci.WorkDefinitionWriteTimestamp, av.TimestampResult)
		FROM	@CalculationInputs ci
		LEFT
		JOIN	dbo.attributevalue av	ON	av.[uid]	= ci.AttributeValueLinkUID

		--	Double check that there is only one value per input
		;WITH InputCounts
		(
			InputCount,
			AttributeCalculationMasterInputName,
			WDUID,
			MaxWriteTimestamp
		)
		AS
		(
			SELECT	COUNT(1) AS [InputCount],
					AttributeCalculationMasterInputName,
					WDUID,
					MAX(WorkDefinitionWriteTimestamp)
			FROM	@CalculationInputs
			GROUP
			BY		AttributeCalculationMasterInputName, WDUID
		)
		
		UPDATE	ci
		SET		ci.InputCounts = cte.InputCount,
				ci.MaxWriteTimestamp = cte.MaxWriteTimestamp
		FROM	@CalculationInputs	ci
		JOIN	InputCounts			cte	ON ci.WDUID = cte.WDUID
										AND ci.AttributeCalculationMasterInputName = cte.AttributeCalculationMasterInputName
		
		--	If more than one value per input, delete based on timestamp
		IF EXISTS
		(
			SELECT	1
			FROM	@CalculationInputs
			WHERE	InputCounts > 1
		)
		BEGIN
			DELETE FROM @CalculationInputs
			WHERE	InputCounts > 1
			AND		WorkDefinitionWriteTimestamp < MaxWriteTimestamp
			AND		WorkDefinitionWriteTimestamp IS NOT NULL
			AND		MaxWriteTimestamp IS NOT NULL
		END
	END
    --==========================================================================================================================================
    --  Get Part Validation Step Information
    --==========================================================================================================================================
	SET @ErrorSection = N'Get Part Validation Step Information'

	IF EXISTS
	(
		SELECT	1
		FROM	@WorkDefinitionInfo
		WHERE	WorkDefinitionMasterTypeName = @STEP_TYPE_PARTVALIDATION
	)
	BEGIN
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Get the BOM items to validate in that step
		----------------------------------------------------------------------------------------------------------------------------------------
		INSERT INTO @Parts
		(
			SequenceNumber,
			BillOfMaterialItemUID,
			BillOfMaterialItemDisplayName,
			ProductUID,
			ProductName,
			ProductDisplayName,
			RequiredQuantity,
			ActualProductName,
			ActualQuantity,
			IsCompleted						
		)
		SELECT	wdmil.sequencenumber,
				bomi.[uid],
				bomi.displayname,
				p.[uid],
				p.[name],
				p.displayname,
				wdmil.requiredquantity,
				CASE 
					WHEN wdmil.actualquantity > 0
						THEN COALESCE(psub.[name], p.[name])
					ELSE
						NULL	
				END,
				wdmil.actualquantity,
				--	Only evaluate complete here if part validation extended is false
				--	Otherwise handle in section below
				CASE
					WHEN wdmil.actualquantity > 0 AND @IsPartValidationEXTEnabled = 0
						THEN 1
					ELSE
						0
				END
		FROM	@WorkDefinitionInfo wdi
		JOIN	dbo.workdefinitionmaterialitemlink wdmil	ON	wdmil.workdefinitionuid = wdi.WorkDefinitionUID
		JOIN	dbo.billofmaterialitem bomi					ON	bomi.[uid]				= wdmil.billofmaterialitemuid
		JOIN	dbo.product p								ON	p.[uid]					= bomi.productuid
		LEFT
		JOIN	dbo.product psub							ON	psub.[uid]				= wdmil.substitutionproductuid
		
		--	Fill out part specific values for part validation extended
		IF @IsPartValidationEXTEnabled = 1
		BEGIN
			--	Grab config values
			UPDATE	p
			SET		p.IsQuantity = wdmil_ap.EXT_IsQuantity,
					p.IsSerialized = wdmil_ap.EXT_IsSerialized,
					p.UniqueSerialNumberSource = bomi_ap.EXT_UniqueSerialNumberSource
			FROM	@WorkDefinitionInfo	wdi
			JOIN	dbo.workdefinitionmaterialitemlink	wdmil	ON wdmil.workdefinitionuid = wdi.workdefinitionuid
			JOIN	dbo.billofmaterialitem	bomi	ON	bomi.[uid] = wdmil.billofmaterialitemuid
			JOIN	@Parts	p	ON	bomi.[uid] = p.BillOfMaterialItemUID
			LEFT
			JOIN	dbo.workdefinitionmaterialitemlink_ap wdmil_ap ON wdmil.[uid] = wdmil_ap.[uid]
			LEFT
			JOIN	dbo.billofmaterialitem_ap	bomi_ap ON bomi.[uid] = bomi_ap.[uid]

			--	Grab consumption for any steps where IsSerialized = 1
			--	Evaluate completion for IsSerialized components
			IF EXISTS
			(
				SELECT	1
				FROM	@Parts
				WHERE	IsSerialized = 1
			)
			BEGIN
				INSERT INTO @PartConsumptions
				(
					WorkDefinitionSerialNumberConsumptionUID,
					WorkDefinitionUID,
					BillOfMaterialItemUID,
					BillOfMaterialSubstitutionUID,
					ProductUID,
					ProductName,
					ProductDisplayName,
					ConsumedQuantity,
					FactorOfConversion,
					SerialNumberUID,
					SerialNumberValue
				)
				SELECT	wdsnp.[uid],
						wdi.WorkDefinitionUID,
						bomi.[uid],
						wdsnp.BillOfMaterialSubstitutionUID,
						pr.[uid],
						pr.[name],
						pr.DisplayName,
						wdsnp.Quantity,
						boms.FactorOfConversion,
						wdsnp.SerialNumberUID,
						sn.[Value]
				FROM	@WorkDefinitionInfo	wdi
				JOIN	dbo.workdefinitionmaterialitemlink	wdmil	ON wdmil.workdefinitionuid = wdi.workdefinitionuid
				JOIN	dbo.billofmaterialitem	bomi	ON	bomi.[uid] = wdmil.billofmaterialitemuid
				JOIN	@Parts	p	ON	bomi.[uid] = p.BillOfMaterialItemUID
				JOIN	dbo.ext_workdefinitionserialnumberconsumption	wdsnp	ON	wdmil.[uid] = wdsnp.WorkDefinitionMaterialItemLinkUID
				JOIN	dbo.serialnumber	sn ON wdsnp.serialnumberuid = sn.[uid]
				JOIN	dbo.[product]	pr	ON	wdsnp.ProductUID = pr.[uid]
				LEFT
				JOIN	dbo.billofmaterialsubstitution boms ON wdsnp.billofmaterialsubstitutionuid = boms.[uid]
				WHERE	p.IsSerialized = 1
			
				;WITH CTE_AggregateConsumption	(BillOfMaterialItemUID, SumConsumedQuantity)
				AS
				(
					SELECT	BillOfMaterialItemUID,
							SUM(ConsumedQuantity * COALESCE(FactorOfConversion, 1))
					FROM	@PartConsumptions
					GROUP
					BY		BillOfMaterialItemUID
				)

				UPDATE	p
				SET		p.ConsumedQuantity = cte.SumConsumedQuantity
				FROM	@Parts	p
				JOIN	CTE_AggregateConsumption	cte ON p.BillOfMaterialItemUID = cte.BillOfMaterialItemUID
				WHERE	p.IsSerialized = 1
			END

			--	Handle completion if IsSerialized is false
			--	Actual quantity just needs to be greater than 0
			UPDATE	p
			SET		p.IsCompleted = IIF(p.actualquantity > 0, 1, 0)
			FROM	@Parts	p
			WHERE	p.IsSerialized = 0

			--	Handle completion if IsSerialized is true and IsQuantity is false
			--	quantity aggregated from consumption records must = actual quantity
			UPDATE	p
			SET		p.IsCompleted = IIF(p.ConsumedQuantity = p.actualquantity, 1, 0)
			FROM	@Parts	p
			WHERE	p.IsSerialized = 1
			AND		p.IsQuantity = 0

			--	Handle completion if IsSerialized is true and IsQuantity is true
			--	quantity aggregated from consumption records must = actual quantity and actual quantity must be greater than 0
			UPDATE	p
			SET		p.IsCompleted = IIF(p.actualquantity > 0 AND p.ConsumedQuantity = p.actualquantity, 1, 0)
			FROM	@Parts	p
			WHERE	p.IsSerialized = 1
			AND		p.IsQuantity = 1
		END
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Get possible substitutions for each BOM item
		----------------------------------------------------------------------------------------------------------------------------------------
		INSERT INTO @PartSubstitutions
		(
			BillOfMaterialItemUID,
			ProductUID,
			ProductName,
			ProductDisplayName,
			RequiredQuantity
		)
		SELECT	p.BillOfMaterialItemUID,
				pr.[uid],
				pr.[name],
				pr.displayname,
				p.RequiredQuantity * boms.factorofconversion
		FROM	@Parts p
		JOIN	dbo.billofmaterialsubstitution boms	ON	boms.billofmaterialitemuid	= p.BillOfMaterialItemUID
		JOIN	dbo.product pr						ON	pr.[uid]					= boms.productuid	
	END
    --==========================================================================================================================================
    --  Get Smart Tool Step Information
    --==========================================================================================================================================
	SET @ErrorSection = N'Get Smart Tool Step Information'
	
	IF EXISTS
	(
		SELECT	1
		FROM	@WorkDefinitionInfo
		WHERE	WorkDefinitionMasterTypeName = @STEP_TYPE_SMART_TOOL			
	)
	BEGIN
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Get smart tool details
		----------------------------------------------------------------------------------------------------------------------------------------
		INSERT INTO @SmartToolStep
		(
			AllowValueOverride,
			AllowSubstitution,
			SmartToolUID,
			SmartToolTypeUIDAllowed,
			SmartToolName,
			SmartToolDisplayName
		)
		SELECT	DISTINCT 
				wdst.allowoverride,
				wdst.allowsubstitution,
				wdst.smarttooluid,
				st.smarttooltypeuid,
				st.[name],
				st.displayname
		FROM	@WorkDefinitionInfo wdi
		JOIN	dbo.workdefinitionsmarttool wdst		ON	wdst.workdefinitionuid			= wdi.WorkDefinitionUID
		JOIN	dbo.smarttool st						ON	st.[uid]						= wdst.smarttooluid
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Get serial number related information about the step
		----------------------------------------------------------------------------------------------------------------------------------------
		INSERT INTO @Attributes
		(
			SmartToolUID,
			AttributeUID,
			AttributeName,
			AttributeDisplayName,
			AttributeParallelExecutionGroupingUID,
			WDUID,
			ParallelExecutionSequenceNumber,
			SequenceNumber,
			UnitOfMeasureName,
			UnitOfMeasureDisplayName,
			DataFormatName,
			IsSmartToolWrite,
			RetrieveControlCharacteristics
		)
		SELECT	a.SmartToolUID,
				a.[uid],
				a.[name],
				a.displayname,
				a.attributeparallelexecutiongroupinguid,
				wdal.workdefinitionuid,
				a.parallelexecutionsequencenumber,
				wdal.sequencenumber,
				uom.[name],
				uom.displayname,
				df.[name],
				CASE 
					WHEN wdal.valuetowrite IS NOT NULL
						THEN 1
					ELSE 
						0
				END,
				CASE 
					WHEN wdal.valuetowrite IS NOT NULL
						THEN 0
					ELSE 
						1
				END
		FROM	@WorkDefinitionInfo wdi
		JOIN	dbo.workdefinitionattributelink wdal			ON	wdal.workdefinitionuid	= wdi.WorkDefinitionUID
		JOIN	dbo.attribute a									ON	a.[uid]					= wdal.attributeuid
		JOIN	dbo.dataformat df								ON	df.[uid]				= a.dataformatuid
		LEFT
		JOIN	dbo.unitofmeasure uom							ON	uom.[uid]				= a.unitofmeasureuid
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Get value to write for writes to smart tools 
		--	(we read it for both read and write because we need OverriddenBy for reads, while we're in that table...)
		----------------------------------------------------------------------------------------------------------------------------------------
		UPDATE	a
		SET		a.ValueToWrite	=	CASE a.IsSmartToolWrite
										WHEN 1
											THEN wdal.valuetowrite
										ELSE
											NULL
									END,
				a.OverriddenBy	=	wdal.overriddenby
		FROM	@Attributes a
		CROSS
		JOIN	@WorkDefinitionInfo wdi
		JOIN	dbo.workdefinitionattributelink wdal			ON	wdal.workdefinitionuid					= wdi.WorkDefinitionUID
																AND	wdal.attributeuid						= a.AttributeUID
																AND	wdal.sequencenumber						= a.SequenceNumber
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Get current attribute value for reads and writes from smart tools
		----------------------------------------------------------------------------------------------------------------------------------------
		UPDATE	a
		SET		a.AttributeValue	=	av.[value],
				a.TimestampResult	=	av.timestampresult
		FROM	@Attributes a
		JOIN	dbo.workdefinitionattributelink wdal		ON	wdal.workdefinitionuid				= a.WDUID
															AND	wdal.attributeuid					= a.AttributeUID
															AND wdal.sequencenumber					= a.SequenceNumber
		JOIN	dbo.attributevaluelink avl					ON	avl.workdefinitionattributelinkuid	= wdal.[uid]
		JOIN	dbo.attributevalue av						ON	av.[uid]							= avl.attributevalueuid
		WHERE	a.IsSmartToolWrite	= 0
		
		UPDATE	a
		SET		a.TimestampResult	=	wdal.writevaluetimestamp
		FROM	@Attributes a
		JOIN	dbo.workdefinitionattributelink wdal		ON	wdal.workdefinitionuid				= a.WDUID
															AND	wdal.attributeuid					= a.AttributeUID
															AND wdal.sequencenumber					= a.SequenceNumber
		WHERE	a.IsSmartToolWrite	= 1
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Verify which read values were overridden
		----------------------------------------------------------------------------------------------------------------------------------------
		UPDATE	@Attributes
		SET		IsOverridden		= 1
		WHERE	IsSmartToolWrite	= 0
		AND		OverriddenBy		IS NOT NULL	
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Identify which entries are completed
		----------------------------------------------------------------------------------------------------------------------------------------
		UPDATE	@Attributes
		SET		IsCompleted			= 1
		WHERE	TimestampResult		IS NOT NULL
        AND     (
                    (
                        COALESCE(AttributeValue, @EMPTY_STRING) <>  @EMPTY_STRING
                        AND
                        COALESCE(IsSmartToolWrite, 0)           <>  1
                    )
                    OR
                    COALESCE(IsSmartToolWrite, 0)    =   1
                )
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Retrieve smart tool alias for each attributes
		----------------------------------------------------------------------------------------------------------------------------------------	
		;WITH	SmartToolAlias
		AS
		(
			SELECT	DISTINCT 
					wdmsta.sequencenumber						AS	[SequenceNumber],
					wdmsta.[name]								AS	[AliasName],
					rial.attributeuid							AS	[AttributeUID],
					rial.attributeparallelexecutiongroupinguid	AS	[AttributeParallelExecutionGroupingUID]
			FROM	@WorkDefinitionInfo	wdi
			JOIN	dbo.routingitem								ri		ON	ri.productionpathversionlinkuid					=	wdi.ProductionPathVersionLinkUID
			JOIN	dbo.routingitemworkdefinitionmasterrootlink	riwdmrl	ON	riwdmrl.routingitemuid							=	ri.[uid]
			JOIN	dbo.routingitemattributelink				rial	ON	rial.routingitemworkdefinitionmasterrootlinkuid	=	riwdmrl.[uid]
			JOIN	dbo.workdefinitionmastersmarttoolalias		wdmsta	ON	wdmsta.[uid]									=	rial.workdefinitionmastersmarttoolaliasuid
			WHERE	wdi.WorkDefinitionRootMasterVersionUID		=	riwdmrl.workdefinitionmasterrootversionuid	
			AND		wdi.WorkDefinitionMasterUID					=	rial.workdefinitionmasteruid
		)
		UPDATE	a
		SET		a.SmartToolAliasName	=	sma.AliasName
		FROM	@Attributes	a
		JOIN	SmartToolAlias	sma	ON	sma.SequenceNumber	=	a.SequenceNumber
										AND
										(
											sma.AttributeUID							=	a.AttributeUID
											OR
											sma.AttributeParallelExecutionGroupingUID	=	a.AttributeParallelExecutionGroupingUID
										)
	END
	--==========================================================================================================================================
	--	Verify which attributes are inputs to calculations in the same work instruction
	--==========================================================================================================================================
	IF EXISTS
	(
		SELECT	1
		FROM	@Attributes
	)
	BEGIN
		SET @ErrorSection = N'Get calculation dependencies'

		INSERT INTO @CalculationInputDependencies
		(
			AttributeUID,
			WorkDefinitionUID,
			WorkDefinitionName,
			WorkDefinitionDescription
		)
		SELECT	a.AttributeUID,
				wd.[uid],
				wd.[name],
				wd.[description]
		FROM	@WorkDefinitionInfo wdi
		JOIN	dbo.workdefinitioncalculationinputlink wdcil	ON	wdcil.workdefinitionuid	= wdi.WorkDefinitionUID
		JOIN	@Attributes a									ON	a.AttributeUID			= wdcil.attributeuid
		JOIN	dbo.workdefinitioncalculationlink wdcl			ON	wdcl.[uid]				= wdcil.workdefinitioncalculationlinkuid
		JOIN	dbo.workdefinition wd							ON	wd.[uid]				= wdcl.workdefinitionuid
		JOIN	dbo.workdefinitionmastertype wdmt				ON	wdmt.[uid]				= wd.workdefinitionmastertypeuid
		WHERE	wdmt.[name] = @STEP_TYPE_CALCULATION
	END
	--==========================================================================================================================================
	--	Get control characteristics for attributes and 
	--==========================================================================================================================================
	IF EXISTS
	(
		SELECT	1
		FROM	@Attributes
		WHERE	RetrieveControlCharacteristics = 1
	)
	BEGIN
		SET @ErrorSection = N'Get control characteristics'
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Get attribute groups related to the found attributes
		----------------------------------------------------------------------------------------------------------------------------------------
		INSERT INTO @AttributeGroups
		(
			[UID],
			[Name],
			SelectionListUID,
			RejectOutdated
		)
		SELECT	DISTINCT 
				a.AttributeParallelExecutionGroupingUID,
				apeg.[name],
				aap.ext_selectionlistuid,
				COALESCE(aap.ext_rejectoutdated,0)
		FROM	@Attributes a
		JOIN	dbo.attributeparallelexecutiongrouping apeg	ON	apeg.[uid] = a.AttributeParallelExecutionGroupingUID
		LEFT
		JOIN	dbo.attribute_ap aap ON aap.[uid] = a.AttributeUID
		WHERE	a.RetrieveControlCharacteristics = 1
		AND		@MeasureSingleValue = 0
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Prepare input JSON for control characteristic retrieval
		----------------------------------------------------------------------------------------------------------------------------------------
		IF EXISTS 
		(
			SELECT	1
			FROM	@AttributeGroups
		)
		BEGIN
			INSERT INTO @ControlCharacteristicRequests
			(
				AttGroupRcdIdx,
				AttributeParallelExecutionGroupingUID,
				ProductUID,
				ProductTypeUID,
				EquipmentUID,
				StartTime,
				IsSmartTool,
				WorkDefinitionMastergroupUID,
				WorkDefinitionMasterRootUID,
				WorkDefinitionMasterUID
			)
			SELECT	DISTINCT 
					ag.RcdIdx,
					ag.[UID],
					wdi.ProductUID,
					wdi.ProductTypeUID,
					wdi.EquipmentUID,
					CASE
						WHEN	wdi.WorkDefinitionMasterTypeName	= @STEP_TYPE_SMART_TOOL
							THEN	CONVERT(NVARCHAR(25), GETUTCDATE(), 120)
						ELSE
							CONVERT(NVARCHAR(25), @LimitTimestamp, 120)
					END,
					CASE
						WHEN	wdi.WorkDefinitionMasterTypeName	= @STEP_TYPE_SMART_TOOL
							THEN	1
						ELSE
							0
					END,
					wdi.WorkDefinitionMasterGroupUID,
					v.workdefinitionmasterrootuid,
					wdi.WorkDefinitionMasterUID
			FROM	@AttributeGroups ag
			CROSS 
			JOIN	@WorkDefinitionInfo wdi
			JOIN	dbo.workdefinitionmasterrootversion v ON v.[uid] = wdi.WorkDefinitionRootMasterVersionUID
		END
		ELSE
		BEGIN
			INSERT INTO @ControlCharacteristicRequests
			(
				AttributeUID,
				ProductUID,
				ProductTypeUID,
				EquipmentUID,
				StartTime,
				IsSmartTool,
				WorkDefinitionMastergroupUID,
				WorkDefinitionMasterRootUID,
				WorkDefinitionMasterUID
			)
			SELECT	DISTINCT
                    a.AttributeUID,
					wdi.ProductUID,
					wdi.ProductTypeUID,
					wdi.EquipmentUID,
					CASE
						WHEN	wdi.WorkDefinitionMasterTypeName	= @STEP_TYPE_SMART_TOOL
							THEN	CONVERT(NVARCHAR(25), GETUTCDATE(), 120)
						ELSE
							CONVERT(NVARCHAR(25), @LimitTimestamp, 120)
					END,
					CASE
						WHEN	wdi.WorkDefinitionMasterTypeName	= @STEP_TYPE_SMART_TOOL
							THEN	1
						ELSE
							0
					END,
					wdi.WorkDefinitionMasterGroupUID,
					v.workdefinitionmasterrootuid,
					wdi.WorkDefinitionMasterUID
			FROM	@Attributes a
			CROSS 
			JOIN	@WorkDefinitionInfo wdi
			JOIN	dbo.workdefinitionmasterrootversion v ON v.[uid] = wdi.WorkDefinitionRootMasterVersionUID
			WHERE	a.RetrieveControlCharacteristics = 1
		END

		----------------------------------------------------------------------------------------------------------------------------------------
		--	Extract control characteristic data found
		----------------------------------------------------------------------------------------------------------------------------------------
		-- If execution retrieval time is the work instruction approval time, we can use the SP to retrieve limits for placeholder
		IF (
				@ExecutionLimitsReferenceTime = @RETRIEVAL_TYPE_APPROVAL_TIME 
				OR
				EXISTS
				(
					SELECT	1
					FROM	@WorkDefinitionInfo
					WHERE	WorkDefinitionMasterTypeName = @STEP_TYPE_SMART_TOOL			
				)
		)
		BEGIN
		SET @ControlCharacteristicInputJSON	=	(
													SELECT 	CONVERT(BIT,'false') AS [UsePagination],
															CONVERT(BIT,'false') AS [FromLimitConfigurationMashup],
															CONVERT(BIT,'false') AS [IsExport],
															(
																SELECT	DISTINCT
																		IIF(r1.IsSmartTool = 1, @CONTEXT_SMART_TOOLS, @CONTEXT_CWC_WORK_INSTRUCTIONS)	AS [ApplicationContext],
																		r1.RcdIdx		AS [LimitRequestIdentifier],
																		r1.StartTime	AS [StartTime],
																		r1.StartTime	AS [EndTime],
																		(
																			SELECT	DISTINCT
																					COALESCE(r2.AttributeParallelExecutionGroupingUID,r2.AttributeUID)										AS [UID],
																					IIF(r2.AttributeParallelExecutionGroupingUID IS NOT NULL, CONVERT(BIT,'true'), CONVERT(BIT,'false'))	AS [IsAttributeGroup]
																			FROM	@ControlCharacteristicRequests r2
																			WHERE	r2.RcdIdx = r1.RcdIdx
																			FOR JSON PATH
																		) AS [Attributes],
																		(
																			SELECT	r3.equipmentUID AS [UID]
																			FROM	@ControlCharacteristicRequests r3
																			WHERE	r3.RcdIdx = r1.RcdIdx
																			FOR JSON PATH
																		) AS [Equipments],
																		(
																			SELECT	r4.productUID AS [UID]
																			FROM	@ControlCharacteristicRequests r4
																			WHERE	r4.RcdIdx = r1.RcdIdx
																			FOR JSON PATH
																		) AS [Products],
																		(
																			SELECT	r5.WorkDefinitionMasterUID	AS [UID],
																					IIF(@ExecutionLimitsReferenceTime = @RETRIEVAL_TYPE_APPROVAL_TIME,CONVERT(BIT,N'false'),CONVERT(BIT,N'true'))	AS [IncludeDraft]
																			FROM	@ControlCharacteristicRequests r5
																			WHERE	r5.RcdIdx = r1.RcdIdx
																			FOR JSON PATH
																		) AS [WorkDefinitionMasters],
																		(
																			SELECT	r6.WorkDefinitionMasterRootUID	AS [UID]
																			FROM	@ControlCharacteristicRequests r6
																			WHERE	r6.RcdIdx = r1.RcdIdx
																			FOR JSON PATH
																		) AS [WorkDefinitionMasterRoots]
																FROM	@ControlCharacteristicRequests r1
																FOR JSON PATH
															) AS [LimitRequests]
														FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
													)
		------------------------------------------------------------------------------------------------------------------------------------
		--	Get control characteristic limits
		------------------------------------------------------------------------------------------------------------------------------------
		SET @NestedObjectName = N'dbo.PTC_FSU_CORE_CCC_GetControlCharacteristicLimits_SP'

		INSERT INTO @ControlCharacteristics
		(	
			LimitRequestIdentifier					,
			StartTime								,
			EndTime									,
			ControlCharacteristicLinkUID			,
			ControlCharacteristicUID				,
			ControlCharacteristicName				,
			ControlCharacteristicDisplayName		,
			UnitOfMeasureUID						,
			UnitOfMeasureName						,
			UnitOfMeasureDisplayName				,
			DataFormatUID							,
			DataFormatName							,
			EquipmentUID							,
			EquipmentName							,
			EquipmentDisplayName					,
			ProductUID								,
			ProductName								,
			ProductTypeUID							,
			ProductTypeName							,
			ProductTypeDisplayName					,
			AttributeUID							,
			AttributeName							,
			AttributeDisplayName					,
			AttributeParallelExecutionGroupingUID	,
			AttributeParallelExecutionGroupingName	,
			WorkDefinitionMasterUID					,
			WorkDefinitionMasterName				,
			WorkDefinitionMasterRootUID				,
			WorkDefinitionMasterRootName			,
			WorkDefinitionMasterRootDisplayName		,
			[Version]								,
			[Status]								,
			SmartToolUID							,
			SmartToolName							,
			SmartToolDisplayName					,
			WorkDocumentUID							,
			WorkDocumentName						,
			WorkDocumentDisplayName					,
			LotNumberUID							,
			LotNumberName							,
			IsInherited								,
			LowEntry								,
			LowReject								,
			LowWarning								,
			LowUser									,
			NominalValue							,
			HighUser								,
			HighWarning								,
			HighReject								,
			HighEntry								,
			TotalCount								
		)
		EXEC dbo.PTC_FSU_CORE_CCC_GetControlCharacteristicLimits_SP
				@op_ErrorGUID			= @ErrorGUID					OUTPUT,
				@op_ValidationCode		= @NestedValidationCode			OUTPUT,
				@op_ValidationMessage	= @NestedValidationMessage		OUTPUT,
				@p_InputJSON			= @ControlCharacteristicInputJSON
		END

		-- If execution retrieval time is current time, we need to find which limits were active at the time of the attribute value timestamp
		ELSE IF (
				@ExecutionLimitsReferenceTime = @RETRIEVAL_TYPE_CURRENT_TIME 
		)
		BEGIN
			INSERT INTO @ControlCharacteristics
			(
				LimitRequestIdentifier					,
				StartTime								,
				EndTime									,
				ControlCharacteristicLinkUID			,
				ProductUID								,
				ProductTypeUID							,
				AttributeUID							,
				AttributeParallelExecutionGroupingUID	,
				LowReject								,
				LowWarning								,
				NominalValue							,
				HighWarning								,
				HighReject								,
				[Priority]
			)
			SELECT	ccr.RcdIdx,
					ccl.starttime,
					ccl.endtime,
					ccl.[uid],
					ccl.productuid,
					ccl.producttypeuid,
					ccl.attributeuid,
					ccl.attributeparallelexecutiongroupinguid,
					ccl.lowreject,
					ccl.lowwarning,
					ccl.nominalvalue,
					ccl.highwarning,
					ccl.highreject,
					CASE 
						WHEN ccl.workdefinitionmastergroupuid IS NOT NULL AND ccl.productuid IS NOT NULL THEN 1
						WHEN ccl.workdefinitionmastergroupuid IS NOT NULL AND ccl.producttypeuid IS NOT NULL THEN 2
						WHEN ccl.workdefinitionmastergroupuid IS NOT NULL THEN 3
						WHEN ccl.productuid IS NOT NULL THEN 4
						WHEN ccl.producttypeuid IS NOT NULL THEN 5
						ELSE 6
					END
			FROM	@ControlCharacteristicRequests ccr
			JOIN	dbo.controlcharacteristiclink ccl	ON  (ccl.attributeuid = ccr.AttributeUID OR ccl.attributeparallelexecutiongroupinguid = ccr.AttributeParallelExecutionGroupingUID)
														AND	(ccl.productuid = ccr.ProductUID OR ccl.productuid IS NULL)
														AND	(ccl.producttypeuid = ccr.ProductTypeUID OR ccl.producttypeuid IS NULL)
														AND	(ccl.workdefinitionmastergroupuid = ccr.WorkDefinitionMasterGroupUID OR ccl.WorkDefinitionMasterGroupUID IS NULL)
														AND		ccl.starttime				<=	@LimitTimestamp
														AND		(
																	ccl.endtime				> @LimitTimestamp
																	OR
																	ccl.endtime				IS NULL
																)

			;WITH LimitsCTE
			AS 
			( 
				SELECT	ROW_NUMBER() OVER (PARTITION BY ccd.LimitRequestIdentifier ORDER BY ccd.[Priority] ASC) AS 'RowNumber',
						ccd.ControlCharacteristicLinkUID,
						ccd.LowReject,
						ccd.LowWarning,
						ccd.NominalValue,
						ccd.HighWarning,
						ccd.HighReject
				FROM	@ControlCharacteristics ccd
			)
			DELETE	cc
			FROM 	@ControlCharacteristics cc
			JOIN	LimitsCTE cte	ON cte.ControlCharacteristicLinkUID = cc.ControlCharacteristicLinkUID
			WHERE 	cte.RowNumber <> 1
		END
		
		----------------------------------------------------------------------------------------------------------------------------------------
		--	Retrieve control characteristic limits
		----------------------------------------------------------------------------------------------------------------------------------------
		SET @NestedObjectName = N''
		
		IF	@MeasureSingleValue = 0
		AND	EXISTS
		(
			SELECT	1
			FROM	@AttributeGroups
		)
		BEGIN
			UPDATE	a					--> if found limit on product use it first, then fallback if found limit on WorkInstruction
			SET		a.LowRejectValue	= COALESCE(cc.lowreject,   cc2.lowreject),
					a.LowWarningValue	= COALESCE(cc.lowwarning,  cc2.lowwarning),
					a.NominalValue		= COALESCE(cc.nominalvalue,cc2.nominalvalue),
					a.HighWarningValue	= COALESCE(cc.highwarning, cc2.highwarning),
					a.HighRejectValue	= COALESCE(cc.highreject,  cc2.highreject)
			FROM	@Attributes a
			JOIN	@WorkDefinitionInfo wdi ON wdi.WorkDefinitionUID = a.WDUID
			JOIN	@ControlCharacteristicRequests ccr	ON ccr.AttributeParallelExecutionGroupingUID = a.AttributeParallelExecutionGroupingUID
			LEFT	--> LEFT JOIN to see if exists limit matching the product
			JOIN	@ControlCharacteristics cc			ON	cc.LimitRequestIdentifier	= ccr.RcdIdx
														AND	cc.ProductUID				= wdi.ProductUID
			LEFT	--> LEFT JOIN to see if exists limit matching the WorkInstruction
			JOIN	@ControlCharacteristics cc2			ON	cc2.LimitRequestIdentifier	=	ccr.RcdIdx
														AND cc2.ProductUID				IS	NULL
            WHERE	a.RetrieveControlCharacteristics	=	1
		END
		ELSE
		BEGIN

			UPDATE	a
			SET		a.LowRejectValue	= cc.lowreject,
					a.LowWarningValue	= cc.lowwarning,
					a.NominalValue		= cc.nominalvalue,
					a.HighWarningValue	= cc.highwarning,
					a.HighRejectValue	= cc.highreject
			FROM	@Attributes a
			JOIN	@ControlCharacteristicRequests ccr	ON	ccr.AttributeUID			= a.AttributeUID
			JOIN	@ControlCharacteristics cc			ON	cc.LimitRequestIdentifier	= ccr.RcdIdx
            WHERE	a.RetrieveControlCharacteristics	=	1
		END
	END

	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Attributes that are out of limits (Reject) are not completed for smart tool & manual entry
	--------------------------------------------------------------------------------------------------------------------------------------------
	UPDATE 	a
	SET		a.IsLimitInclusive = inc.inclusivelimits
	FROM	@Attributes a
	JOIN	@InclusiveLimitConfigurationWorkTable	inc		ON	a.DataFormatName = inc.DataformatName
	WHERE	a.TimestampResult >= inc.StartTime
	AND	(
			a.TimestampResult < inc.EndTime
			OR
			inc.EndTime IS NULL
		)
	
	UPDATE	a
    SET		a.IsCompleted = 0
    FROM	@Attributes a
    WHERE	COALESCE(a.IsSmartToolWrite, 0) = 0
    AND 	(
				(
					a.AttributeValue IS NOT NULL
					AND
					(
						(
							a.DataFormatName = @NUMBER_DATA_TYPE
							AND
							(
								(
									a.LowRejectValue IS NOT NULL
									AND
									(
										(	a.IsLimitInclusive = 0
											AND 
									TRY_PARSE(a.AttributeValue AS NUMERIC(18,5)) <= TRY_PARSE(a.LowRejectValue AS NUMERIC(18,5))
								)
								OR
										(	a.IsLimitInclusive = 1
											AND 
											TRY_PARSE(a.AttributeValue AS NUMERIC(18,5)) < TRY_PARSE(a.LowRejectValue AS NUMERIC(18,5))
										)
									)
									
								)
								OR
								(
									a.HighRejectValue IS NOT NULL
									AND
									(
										(	a.IsLimitInclusive = 0
											AND 
									TRY_PARSE(a.AttributeValue AS NUMERIC(18,5)) >= TRY_PARSE(a.HighRejectValue AS NUMERIC(18,5))
								)
										OR
										(	a.IsLimitInclusive = 1
											AND 
											TRY_PARSE(a.AttributeValue AS NUMERIC(18,5)) > TRY_PARSE(a.HighRejectValue AS NUMERIC(18,5))
										)
									)
								)
							)
						)
						OR
						(
							a.DataFormatName = @BOOLEAN_DATA_TYPE
							AND
							(
								a.NominalValue IS NOT NULL
								AND
								TRY_PARSE(a.AttributeValue AS NUMERIC(18,5)) <> TRY_PARSE(a.NominalValue AS NUMERIC(18,5))
							)
						)
					)
				)
				OR
				a.AttributeValue	IS NULL
			)
    --------------------------------------------------------------------------------------------------------------------------------------------
	--	For smart tool step & manual entry, compare total and completed attribute counts to define completed state
	--------------------------------------------------------------------------------------------------------------------------------------------
    ;WITH	TotalAttributes
	(
		WorkDefinitionUID,
		TotalCount
	)
	AS
	(
		SELECT	WDUID,
				COUNT(1)
		FROM	@Attributes
		GROUP
		BY		WDUID
	),
	CompletedAttributes
	(
		WorkDefinitionUID,
		TotalCount,
		CompletedCount
	)
	AS
	(
		SELECT	a.WDUID,
				ta.TotalCount,
				COUNT(a.IsCompleted)
		FROM	@Attributes	a
		JOIN	TotalAttributes	ta	ON ta.WorkDefinitionUID	= a.WDUID
		WHERE	IsCompleted	= 1
		GROUP
		BY		a.WDUID,
				ta.TotalCount
	)
	UPDATE	wdi
	SET		wdi.StepIsCompleted	= 1
	FROM	@WorkDefinitionInfo	wdi
	JOIN	(
				SELECT	DISTINCT
						a.WDUID,
						ca.TotalCount,
						ca.CompletedCount
				FROM	@Attributes	a
				JOIN	CompletedAttributes	ca	ON ca.WorkDefinitionUID	= a.WDUID
			)	ca	ON	ca.WDUID		= wdi.WorkDefinitionUID
					AND	ca.TotalCount	= ca.CompletedCount
    WHERE   wdi.WorkDefinitionMasterTypeName    IN (@STEP_TYPE_SMART_TOOL, @STEP_TYPE_MANUALENTRY)
    --==========================================================================================================================================
    --  Prepare DetailStepDataJSON
    --==========================================================================================================================================
	SET @ErrorSection = N'Prepare DetailStepDataJSON'
    
	INSERT INTO @DetailStepDataInfo
    (
		RejectMessage,
		WorkDefinitionMasterTypeName,
		SerialNumberUID,
        SerialNumberValue,
		WorkDefinitionUID,
		WorkDefinitionRootUID,
		ParallelExecutionSequenceNumber,
		SerialNumberStepIsComplete,
		SerialNumberIsSelected,
		EstimatedEndTime,
		InstructionalText,
		LogSheetWorkDocumentRootName,
		VerificationStepCompleted,
		SelectionListItemUID
    )
	SELECT	DISTINCT
			wdi.RejectMessage,
			wdi.WorkDefinitionMasterTypeName,
			wdi.SerialNumberUID,
            wdi.SerialNumberValue,
			wdi.WorkDefinitionUID,
			wdi.WorkDefinitionRootUID,
			wdi.ParallelExecutionSequenceNumber,
			wdi.StepIsCompleted,
			CASE @NbWorkDefinitions
				WHEN 1
					THEN '1'
				ELSE
					'0'
			END,
			CONVERT(NVARCHAR(50),@PlannedEndTime,127),
			wdi.InstructionalText,
			wdi.LogSheetWorkDocumentRootName,
			CASE
				WHEN wdi.WorkDefinitionMasterTypeName = @STEP_TYPE_VERIFICATION
					THEN wdi.VerificationStepCompleted
				ELSE
					0
			END,
			wdi.SelectionListItemUID
	FROM	@WorkDefinitionInfo wdi

	IF @IsPartValidationEXTEnabled = 1
	BEGIN
		UPDATE	dsd
		SET		dsd.IsReworkRoute = ppvl_ap.EXT_IsReworkRoute
		FROM	@DetailStepDataInfo	dsd
		JOIN	dbo.WorkDefinitionRoot	wdr	ON	dsd.WorkDefinitionRootUID = wdr.[UID]
		JOIN	dbo.ProductionPathVersionLink	ppvl	ON	wdr.productionpathversionlinkuid = ppvl.[uid]
		LEFT
		JOIN	dbo.ProductionPathVersionLink_AP	ppvl_ap	ON	ppvl.[uid] = ppvl_ap.[uid]

		--	Now that we have rework route property - if true
		--	Include all consumption events in consumption array
		IF EXISTS
		(
			SELECT	1
			FROM	@DetailStepDataInfo
			WHERE	IsReworkRoute = 1
		)
		BEGIN
			INSERT INTO @PartConsumptions
			(
				WorkDefinitionUID,
				BillOfMaterialItemUID,
				BillOfMaterialSubstitutionUID,
				ProductUID,
				ProductName,
				ProductDisplayName,
				ConsumedQuantity,
				SerialNumberUID,
				SerialNumberValue
			)
			SELECT	wdi.WorkDefinitionUID,
					wdmil.[uid],
					wdsnp.BillOfMaterialSubstitutionUID,
					pr.[uid],
					pr.[name],
					pr.DisplayName,
					wdsnp.Quantity,
					wdi.SerialNumberUID,
					wdi.SerialNumberValue
			FROM	@WorkDefinitionInfo	wdi
			JOIN	@DetailStepDataInfo	dsd	ON	wdi.WorkDefinitionUID = dsd.WorkDefinitionUID
			JOIN	dbo.WorkDefinitionRoot	wdr	ON	dsd.WorkDefinitionRootUID = wdr.[UID]
			JOIN	dbo.WorkDefinition	wd	ON	wdr.[UID] = wd.WorkDefinitionRootUID
			JOIN	dbo.workdefinitionmaterialitemlink	wdmil	ON wdmil.workdefinitionuid = wd.[uid]
			JOIN	dbo.billofmaterialitem	bomi	ON	bomi.[uid] = wdmil.billofmaterialitemuid
			JOIN	@Parts	p	ON	bomi.[uid] = p.BillOfMaterialItemUID
			JOIN	dbo.ext_workdefinitionserialnumberconsumption	wdsnp	ON	wdmil.[uid] = wdsnp.WorkDefinitionMaterialItemLinkUID
			JOIN	dbo.product	pr	ON	wdsnp.ProductUID = pr.[uid]
			--	Only include consumption events where bom item is serialized
			WHERE	p.IsSerialized = 1
			--	Only include consumption events where the route is set as rework route
			AND		dsd.IsReworkRoute = 1
			--	Only include consumption events for steps that did not already have consumption gathered
			AND		wd.[UID] NOT IN	(
										SELECT	DISTINCT
												WorkDefinitionUID
										FROM	@PartConsumptions
									)
		END
	END

	SET @SerialNumberCount = @@ROWCOUNT

	SET @CompletedSerialNumberCount =	(
											SELECT 	COUNT(1)
											FROM 	@DetailStepDataInfo
											WHERE	SerialNumberStepIsComplete = '1'
										)

	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Prepare detailed step JSON
	--------------------------------------------------------------------------------------------------------------------------------------------
	SET @DetailStepDataJSON =	(
									SELECT	dsi.SerialNumberUID,
											dsi.SerialNumberValue,
											dsi.WorkDefinitionUID,
											dsi.WorkDefinitionRootUID,
											dsi.SerialNumberStepIsComplete,
											dsi.SerialNumberIsSelected,
											dsi.EstimatedEndTime,
											dsi.IsReworkRoute,
											JSON_QUERY
											(
												(
													SELECT	dsi2.VerificationStepCompleted AS [IsVerified]
													FROM	@DetailStepDataInfo dsi2
													JOIN	@WorkDefinitionInfo wdi	ON	wdi.WorkDefinitionUID = dsi2.WorkDefinitionUID
													WHERE	dsi2.Id								= dsi.Id
													AND		wdi.WorkDefinitionMasterTypeName	= @STEP_TYPE_VERIFICATION
													FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
												)
											) AS [VerificationStepInfo],
											JSON_QUERY
											(
												(
													SELECT	slc.SelectionListUID,
															dsi3.SelectionListItemUID
													FROM	@DetailStepDataInfo dsi3
													JOIN	@SelectionListContent slc	ON	slc.SelectionListItemUID = dsi3.SelectionListItemUID
													WHERE	dsi3.Id = dsi.Id
													FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
												) 
											) AS [ListStepInfo],
											JSON_QUERY
											(
												(
													SELECT	CompletedOn,
											JSON_QUERY
											(
												(
													SELECT	ag.[UID],
																	ag.[Name],
																	COALESCE(ag.SelectionListUID,NULL) AS [SelectionListUID],
																	COALESCE(ag.RejectOutdated,0)  AS [RejectOutdated]
													FROM	@AttributeGroups ag
													WHERE	dsi.WorkDefinitionMasterTypeName = @STEP_TYPE_MANUALENTRY
                                                    ORDER
															BY      ag.[Name]	ASC
													FOR JSON PATH
												)
													) AS [AttributeGrouping],
											JSON_QUERY
											(
												(
													SELECT	a.AttributeUID,
															a.AttributeDisplayName,
															a.AttributeParallelExecutionGroupingUID,
															a.UnitOfMeasureName,
															a.UnitOfMeasureDisplayName,
															a.DataFormatName,
															a.TimestampResult,
															a.LowRejectValue,
															a.LowWarningValue,
															a.NominalValue,
															a.HighWarningValue,
															a.HighRejectValue,
															a.RejectOutdated,
															a.AttributeValue,
															-- List Value selected by the user
															(
																SELECT	asl.SelectionListUID,
																		IIF (a.DataFormatName = @LIST_DATA_TYPE, a.AttributeValue, NULL) AS [SelectionListItemUID]
																FOR JSON PATH
															)	AS [AttributeSelectionListInfo],
															-- Complete List with items - populates Dropdown
															(
																SELECT	asl.SelectionListUID,
																		asl.SelectionListDisplayName,
																		(
																			SELECT	aslc.SelectionListItemUID,
																					aslc.SelectionListItemDisplayName
																			FROM	@AttributeSelectionListContent aslc
																			WHERE	aslc.SelectionListUID = asl.SelectionListUID
																					AND		aslc.AttributeUID = a.AttributeUID
																			FOR JSON PATH
																		)	AS [SelectionListItem]
																		FOR JSON PATH
															)	AS [AttributeListInfo]
													FROM	@Attributes a
                                                    LEFT
                                                    JOIN	@AttributeGroups ag ON a.AttributeParallelExecutionGroupingUID = ag.[UID]
													LEFT
													JOIN	@AttributeSelectionList asl ON asl.AttributeUID = a.AttributeUID
													WHERE	dsi.WorkDefinitionMasterTypeName	= @STEP_TYPE_MANUALENTRY
													AND		dsi.WorkDefinitionUID				= a.WDUID
                                                    ORDER
                                                    BY      ag.[Name] ASC,
															a.AttributeDisplayName ASC
													FOR JSON PATH, INCLUDE_NULL_VALUES
												)
													) AS [Attributes]
													FROM	@WorkDefinitionInfo
													WHERE	WorkDefinitionMasterTypeName = @STEP_TYPE_MANUALENTRY
                                                    ORDER
                                                    BY      StepName ASC
													FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
												)
											) AS [ManualStepInfo],
											JSON_QUERY
											(
												(
													SELECT	a.SequenceNumber,
															a.AttributeUID,
															a.AttributeName,
															a.AttributeDisplayName,
															a.AttributeParallelExecutionGroupingUID,
															a.UnitOfMeasureName,
															a.UnitOfMeasureDisplayName,
															a.DataFormatName,
															a.AttributeValue,
															a.ValueToWrite,
															a.TimestampResult,
															a.LowRejectValue,
															a.LowWarningValue,
															a.NominalValue,
															a.HighWarningValue,
															a.HighRejectValue,
															a.IsSmartToolWrite,
															a.IsOverridden,
															a.IsCompleted,
															a.SmartToolAliasName
													FROM	@Attributes a
													WHERE	dsi.WorkDefinitionMasterTypeName	= @STEP_TYPE_SMART_TOOL
													AND		dsi.WorkDefinitionUID				= a.WDUID
													ORDER
													BY		a.AttributeDisplayName ASC
													FOR JSON PATH, INCLUDE_NULL_VALUES
												)
											) AS [SmartToolStepInfo],
											JSON_QUERY
											(
												(
													SELECT	a.AttributeUID,
															a.AttributeName,
															a.AttributeDisplayName,
															dsi.RejectMessage,
															a.CalculationName,
															a.UnitOfMeasureName,
															a.UnitOfMeasureDisplayName,
															a.DataFormatName,
															a.Formula,
															a.AttributeValue,
															a.TimestampResult,
															a.LowRejectValue,
															a.LowWarningValue,
															a.NominalValue,
															a.HighWarningValue,
															a.HighRejectValue,
															JSON_QUERY
															(
																(
																	SELECT	ci.WDUID,
																			ci.WorkDefinitionName,
																			ci.WorkDefinitionDescription,
																			ci.WorkDefinitionRootDisplay,
																			ci.IsCurrentWorkDefinitionRoot,
																			ci.AttributeCalculationMasterInputName,
																			ci.AttributeCalculationMasterInputDisplayName,
																			ci.AttributeValue
																	FROM	@CalculationInputs ci
																	WHERE	dsi.WorkDefinitionMasterTypeName = @STEP_TYPE_CALCULATION
																	FOR JSON PATH
																)
															) AS [CalculationStepInputInfo]
													FROM	@Attributes a
													WHERE	dsi.WorkDefinitionMasterTypeName	= @STEP_TYPE_CALCULATION
													AND		dsi.WorkDefinitionUID				= a.WDUID
													ORDER
													BY		a.AttributeDisplayName ASC
													FOR JSON PATH
												)
											) AS [CalculationStepInfo],
											JSON_QUERY
											(
												(
													SELECT	p.SequenceNumber,
															p.BillOfMaterialItemUID,
															p.BillOfMaterialItemDisplayName,
															p.ProductUID,
															p.ProductName,
															p.ProductDisplayName,
															p.RequiredQuantity,
															p.ActualProductName,
															p.ActualQuantity,
															p.IsCompleted,
															p.IsQuantity,
															p.IsSerialized,
															p.UniqueSerialNumberSource,
															JSON_QUERY
															(
																(
																	SELECT	ps.ProductUID,
																			ps.ProductName,
																			ps.ProductDisplayName,
																			ps.RequiredQuantity
																	FROM	@PartSubstitutions ps
																	WHERE	ps.BillOfMaterialItemUID = p.BillOfMaterialItemUID
																	FOR JSON PATH
																)
															) AS [Substitution],
															JSON_QUERY
															(
																(
																	SELECT	pc.WorkDefinitionSerialNumberConsumptionUID AS [UID],
																			pc.WorkDefinitionUID,
																			pc.BillOfMaterialItemUID,
																			pc.BillOfMaterialSubstitutionUID,
																			pc.ProductUID,
																			pc.ProductName,
																			pc.ProductDisplayName,
																			pc.ConsumedQuantity,
																			pc.SerialNumberUID,
																			pc.SerialNumberValue
																	FROM	@PartConsumptions	pc
																	WHERE	pc.BillOfMaterialItemUID = p.BillOfMaterialItemUID
																	FOR JSON PATH
																)
															)	AS	[Consumptions]
													FROM	@Parts p
													WHERE	dsi.WorkDefinitionMasterTypeName = @STEP_TYPE_PARTVALIDATION
													ORDER 
													BY		p.SequenceNumber ASC
													FOR JSON PATH
												)
											) AS [PartsValidationStepInfo],
											JSON_QUERY
											(
												(
													SELECT	DISTINCT
                                                    		cid.WorkDefinitionUID,
															cid.WorkDefinitionName,
															cid.WorkDefinitionDescription
													FROM	@CalculationInputDependencies cid
													JOIN 	@Attributes a 						ON a.AttributeUID = cid.AttributeUID
													WHERE	dsi.WorkDefinitionMasterTypeName	IN (@STEP_TYPE_MANUALENTRY, @STEP_TYPE_CALCULATION, @STEP_TYPE_SMART_TOOL)
													AND		dsi.WorkDefinitionUID				= a.WDUID
													FOR JSON PATH
												)
											) AS [ParentCalculationDataJSON]
									FROM 	@DetailStepDataInfo dsi
									FOR JSON PATH, ROOT('SerialNumberInfo')
								)
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Add extra JSON fields on the level above SerialNumberInfo
	--------------------------------------------------------------------------------------------------------------------------------------------
	SET @DetailStepDataJSON = JSON_MODIFY(@DetailStepDataJSON, '$.InstructionalText', (SELECT TOP 1 InstructionalText FROM @WorkDefinitionInfo ORDER BY WorkDefinitionUID ASC)) -- We may have multiple step here but should been all from parallel execution, with the same instructional text for every step
	SET @DetailStepDataJSON = JSON_MODIFY(@DetailStepDataJSON, '$.LogSheetWorkDocumentRootName', (SELECT TOP 1 LogSheetWorkDocumentRootName FROM @WorkDefinitionInfo ORDER BY WorkDefinitionUID ASC)) -- We may have multiple step here but should been all from parallel execution, with the same log sheet for every step	
	SET @DetailStepDataJSON = JSON_MODIFY(@DetailStepDataJSON, '$.SerialNumberCount', @SerialNumberCount)
	SET @DetailStepDataJSON = JSON_MODIFY(@DetailStepDataJSON, '$.CompletedSerialNumberCount', @CompletedSerialNumberCount)
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Add details for the smart tool step
	--------------------------------------------------------------------------------------------------------------------------------------------
	IF EXISTS
	(
		SELECT	1
		FROM	@SmartToolStep
	)	
	BEGIN
		SET @SmartToolStepInfo	= JSON_QUERY((
												SELECT	AllowSubstitution,
														SmartToolTypeUIDAllowed,
														AllowValueOverride,
														SmartToolUID,
														SmartToolName,
														SmartToolDisplayName
												FROM	@SmartToolStep
												FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
											))

		SET @DetailStepDataJSON = JSON_MODIFY(@DetailStepDataJSON, '$.SmartToolStepInfo', JSON_QUERY((@SmartToolStepInfo)))
	END
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Add details for the list step
	--------------------------------------------------------------------------------------------------------------------------------------------
	IF EXISTS
	(
		SELECT	1	
		FROM	@SelectionList
	)
	BEGIN
		SET @ListStepInfo =	JSON_QUERY
							(
								(
									SELECT	sl.SelectionListUID,
											sl.SelectionListDisplayName,
											JSON_QUERY
											(
												(
													SELECT	slc.SelectionListItemUID,
															slc.SelectionListItemDisplayName
													FROM	@SelectionListContent slc
													WHERE	slc.SelectionListUID = sl.SelectionListUID
													FOR JSON PATH
												)
											) AS [SelectionListItem]
									FROM	@SelectionList sl
									FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
								) 
							)
		SET @DetailStepDataJSON = JSON_MODIFY(@DetailStepDataJSON, '$.ListStepInfo', JSON_QUERY(@ListStepInfo))
	END
	--==========================================================================================================================================
    --  Prepare ExecutionNotesJSON
    --==========================================================================================================================================
	SET @ErrorSection = N'Prepare ExecutionNotesJSON'
    
	INSERT INTO @ExecutionNotesInfo
    (
		WorkDefinitionUID,
		SerialNumberUID,
		NoteUID,
		NoteText
    )
	SELECT	wdi.WorkDefinitionUID,
			wdi.SerialNumberUID,
			n.[UID],
			n.body
	FROM	@WorkDefinitionInfo wdi
	LEFT
	JOIN	dbo.note n				ON n.workdefinitionuid = wdi.WorkDefinitionUID

	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Prepare execution note JSON
	--------------------------------------------------------------------------------------------------------------------------------------------
	SET @ExecutionNotesJSON =	(
									SELECT	exi.WorkDefinitionUID,
											exi.SerialNumberUID,
											exi.NoteUID,
											exi.NoteText
									FROM 	@ExecutionNotesInfo exi
									FOR JSON PATH, ROOT('Note'), INCLUDE_NULL_VALUES
	)
	
	--==========================================================================================================================================
    --  Return final results
    --==========================================================================================================================================
	SELECT	@GenericStepDataJSON	AS [GenericStepDataJSON],
			@DetailStepDataJSON		AS [DetailStepDataJSON],
			@ExecutionNotesJSON		AS [ExecutionNotesJSON]

    ------------------------------------------------------------------------------------------------------------------------------------------------
    --	Complete
    ------------------------------------------------------------------------------------------------------------------------------------------------
	SET @ValidationCode = @ERROR_NONE
    SET @ValidationMessage = N'Action Completed Successfully'
END TRY
--==============================================================================================================================================
--  Log critical error messages raised in the main body of logic.
--==============================================================================================================================================
BEGIN CATCH
	--==========================================================================================================================================
	--	ERROR LOGGING LOGIC
	--==========================================================================================================================================
    SET @ValidationMessage   = 	ERROR_MESSAGE()
								+ ' - '
								+ @Parameters

	IF @ValidationCode IS NULL
	BEGIN
		SET	@ValidationCode = @ERROR_CRITICAL
	END
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Set the output validation code parameter to a critical error if it still NULL
	--------------------------------------------------------------------------------------------------------------------------------------------
	IF @IsUserError = 1
	BEGIN
		SET	@ErrorSeverityLevel = @ERROR_USER
	END
	ELSE
	BEGIN
		SET	@ErrorSeverityLevel = @ERROR_CRITICAL
	END
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Assign error message parameter values.
	--------------------------------------------------------------------------------------------------------------------------------------------
	SET @ErrorGUID		= NEWID()
	SET @ErrorState		= ERROR_STATE()
	SET @ErrorSeverity	= ERROR_SEVERITY()
	--------------------------------------------------------------------------------------------------------------------------------------------
	--	Log error message.
	--------------------------------------------------------------------------------------------------------------------------------------------
    EXECUTE dbo.MnfgCommon_LogErrorMessage_SP
			@op_ErrorGUID			= @ErrorGUID,
			@p_ObjectName			= @OBJECT_NAME,
			@p_ErrorSection			= @ErrorSection,
			@p_ErrorMessage			= @ValidationMessage,
			@p_ErrorSeverityLevel	= @ErrorSeverityLevel,
			@p_ErrorSeverity		= @ErrorSeverity,
			@p_ErrorState			= @ErrorState,
			@p_ErrorCode			= @ValidationCode
END CATCH

Finish:

IF	@ValidationCode <> @ERROR_NONE
BEGIN
	IF	EXISTS
	(
		SELECT	1
		FROM	@MessageToken
	)
	BEGIN
		SET @ValidationToken =	CONVERT
								(	NVARCHAR(MAX),
									(
										SELECT	mt.TokenName	AS	tokenName,
												(
													SELECT	mph.placeHolder	AS	id,
															mph.[value]		AS	replacement
													FROM	@MessagePlaceHolder mph
													FOR		JSON PATH
												)	AS	placeHolders
										FROM	@MessageToken mt
										FOR		JSON PATH, WITHOUT_ARRAY_WRAPPER
									)
								)
	END
END

SELECT  @ValidationCode     AS  [ValidationCode],
        @ValidationMessage  AS  [ValidationMessage],
		@ValidationToken	AS	[ValidationToken]

SET ANSI_NULLS OFF
SET NOCOUNT OFF