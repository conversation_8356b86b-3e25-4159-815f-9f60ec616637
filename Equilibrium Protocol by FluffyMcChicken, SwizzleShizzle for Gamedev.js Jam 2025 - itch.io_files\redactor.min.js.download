(()=>{var Nt=Object.create,Ke=Object.defineProperty,It=Object.getOwnPropertyDescriptor,Ge=Object.getOwnPropertyNames,Lt=Object.getPrototypeOf,Bt=Object.prototype.hasOwnProperty,Dt=(r,d)=>function(){return d||(0,r[Ge(r)[0]])((d={exports:{}}).exports,d),d.exports},Ot=(r,d,f,e)=>{if(d&&typeof d=="object"||typeof d=="function")for(let t of Ge(d))!Bt.call(r,t)&&t!==f&&Ke(r,t,{get:()=>d[t],enumerable:!(e=It(d,t))||e.enumerable});return r},Pt=(r,d,f)=>(f=r!=null?Nt(Lt(r)):{},Ot(d||!r||!r.__esModule?Ke(f,"default",{value:r,enumerable:!0}):f,r)),Ft=Dt({"lib/purify.min.js"(r,d){(function(f,e){typeof r=="object"&&typeof d!="undefined"?d.exports=e():typeof define=="function"&&define.amd?define(e):(f=typeof globalThis!="undefined"?globalThis:f||self).DOMPurify=e()})(r,function(){"use strict";const{entries:f,setPrototypeOf:e,isFrozen:t,getPrototypeOf:i,getOwnPropertyDescriptor:s}=Object;let{freeze:o,seal:n,create:a}=Object,{apply:c,construct:u}=typeof Reflect!="undefined"&&Reflect;o||(o=function(T){return T}),n||(n=function(T){return T}),c||(c=function(T,x,b){return T.apply(x,b)}),u||(u=function(T,x){return new T(...x)});const h=H(Array.prototype.forEach),g=H(Array.prototype.pop),m=H(Array.prototype.push),E=H(String.prototype.toLowerCase),A=H(String.prototype.toString),J=H(String.prototype.match),j=H(String.prototype.replace),X=H(String.prototype.indexOf),q=H(String.prototype.trim),U=H(Object.prototype.hasOwnProperty),O=H(RegExp.prototype.test),oe=(Xe=TypeError,function(){for(var T=arguments.length,x=new Array(T),b=0;b<T;b++)x[b]=arguments[b];return u(Xe,x)});var Xe;function H(T){return function(x){for(var b=arguments.length,S=new Array(b>1?b-1:0),_=1;_<b;_++)S[_-1]=arguments[_];return c(T,x,S)}}function y(T,x){let b=arguments.length>2&&arguments[2]!==void 0?arguments[2]:E;e&&e(T,null);let S=x.length;for(;S--;){let _=x[S];if(typeof _=="string"){const ae=b(_);ae!==_&&(t(x)||(x[S]=ae),_=ae)}T[_]=!0}return T}function Ht(T){for(let x=0;x<T.length;x++)U(T,x)||(T[x]=null);return T}function V(T){const x=a(null);for(const[b,S]of f(T))U(T,b)&&(Array.isArray(S)?x[b]=Ht(S):S&&typeof S=="object"&&S.constructor===Object?x[b]=V(S):x[b]=S);return x}function ne(T,x){for(;T!==null;){const b=s(T,x);if(b){if(b.get)return H(b.get);if(typeof b.value=="function")return H(b.value)}T=i(T)}return function(){return null}}const Ve=o(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Ce=o(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Ee=o(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Mt=o(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Ae=o(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Ut=o(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Qe=o(["#text"]),Je=o(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Se=o(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Ze=o(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),pe=o(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),zt=n(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Wt=n(/<%[\w\W]*|[\w\W]*%>/gm),qt=n(/\$\{[\w\W]*}/gm),Kt=n(/^data-[\-\w.\u00B7-\uFFFF]+$/),Gt=n(/^aria-[\-\w]+$/),$e=n(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Yt=n(/^(?:\w+script|data):/i),jt=n(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),et=n(/^html$/i),Xt=n(/^[a-z][.\w]*(-[.\w]+)+$/i);var tt=Object.freeze({__proto__:null,ARIA_ATTR:Gt,ATTR_WHITESPACE:jt,CUSTOM_ELEMENT:Xt,DATA_ATTR:Kt,DOCTYPE_NAME:et,ERB_EXPR:Wt,IS_ALLOWED_URI:$e,IS_SCRIPT_OR_DATA:Yt,MUSTACHE_EXPR:zt,TMPLIT_EXPR:qt});const Vt=1,Qt=3,Jt=7,Zt=8,$t=9,ei=function(){return typeof window=="undefined"?null:window};var ti=function T(){let x=arguments.length>0&&arguments[0]!==void 0?arguments[0]:ei();const b=l=>T(l);if(b.version="3.2.3",b.removed=[],!x||!x.document||x.document.nodeType!==$t)return b.isSupported=!1,b;let{document:S}=x;const _=S,ae=_.currentScript,{DocumentFragment:it,HTMLTemplateElement:ii,Node:Re,Element:rt,NodeFilter:le,NamedNodeMap:ri=x.NamedNodeMap||x.MozNamedAttrMap,HTMLFormElement:si,DOMParser:oi,trustedTypes:ge}=x,ce=rt.prototype,ni=ne(ce,"cloneNode"),ai=ne(ce,"remove"),li=ne(ce,"nextSibling"),ci=ne(ce,"childNodes"),ve=ne(ce,"parentNode");if(typeof ii=="function"){const l=S.createElement("template");l.content&&l.content.ownerDocument&&(S=l.content.ownerDocument)}let P,de="";const{implementation:Ne,createNodeIterator:di,createDocumentFragment:ui,getElementsByTagName:hi}=S,{importNode:fi}=_;let M={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};b.isSupported=typeof f=="function"&&typeof ve=="function"&&Ne&&Ne.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:Ie,ERB_EXPR:Le,TMPLIT_EXPR:Be,DATA_ATTR:pi,ARIA_ATTR:gi,IS_SCRIPT_OR_DATA:vi,ATTR_WHITESPACE:st,CUSTOM_ELEMENT:mi}=tt;let{IS_ALLOWED_URI:ot}=tt,L=null;const nt=y({},[...Ve,...Ce,...Ee,...Ae,...Qe]);let D=null;const at=y({},[...Je,...Se,...Ze,...pe]);let R=Object.seal(a(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),ue=null,De=null,lt=!0,Oe=!0,ct=!1,dt=!0,Z=!1,Pe=!0,Q=!1,Fe=!1,_e=!1,ee=!1,me=!1,be=!1,ut=!0,ht=!1,He=!0,he=!1,te={},ie=null;const ft=y({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let pt=null;const gt=y({},["audio","video","img","source","image","track"]);let Me=null;const vt=y({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ye="http://www.w3.org/1998/Math/MathML",ke="http://www.w3.org/2000/svg",K="http://www.w3.org/1999/xhtml";let re=K,Ue=!1,ze=null;const bi=y({},[ye,ke,K],A);let we=y({},["mi","mo","mn","ms","mtext"]),xe=y({},["annotation-xml"]);const yi=y({},["title","style","font","a","script"]);let fe=null;const ki=["application/xhtml+xml","text/html"];let B=null,se=null;const wi=S.createElement("form"),mt=function(l){return l instanceof RegExp||l instanceof Function},We=function(){let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!se||se!==l){if(l&&typeof l=="object"||(l={}),l=V(l),fe=ki.indexOf(l.PARSER_MEDIA_TYPE)===-1?"text/html":l.PARSER_MEDIA_TYPE,B=fe==="application/xhtml+xml"?A:E,L=U(l,"ALLOWED_TAGS")?y({},l.ALLOWED_TAGS,B):nt,D=U(l,"ALLOWED_ATTR")?y({},l.ALLOWED_ATTR,B):at,ze=U(l,"ALLOWED_NAMESPACES")?y({},l.ALLOWED_NAMESPACES,A):bi,Me=U(l,"ADD_URI_SAFE_ATTR")?y(V(vt),l.ADD_URI_SAFE_ATTR,B):vt,pt=U(l,"ADD_DATA_URI_TAGS")?y(V(gt),l.ADD_DATA_URI_TAGS,B):gt,ie=U(l,"FORBID_CONTENTS")?y({},l.FORBID_CONTENTS,B):ft,ue=U(l,"FORBID_TAGS")?y({},l.FORBID_TAGS,B):{},De=U(l,"FORBID_ATTR")?y({},l.FORBID_ATTR,B):{},te=!!U(l,"USE_PROFILES")&&l.USE_PROFILES,lt=l.ALLOW_ARIA_ATTR!==!1,Oe=l.ALLOW_DATA_ATTR!==!1,ct=l.ALLOW_UNKNOWN_PROTOCOLS||!1,dt=l.ALLOW_SELF_CLOSE_IN_ATTR!==!1,Z=l.SAFE_FOR_TEMPLATES||!1,Pe=l.SAFE_FOR_XML!==!1,Q=l.WHOLE_DOCUMENT||!1,ee=l.RETURN_DOM||!1,me=l.RETURN_DOM_FRAGMENT||!1,be=l.RETURN_TRUSTED_TYPE||!1,_e=l.FORCE_BODY||!1,ut=l.SANITIZE_DOM!==!1,ht=l.SANITIZE_NAMED_PROPS||!1,He=l.KEEP_CONTENT!==!1,he=l.IN_PLACE||!1,ot=l.ALLOWED_URI_REGEXP||$e,re=l.NAMESPACE||K,we=l.MATHML_TEXT_INTEGRATION_POINTS||we,xe=l.HTML_INTEGRATION_POINTS||xe,R=l.CUSTOM_ELEMENT_HANDLING||{},l.CUSTOM_ELEMENT_HANDLING&&mt(l.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(R.tagNameCheck=l.CUSTOM_ELEMENT_HANDLING.tagNameCheck),l.CUSTOM_ELEMENT_HANDLING&&mt(l.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(R.attributeNameCheck=l.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),l.CUSTOM_ELEMENT_HANDLING&&typeof l.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(R.allowCustomizedBuiltInElements=l.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Z&&(Oe=!1),me&&(ee=!0),te&&(L=y({},Qe),D=[],te.html===!0&&(y(L,Ve),y(D,Je)),te.svg===!0&&(y(L,Ce),y(D,Se),y(D,pe)),te.svgFilters===!0&&(y(L,Ee),y(D,Se),y(D,pe)),te.mathMl===!0&&(y(L,Ae),y(D,Ze),y(D,pe))),l.ADD_TAGS&&(L===nt&&(L=V(L)),y(L,l.ADD_TAGS,B)),l.ADD_ATTR&&(D===at&&(D=V(D)),y(D,l.ADD_ATTR,B)),l.ADD_URI_SAFE_ATTR&&y(Me,l.ADD_URI_SAFE_ATTR,B),l.FORBID_CONTENTS&&(ie===ft&&(ie=V(ie)),y(ie,l.FORBID_CONTENTS,B)),He&&(L["#text"]=!0),Q&&y(L,["html","head","body"]),L.table&&(y(L,["tbody"]),delete ue.tbody),l.TRUSTED_TYPES_POLICY){if(typeof l.TRUSTED_TYPES_POLICY.createHTML!="function")throw oe('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof l.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw oe('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');P=l.TRUSTED_TYPES_POLICY,de=P.createHTML("")}else P===void 0&&(P=function(v,p){if(typeof v!="object"||typeof v.createPolicy!="function")return null;let w=null;const C="data-tt-policy-suffix";p&&p.hasAttribute(C)&&(w=p.getAttribute(C));const k="dompurify"+(w?"#"+w:"");try{return v.createPolicy(k,{createHTML:N=>N,createScriptURL:N=>N})}catch(N){return console.warn("TrustedTypes policy "+k+" could not be created."),null}}(ge,ae)),P!==null&&typeof de=="string"&&(de=P.createHTML(""));o&&o(l),se=l}},bt=y({},[...Ce,...Ee,...Mt]),yt=y({},[...Ae,...Ut]),z=function(l){m(b.removed,{element:l});try{ve(l).removeChild(l)}catch(v){ai(l)}},Te=function(l,v){try{m(b.removed,{attribute:v.getAttributeNode(l),from:v})}catch(p){m(b.removed,{attribute:null,from:v})}if(v.removeAttribute(l),l==="is")if(ee||me)try{z(v)}catch(p){}else try{v.setAttribute(l,"")}catch(p){}},kt=function(l){let v=null,p=null;if(_e)l="<remove></remove>"+l;else{const k=J(l,/^[\r\n\t ]+/);p=k&&k[0]}fe==="application/xhtml+xml"&&re===K&&(l='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+l+"</body></html>");const w=P?P.createHTML(l):l;if(re===K)try{v=new oi().parseFromString(w,fe)}catch(k){}if(!v||!v.documentElement){v=Ne.createDocument(re,"template",null);try{v.documentElement.innerHTML=Ue?de:w}catch(k){}}const C=v.body||v.documentElement;return l&&p&&C.insertBefore(S.createTextNode(p),C.childNodes[0]||null),re===K?hi.call(v,Q?"html":"body")[0]:Q?v.documentElement:C},wt=function(l){return di.call(l.ownerDocument||l,l,le.SHOW_ELEMENT|le.SHOW_COMMENT|le.SHOW_TEXT|le.SHOW_PROCESSING_INSTRUCTION|le.SHOW_CDATA_SECTION,null)},qe=function(l){return l instanceof si&&(typeof l.nodeName!="string"||typeof l.textContent!="string"||typeof l.removeChild!="function"||!(l.attributes instanceof ri)||typeof l.removeAttribute!="function"||typeof l.setAttribute!="function"||typeof l.namespaceURI!="string"||typeof l.insertBefore!="function"||typeof l.hasChildNodes!="function")},xt=function(l){return typeof Re=="function"&&l instanceof Re};function G(l,v,p){h(l,w=>{w.call(b,v,p,se)})}const Tt=function(l){let v=null;if(G(M.beforeSanitizeElements,l,null),qe(l))return z(l),!0;const p=B(l.nodeName);if(G(M.uponSanitizeElement,l,{tagName:p,allowedTags:L}),l.hasChildNodes()&&!xt(l.firstElementChild)&&O(/<[/\w]/g,l.innerHTML)&&O(/<[/\w]/g,l.textContent)||l.nodeType===Jt||Pe&&l.nodeType===Zt&&O(/<[/\w]/g,l.data))return z(l),!0;if(!L[p]||ue[p]){if(!ue[p]&&Et(p)&&(R.tagNameCheck instanceof RegExp&&O(R.tagNameCheck,p)||R.tagNameCheck instanceof Function&&R.tagNameCheck(p)))return!1;if(He&&!ie[p]){const w=ve(l)||l.parentNode,C=ci(l)||l.childNodes;if(C&&w)for(let k=C.length-1;k>=0;--k){const N=ni(C[k],!0);N.__removalCount=(l.__removalCount||0)+1,w.insertBefore(N,li(l))}}return z(l),!0}return l instanceof rt&&!function(w){let C=ve(w);C&&C.tagName||(C={namespaceURI:re,tagName:"template"});const k=E(w.tagName),N=E(C.tagName);return!!ze[w.namespaceURI]&&(w.namespaceURI===ke?C.namespaceURI===K?k==="svg":C.namespaceURI===ye?k==="svg"&&(N==="annotation-xml"||we[N]):!!bt[k]:w.namespaceURI===ye?C.namespaceURI===K?k==="math":C.namespaceURI===ke?k==="math"&&xe[N]:!!yt[k]:w.namespaceURI===K?!(C.namespaceURI===ke&&!xe[N])&&!(C.namespaceURI===ye&&!we[N])&&!yt[k]&&(yi[k]||!bt[k]):!(fe!=="application/xhtml+xml"||!ze[w.namespaceURI]))}(l)?(z(l),!0):p!=="noscript"&&p!=="noembed"&&p!=="noframes"||!O(/<\/no(script|embed|frames)/i,l.innerHTML)?(Z&&l.nodeType===Qt&&(v=l.textContent,h([Ie,Le,Be],w=>{v=j(v,w," ")}),l.textContent!==v&&(m(b.removed,{element:l.cloneNode()}),l.textContent=v)),G(M.afterSanitizeElements,l,null),!1):(z(l),!0)},Ct=function(l,v,p){if(ut&&(v==="id"||v==="name")&&(p in S||p in wi))return!1;if(!(Oe&&!De[v]&&O(pi,v))){if(!(lt&&O(gi,v))){if(!D[v]||De[v]){if(!(Et(l)&&(R.tagNameCheck instanceof RegExp&&O(R.tagNameCheck,l)||R.tagNameCheck instanceof Function&&R.tagNameCheck(l))&&(R.attributeNameCheck instanceof RegExp&&O(R.attributeNameCheck,v)||R.attributeNameCheck instanceof Function&&R.attributeNameCheck(v))||v==="is"&&R.allowCustomizedBuiltInElements&&(R.tagNameCheck instanceof RegExp&&O(R.tagNameCheck,p)||R.tagNameCheck instanceof Function&&R.tagNameCheck(p))))return!1}else if(!Me[v]){if(!O(ot,j(p,st,""))){if((v!=="src"&&v!=="xlink:href"&&v!=="href"||l==="script"||X(p,"data:")!==0||!pt[l])&&!(ct&&!O(vi,j(p,st,"")))){if(p)return!1}}}}}return!0},Et=function(l){return l!=="annotation-xml"&&J(l,mi)},At=function(l){G(M.beforeSanitizeAttributes,l,null);const{attributes:v}=l;if(!v||qe(l))return;const p={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:D,forceKeepAttr:void 0};let w=v.length;for(;w--;){const C=v[w],{name:k,namespaceURI:N,value:Y}=C,W=B(k);let F=k==="value"?Y:q(Y);if(p.attrName=W,p.attrValue=F,p.keepAttr=!0,p.forceKeepAttr=void 0,G(M.uponSanitizeAttribute,l,p),F=p.attrValue,!ht||W!=="id"&&W!=="name"||(Te(k,l),F="user-content-"+F),Pe&&O(/((--!?|])>)|<\/(style|title)/i,F)){Te(k,l);continue}if(p.forceKeepAttr||(Te(k,l),!p.keepAttr))continue;if(!dt&&O(/\/>/i,F)){Te(k,l);continue}Z&&h([Ie,Le,Be],Rt=>{F=j(F,Rt," ")});const St=B(l.nodeName);if(Ct(St,W,F)){if(P&&typeof ge=="object"&&typeof ge.getAttributeType=="function"&&!N)switch(ge.getAttributeType(St,W)){case"TrustedHTML":F=P.createHTML(F);break;case"TrustedScriptURL":F=P.createScriptURL(F)}try{N?l.setAttributeNS(N,k,F):l.setAttribute(k,F),qe(l)?z(l):g(b.removed)}catch(Rt){}}}G(M.afterSanitizeAttributes,l,null)},xi=function l(v){let p=null;const w=wt(v);for(G(M.beforeSanitizeShadowDOM,v,null);p=w.nextNode();)G(M.uponSanitizeShadowNode,p,null),Tt(p),At(p),p.content instanceof it&&l(p.content);G(M.afterSanitizeShadowDOM,v,null)};return b.sanitize=function(l){let v=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},p=null,w=null,C=null,k=null;if(Ue=!l,Ue&&(l="<!-->"),typeof l!="string"&&!xt(l)){if(typeof l.toString!="function")throw oe("toString is not a function");if(typeof(l=l.toString())!="string")throw oe("dirty is not a string, aborting")}if(!b.isSupported)return l;if(Fe||We(v),b.removed=[],typeof l=="string"&&(he=!1),he){if(l.nodeName){const W=B(l.nodeName);if(!L[W]||ue[W])throw oe("root node is forbidden and cannot be sanitized in-place")}}else if(l instanceof Re)p=kt("<!---->"),w=p.ownerDocument.importNode(l,!0),w.nodeType===Vt&&w.nodeName==="BODY"||w.nodeName==="HTML"?p=w:p.appendChild(w);else{if(!ee&&!Z&&!Q&&l.indexOf("<")===-1)return P&&be?P.createHTML(l):l;if(p=kt(l),!p)return ee?null:be?de:""}p&&_e&&z(p.firstChild);const N=wt(he?l:p);for(;C=N.nextNode();)Tt(C),At(C),C.content instanceof it&&xi(C.content);if(he)return l;if(ee){if(me)for(k=ui.call(p.ownerDocument);p.firstChild;)k.appendChild(p.firstChild);else k=p;return(D.shadowroot||D.shadowrootmode)&&(k=fi.call(_,k,!0)),k}let Y=Q?p.outerHTML:p.innerHTML;return Q&&L["!doctype"]&&p.ownerDocument&&p.ownerDocument.doctype&&p.ownerDocument.doctype.name&&O(et,p.ownerDocument.doctype.name)&&(Y="<!DOCTYPE "+p.ownerDocument.doctype.name+`>
`+Y),Z&&h([Ie,Le,Be],W=>{Y=j(Y,W," ")}),P&&be?P.createHTML(Y):Y},b.setConfig=function(){We(arguments.length>0&&arguments[0]!==void 0?arguments[0]:{}),Fe=!0},b.clearConfig=function(){se=null,Fe=!1},b.isValidAttribute=function(l,v,p){se||We({});const w=B(l),C=B(v);return Ct(w,C,p)},b.addHook=function(l,v){typeof v=="function"&&m(M[l],v)},b.removeHook=function(l){return g(M[l])},b.removeHooks=function(l){M[l]=[]},b.removeAllHooks=function(){M={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},b}();return ti})}});(function(r){"use strict";Function.prototype.bind||(Function.prototype.bind=function(e){var t=this;return function(){return t.apply(e)}});var d=0;r.fn.redactor=function(e){var t=[],i=Array.prototype.slice.call(arguments,1);return typeof e=="string"?this.each(function(){var s=r.data(this,"redactor"),o;if(e.search(/\./)!=="-1"?(o=e.split("."),typeof s[o[0]]!="undefined"&&(o=s[o[0]][o[1]])):o=s[e],typeof s!="undefined"&&r.isFunction(o)){var n=o.apply(s,i);n!==void 0&&n!==s&&t.push(n)}else r.error('No such method "'+e+'" for Redactor')}):this.each(function(){r.data(this,"redactor",{}),r.data(this,"redactor",f(this,e))}),t.length===0?this:t.length===1?t[0]:t};function f(e,t){return new f.prototype.init(e,t)}r.Redactor=f,r.Redactor.VERSION="2.13",r.Redactor.modules=["air","autosave","block","buffer","build","button","caret","clean","code","core","detect","dropdown","events","file","focus","image","indent","inline","insert","keydown","keyup","lang","line","link","linkify","list","marker","modal","observe","offset","paragraphize","paste","placeholder","progress","selection","shortcuts","storage","toolbar","upload","uploads3","utils","browser"],r.Redactor.settings={},r.Redactor.opts={animation:!1,lang:"en",direction:"ltr",spellcheck:!0,overrideStyles:!0,stylesClass:!1,scrollTarget:document,focus:!1,focusEnd:!1,clickToEdit:!1,structure:!1,tabindex:!1,minHeight:!1,maxHeight:!1,maxWidth:!1,plugins:!1,callbacks:{},placeholder:!1,linkify:!0,enterKey:!0,pastePlainText:!1,pasteImages:!0,pasteLinks:!0,pasteBlockTags:["pre","h1","h2","h3","h4","h5","h6","table","tbody","thead","tfoot","th","tr","td","ul","ol","li","blockquote","p","figure","figcaption"],pasteInlineTags:["br","strong","ins","code","del","span","samp","kbd","sup","sub","abbr","mark","var","cite","small","b","u","em","i"],preClass:!1,preSpaces:4,tabAsSpaces:!1,tabKey:!0,autosave:!1,autosaveName:!1,autosaveFields:!1,imageUpload:null,imageUploadParam:"file",imageUploadFields:!1,imageUploadForms:!1,imageTag:"figure",imageEditable:!0,imageCaption:!0,imagePosition:!1,imageResizable:!1,imageFloatMargin:"10px",dragImageUpload:!0,multipleImageUpload:!0,clipboardImageUpload:!0,fileUpload:null,fileUploadParam:"file",fileUploadFields:!1,fileUploadForms:!1,dragFileUpload:!0,s3:!1,linkNewTab:!1,linkNewTabHide:!1,linkTooltip:!0,linkNofollow:!1,linkSize:30,linkValidation:!0,pasteLinkTarget:!1,videoContainerClass:"video-container",toolbar:!0,toolbarFixed:!0,toolbarFixedTarget:document,toolbarFixedTopOffset:0,toolbarExternal:!1,toolbarOverflow:!1,air:!1,airWidth:!1,formatting:["p","blockquote","pre","h1","h2","h3","h4","h5","h6"],formattingAdd:!1,buttons:["format","bold","italic","deleted","lists","image","file","link","horizontalrule"],buttonsTextLabeled:!1,buttonsHide:[],buttonsHideOnMobile:[],script:!0,removeNewlines:!1,removeComments:!0,replaceTags:{b:"strong",i:"em",strike:"del"},keepStyleAttr:[],keepInlineOnEnter:!1,shortcuts:{"ctrl+shift+m, meta+shift+m":{func:"inline.removeFormat"},"ctrl+b, meta+b":{func:"inline.format",params:["bold"]},"ctrl+i, meta+i":{func:"inline.format",params:["italic"]},"ctrl+h, meta+h":{func:"inline.format",params:["superscript"]},"ctrl+l, meta+l":{func:"inline.format",params:["subscript"]},"ctrl+k, meta+k":{func:"link.show"},"ctrl+shift+7":{func:"list.toggle",params:["orderedlist"]},"ctrl+shift+8":{func:"list.toggle",params:["unorderedlist"]}},shortcutsAdd:!1,activeButtons:["deleted","italic","bold"],activeButtonsStates:{b:"bold",strong:"bold",i:"italic",em:"italic",del:"deleted",strike:"deleted"},langs:{en:{format:"Format",image:"Image",file:"File",link:"Link",bold:"Bold",italic:"Italic",deleted:"Strikethrough",underline:"Underline","bold-abbr":"B","italic-abbr":"I","deleted-abbr":"S","underline-abbr":"U",lists:"Lists","link-insert":"Insert link","link-edit":"Edit link","link-in-new-tab":"Open link in new tab",unlink:"Unlink",cancel:"Cancel",close:"Close",insert:"Insert",save:"Save",delete:"Delete",text:"Text",edit:"Edit",title:"Title",paragraph:"Normal text",quote:"Quote",code:"Code",heading1:"Heading 1",heading2:"Heading 2",heading3:"Heading 3",heading4:"Heading 4",heading5:"Heading 5",heading6:"Heading 6",filename:"Name",optional:"optional",unorderedlist:"Unordered List",orderedlist:"Ordered List",outdent:"Outdent",indent:"Indent",horizontalrule:"Line","upload-label":"Drop file here or ",caption:"Caption",bulletslist:"Bullets",numberslist:"Numbers","image-position":"Position",none:"None",left:"Left",right:"Right",center:"Center","accessibility-help-label":"Rich text editor"}},type:"textarea",inline:!1,inlineTags:["a","span","strong","strike","b","u","em","i","code","del","ins","samp","kbd","sup","sub","abbr","mark","var","cite","small"],blockTags:["pre","ul","ol","li","p","h1","h2","h3","h4","h5","h6","dl","dt","dd","div","td","blockquote","output","figcaption","figure","address","section","header","footer","aside","article","iframe"],paragraphize:!0,paragraphizeBlocks:["table","div","pre","form","ul","ol","h1","h2","h3","h4","h5","h6","dl","blockquote","figcaption","address","section","header","footer","aside","article","object","style","script","iframe","select","input","textarea","button","option","map","area","math","hr","fieldset","legend","hgroup","nav","figure","details","menu","summary","p"],emptyHtml:"<p>&#x200b;</p>",invisibleSpace:"&#x200b;",emptyHtmlRendered:r("").html("\u200B").html(),imageTypes:["image/png","image/jpeg","image/gif"],userAgent:navigator.userAgent.toLowerCase(),observe:{dropdowns:[]},regexps:{linkyoutube:/https?:\/\/(?:[0-9A-Z-]+\.)?(?:youtu\.be\/|youtube\.com\S*[^\w\-\s])([\w\-]{11})(?=[^\w\-]|$)(?![?=&+%\w.\-]*(?:['"][^<>]*>|<\/a>))[?=&+%\w.-]*/ig,linkvimeo:/https?:\/\/(www\.)?vimeo.com\/(\d+)($|\/)/,linkimage:/((https?|www)[^\s]+\.)(jpe?g|png|gif)(\?[^\s-]+)?/ig,url:/(https?:\/\/(?:www\.|(?!www))[^\s\.]+\.[^\s]{2,}|www\.[^\s]+\.[^\s]{2,})/ig}},f.fn=r.Redactor.prototype={keyCode:{BACKSPACE:8,DELETE:46,UP:38,DOWN:40,ENTER:13,SPACE:32,ESC:27,TAB:9,CTRL:17,META:91,SHIFT:16,ALT:18,RIGHT:39,LEFT:37,LEFT_WIN:91},init:function(e,t){if(this.$element=r(e),this.uuid=d++,this.sBuffer=[],this.sRebuffer=[],this.loadOptions(t),this.loadModules(),this.opts.clickToEdit&&!this.$element.hasClass("redactor-click-to-edit"))return this.loadToEdit(t);this.$element.hasClass("redactor-click-to-edit")&&this.$element.removeClass("redactor-click-to-edit"),this.reIsBlock=new RegExp("^("+this.opts.blockTags.join("|").toUpperCase()+")$","i"),this.reIsInline=new RegExp("^("+this.opts.inlineTags.join("|").toUpperCase()+")$","i"),this.opts.dragImageUpload=this.opts.imageUpload===null?!1:this.opts.dragImageUpload,this.opts.dragFileUpload=this.opts.fileUpload===null?!1:this.opts.dragFileUpload,this.formatting={},this.lang.load(),r.extend(this.opts.shortcuts,this.opts.shortcutsAdd),this.$editor=this.$element,this.detectType(),this.core.callback("start"),this.core.callback("startToEdit"),this.start=!0,this.build.start()},detectType:function(){this.build.isInline()||this.opts.inline?this.opts.type="inline":this.build.isTag("DIV")?this.opts.type="div":this.build.isTag("PRE")&&(this.opts.type="pre")},loadToEdit:function(e){this.$element.on("click.redactor-click-to-edit",r.proxy(function(){this.initToEdit(e)},this)),this.$element.addClass("redactor-click-to-edit")},initToEdit:function(e){r.extend(e.callbacks,{startToEdit:function(){this.insert.node(this.marker.get(),!1)},initToEdit:function(){this.selection.restore(),this.clickToCancelStorage=this.code.get(),r(this.opts.clickToCancel).off(".redactor-click-to-edit"),r(this.opts.clickToCancel).show().on("click.redactor-click-to-edit",r.proxy(function(t){t.preventDefault(),this.core.destroy(),this.events.syncFire=!1,this.$element.html(this.clickToCancelStorage),this.core.callback("cancel",this.clickToCancelStorage),this.events.syncFire=!0,this.clickToCancelStorage="",r(this.opts.clickToCancel).hide(),r(this.opts.clickToSave).hide(),this.$element.on("click.redactor-click-to-edit",r.proxy(function(){this.initToEdit(e)},this)),this.$element.addClass("redactor-click-to-edit")},this)),r(this.opts.clickToSave).off(".redactor-click-to-edit"),r(this.opts.clickToSave).show().on("click.redactor-click-to-edit",r.proxy(function(t){t.preventDefault(),this.core.destroy(),this.core.callback("save",this.code.get()),r(this.opts.clickToCancel).hide(),r(this.opts.clickToSave).hide(),this.$element.on("click.redactor-click-to-edit",r.proxy(function(){this.initToEdit(e)},this)),this.$element.addClass("redactor-click-to-edit")},this))}}),this.$element.redactor(e),this.$element.off(".redactor-click-to-edit")},loadOptions:function(e){var t={};typeof r.Redactor.settings.namespace!="undefined"?this.$element.hasClass(r.Redactor.settings.namespace)&&(t=r.Redactor.settings):t=r.Redactor.settings,this.opts=r.extend({},r.Redactor.opts,this.$element.data(),e),this.opts=r.extend({},this.opts,t)},getModuleMethods:function(e){return Object.getOwnPropertyNames(e).filter(function(t){return typeof e[t]=="function"})},loadModules:function(){for(var e=r.Redactor.modules.length,t=0;t<e;t++)this.bindModuleMethods(r.Redactor.modules[t])},bindModuleMethods:function(e){if(typeof this[e]!="undefined"){this[e]=this[e]();for(var t=this.getModuleMethods(this[e]),i=t.length,s=0;s<i;s++)this[e][t[s]]=this[e][t[s]].bind(this)}},air:function(){return{enabled:!1,collapsed:function(){this.opts.air&&this.selection.get().collapseToStart()},collapsedEnd:function(){this.opts.air&&this.selection.get().collapseToEnd()},build:function(){this.detect.isMobile()||(this.button.hideButtons(),this.button.hideButtonsOnMobile(),this.opts.buttons.length!==0&&(this.$air=this.air.createContainer(),this.opts.airWidth!==!1&&this.$air.css("width",this.opts.airWidth),this.air.append(),this.button.$toolbar=this.$air,this.button.setFormatting(),this.button.load(this.$air),this.core.editor().on("mouseup.redactor",this,r.proxy(function(e){this.selection.text()!==""&&this.air.show(e)},this))))},append:function(){this.$air.appendTo("body")},createContainer:function(){return r("<ul>").addClass("redactor-air").attr({id:"redactor-air-"+this.uuid,role:"toolbar"}).hide()},show:function(e){this.selection.saveInstant(),r(".redactor-air").hide();var t=0,i=this.$air.innerWidth();r(window).width()<e.clientX+i&&(t=200),this.$air.css({left:e.clientX-t+"px",top:e.clientY+10+r(document).scrollTop()+"px"}).show(),this.air.enabled=!0,this.air.bindHide()},bindHide:function(){r(document).on("mousedown.redactor-air."+this.uuid,r.proxy(function(e){var t=r(e.target).closest(".redactor-dropdown").length;if(r(e.target).closest(this.$air).length===0&&t===0){var i=this.air.hide(e);i!==!1&&this.marker.remove()}},this)).on("keydown.redactor-air."+this.uuid,r.proxy(function(e){var t=e.which;if(!(!this.utils.isRedactorParent(e.target)&&!r(e.target).hasClass("redactor-in")||r(e.target).closest("#redactor-modal").length!==0)){if(t===this.keyCode.ESC)this.selection.get().collapseToStart();else if(t===this.keyCode.BACKSPACE||t===this.keyCode.DELETE){var i=this.selection.get(),s=this.selection.range(i);s.deleteContents()}else t===this.keyCode.ENTER&&this.selection.get().collapseToEnd();this.air.enabled?this.air.hide(e):this.selection.get().collapseToStart()}},this))},hide:function(e){var t=e.ctrlKey||e.metaKey||e.shiftKey&&e.altKey;if(t)return!1;this.button.setInactiveAll(),this.$air.fadeOut(100),this.air.enabled=!1,r(document).off("mousedown.redactor-air."+this.uuid),r(document).off("keydown.redactor-air."+this.uuid)}}},autosave:function(){return{enabled:!1,html:!1,init:function(){this.opts.autosave&&(this.autosave.enabled=!0,this.autosave.name=this.opts.autosaveName?this.opts.autosaveName:this.$textarea.attr("name"))},is:function(){return this.autosave.enabled},send:function(){if(this.opts.autosave&&(this.autosave.source=this.code.get(),this.autosave.html!==this.autosave.source)){var e={};e.name=this.autosave.name,e[this.autosave.name]=this.autosave.source,e=this.autosave.getHiddenFields(e);var t=r.ajax({url:this.opts.autosave,type:"post",data:e});t.done(this.autosave.success)}},getHiddenFields:function(e){return this.opts.autosaveFields===!1||typeof this.opts.autosaveFields!="object"||r.each(this.opts.autosaveFields,r.proxy(function(t,i){i!==null&&i.toString().indexOf("#")===0&&(i=r(i).val()),e[t]=i},this)),e},success:function(e){var t;try{t=JSON.parse(e)}catch(s){t=e}var i=typeof t.error=="undefined"?"autosave":"autosaveError";this.core.callback(i,this.autosave.name,t),this.autosave.html=this.autosave.source},disable:function(){this.autosave.enabled=!1,clearInterval(this.autosaveTimeout)}}},block:function(){return{format:function(e,t,i,s){if(e=e==="quote"?"blockquote":e,this.block.tags=["p","blockquote","pre","h1","h2","h3","h4","h5","h6","div","figure"],r.inArray(e,this.block.tags)!==-1)return e==="p"&&typeof t=="undefined"&&(t="class"),this.placeholder.hide(),this.buffer.set(),this.utils.isCollapsed()?this.block.formatCollapsed(e,t,i,s):this.block.formatUncollapsed(e,t,i,s)},formatCollapsed:function(e,t,i,s){this.selection.save();var o=this.selection.block(),n=o.tagName.toLowerCase();if(r.inArray(n,this.block.tags)===-1){this.selection.restore();return}var a=!1;n===e&&t===void 0&&(e="p",a=!0),a&&(this.block.removeAllClass(),this.block.removeAllAttr());var c;if(n==="blockquote"&&this.utils.isEndOfElement(o)){this.marker.remove(),c=document.createElement("p"),c.innerHTML=this.opts.invisibleSpace,r(o).after(c),this.caret.start(c);var u=r(o).children().last();u.length!==0&&u[0].tagName==="BR"&&u.remove()}else c=this.utils.replaceToTag(o,e);if(typeof t=="object"){s=i;for(var h in t)c=this.block.setAttr(c,h,t[h],s)}else c=this.block.setAttr(c,t,i,s);return e==="pre"&&c.length===1&&r(c).html(r.trim(r(c).html())),this.selection.restore(),this.block.removeInlineTags(c),c},formatUncollapsed:function(e,t,i,s){this.selection.save();var o=[],n=this.selection.blocks();n[0]&&(r(n[0]).hasClass("redactor-in")||r(n[0]).hasClass("redactor-box"))&&(n=this.core.editor().find(this.opts.blockTags.join(", ")));for(var a=n.length,c=0;c<a;c++){var u=n[c].tagName.toLowerCase();if(r.inArray(u,this.block.tags)!==-1&&u!=="figure"){var h=this.utils.replaceToTag(n[c],e);if(typeof t=="object"){s=i;for(var g in t)h=this.block.setAttr(h,g,t[g],s)}else h=this.block.setAttr(h,t,i,s);o.push(h),this.block.removeInlineTags(h)}}if(this.selection.restore(),e==="pre"&&o.length!==0){var m=o[0];r.each(o,function(E,A){E!==0&&(r(m).append(`
`+r.trim(A.html())),r(A).remove())}),o=[],o.push(m)}return o},removeInlineTags:function(e){e=e[0]||e;var t=this.opts.inlineTags,i=["PRE","H1","H2","H3","H4","H5","H6"];if(r.inArray(e.tagName,i)!==-1){if(e.tagName!=="PRE"){var s=t.indexOf("a");t.splice(s,1)}r(e).find(t.join(",")).not(".redactor-selection-marker").contents().unwrap()}},setAttr:function(e,t,i,s){if(typeof t=="undefined")return e;var o=typeof s=="undefined"?"replace":s;return t==="class"?e=this.block[o+"Class"](i,e):o==="remove"?e=this.block[o+"Attr"](t,e):o==="removeAll"?e=this.block[o+"Attr"](t,e):e=this.block[o+"Attr"](t,i,e),e},getBlocks:function(e){if(e=typeof e=="undefined"?this.selection.blocks():e,r(e).hasClass("redactor-box")){var t=[],i=this.core.editor().children();return r.each(i,r.proxy(function(s,o){this.utils.isBlock(o)&&t.push(o)},this)),t}return e},replaceClass:function(e,t){return r(this.block.getBlocks(t)).removeAttr("class").addClass(e)[0]},toggleClass:function(e,t){return r(this.block.getBlocks(t)).toggleClass(e)[0]},addClass:function(e,t){return r(this.block.getBlocks(t)).addClass(e)[0]},removeClass:function(e,t){return r(this.block.getBlocks(t)).removeClass(e)[0]},removeAllClass:function(e){return r(this.block.getBlocks(e)).removeAttr("class")[0]},replaceAttr:function(e,t,i){return i=this.block.removeAttr(e,i),r(i).attr(e,t)[0]},toggleAttr:function(e,t,i){i=this.block.getBlocks(i);var s=this,o=[];return r.each(i,function(n,a){var c=r(a);c.attr(e)?o.push(s.block.removeAttr(e,a)):o.push(s.block.addAttr(e,t,a))}),o},addAttr:function(e,t,i){return r(this.block.getBlocks(i)).attr(e,t)[0]},removeAttr:function(e,t){return r(this.block.getBlocks(t)).removeAttr(e)[0]},removeAllAttr:function(e){e=this.block.getBlocks(e);var t=[];return r.each(e,function(i,s){if(typeof s.attributes!="undefined")for(;s.attributes.length;)s.removeAttribute(s.attributes[0].name);t.push(s)}),t}}},buffer:function(){return{set:function(e){typeof e=="undefined"&&this.buffer.clear(),typeof e=="undefined"||e==="undo"?this.buffer.setUndo():this.buffer.setRedo()},setUndo:function(){var e=this.selection.saveInstant(),t=this.sBuffer[this.sBuffer.length-1],i=this.core.editor().html(),s=!(typeof t!="undefined"&&t[0]===i);s&&this.sBuffer.push([i,e])},setRedo:function(){var e=this.selection.saveInstant();this.sRebuffer.push([this.core.editor().html(),e])},add:function(){this.sBuffer.push([this.core.editor().html(),0])},undo:function(){if(this.sBuffer.length!==0){var e=this.sBuffer.pop();this.buffer.set("redo"),this.core.editor().html(e[0]),this.selection.restoreInstant(e[1]),this.selection.restore(),this.observe.load()}},redo:function(){if(this.sRebuffer.length!==0){var e=this.sRebuffer.pop();this.buffer.set("undo"),this.core.editor().html(e[0]),this.selection.restoreInstant(e[1]),this.selection.restore(),this.observe.load()}},clear:function(){this.sRebuffer=[]}}},build:function(){return{start:function(){if(this.opts.type==="inline")this.opts.type="inline";else if(this.opts.type==="div"){var e=r.trim(this.$editor.html());e===""&&this.$editor.html(this.opts.emptyHtml),this.build.buildTextarea()}else this.opts.type==="textarea"&&this.build.startTextarea();this.build.setIn(),this.build.setId(),this.build.enableEditor(),this.build.setOptions(),this.build.callEditor()},createContainerBox:function(){this.$box=r('<div class="redactor-box" role="application" />')},setIn:function(){this.core.editor().addClass("redactor-in")},setId:function(){var e=this.opts.type==="textarea"?"redactor-uuid-"+this.uuid:this.$element.attr("id");this.core.editor().attr("id",typeof e=="undefined"?"redactor-uuid-"+this.uuid:e)},getName:function(){var e=this.$element.attr("name");return typeof e=="undefined"?"content-"+this.uuid:e},buildTextarea:function(){this.$textarea=r("<textarea>"),this.$textarea.attr("name",this.build.getName()),this.$textarea.hide(),this.$element.after(this.$textarea),this.build.setStartAttrs()},loadFromTextarea:function(){this.$editor=r("<div />"),this.$textarea=this.$element,this.$element.attr("name",this.build.getName()),this.$box.insertAfter(this.$element).append(this.$editor).append(this.$element),this.build.setStartAttrs(),this.$editor.addClass("redactor-layer"),this.opts.overrideStyles&&this.$editor.addClass("redactor-styles"),this.$element.hide(),this.$box.prepend('<span class="redactor-voice-label" id="redactor-voice-'+this.uuid+'" aria-hidden="false">'+this.lang.get("accessibility-help-label")+"</span>")},setStartAttrs:function(){this.$editor.attr({"aria-labelledby":"redactor-voice-"+this.uuid,role:"presentation"})},startTextarea:function(){this.build.createContainerBox(),this.build.loadFromTextarea(),this.code.start(this.core.textarea().val()),this.core.textarea().val(this.clean.onSync(this.$editor.html()))},isTag:function(e){return this.$element[0].tagName===e},isInline:function(){return!this.build.isTag("TEXTAREA")&&!this.build.isTag("DIV")&&!this.build.isTag("PRE")},enableEditor:function(){this.core.editor().attr({contenteditable:!0})},setOptions:function(){this.opts.type==="inline"&&(this.opts.enterKey=!1),(this.opts.type==="inline"||this.opts.type==="pre")&&(this.opts.toolbarMobile=!1,this.opts.toolbar=!1,this.opts.air=!1,this.opts.linkify=!1),this.core.editor().attr("spellcheck",this.opts.spellcheck),this.opts.structure&&this.core.editor().addClass("redactor-structure"),this.opts.stylesClass&&this.core.editor().addClass(this.opts.stylesClass),this.opts.type==="textarea"&&(this.core.box().attr("dir",this.opts.direction),this.core.editor().attr("dir",this.opts.direction),this.opts.tabindex&&this.core.editor().attr("tabindex",this.opts.tabindex),this.opts.minHeight?this.core.editor().css("min-height",this.opts.minHeight):this.core.editor().css("min-height","40px"),this.opts.maxHeight&&this.core.editor().css("max-height",this.opts.maxHeight),this.opts.maxWidth&&this.core.editor().css({"max-width":this.opts.maxWidth,margin:"auto"}))},callEditor:function(){this.build.disableBrowsersEditing(),this.events.init(),this.build.setHelpers(),(this.opts.toolbar||this.opts.air)&&(this.toolbarsButtons=this.button.init()),this.opts.air?this.air.build():this.opts.toolbar&&this.toolbar.build(),this.detect.isMobile()&&this.opts.toolbarMobile&&this.opts.air&&(this.opts.toolbar=!0,this.toolbar.build()),(this.opts.air||this.opts.toolbar)&&(this.core.editor().on("mouseup.redactor-observe."+this.uuid+" keyup.redactor-observe."+this.uuid+" focus.redactor-observe."+this.uuid+" touchstart.redactor-observe."+this.uuid,r.proxy(this.observe.toolbar,this)),this.core.element().on("blur.callback.redactor",r.proxy(function(){this.button.setInactiveAll()},this))),this.modal.templates(),this.build.plugins(),this.autosave.init(),this.code.html=this.code.cleaned(this.core.editor().html()),this.core.callback("init"),this.core.callback("initToEdit"),this.storage.observe(),this.start=!1},setHelpers:function(){this.opts.linkify&&this.linkify.format(),this.placeholder.init(),this.opts.focus?setTimeout(this.focus.start,100):this.opts.focusEnd&&setTimeout(this.focus.end,100)},disableBrowsersEditing:function(){try{document.execCommand("enableObjectResizing",!1,!1),document.execCommand("enableInlineTableEditing",!1,!1),document.execCommand("AutoUrlDetect",!1,!1)}catch(e){}},plugins:function(){this.opts.plugins&&r.each(this.opts.plugins,r.proxy(function(e,t){var i=typeof RedactorPlugins!="undefined"&&typeof RedactorPlugins[t]!="undefined"?RedactorPlugins:f.fn;if(r.isFunction(i[t])){this[t]=i[t]();for(var s=this.getModuleMethods(this[t]),o=s.length,n=0;n<o;n++)this[t][s[n]]=this[t][s[n]].bind(this);if(typeof this[t].langs!="undefined"){var a={};typeof this[t].langs[this.opts.lang]!="undefined"?a=this[t].langs[this.opts.lang]:typeof this[t].langs[this.opts.lang]=="undefined"&&typeof this[t].langs.en!="undefined"&&(a=this[t].langs.en);var c=this;r.each(a,function(u,h){typeof c.opts.curLang[u]=="undefined"&&(c.opts.curLang[u]=h)})}r.isFunction(this[t].init)&&this[t].init()}},this))}}},button:function(){return{toolbar:function(){return typeof this.button.$toolbar=="undefined"||!this.button.$toolbar?this.$toolbar:this.button.$toolbar},init:function(){return{format:{title:this.lang.get("format"),icon:!0,dropdown:{p:{title:this.lang.get("paragraph"),func:"block.format"},blockquote:{title:this.lang.get("quote"),func:"block.format"},pre:{title:this.lang.get("code"),func:"block.format"},h1:{title:this.lang.get("heading1"),func:"block.format"},h2:{title:this.lang.get("heading2"),func:"block.format"},h3:{title:this.lang.get("heading3"),func:"block.format"},h4:{title:this.lang.get("heading4"),func:"block.format"},h5:{title:this.lang.get("heading5"),func:"block.format"},h6:{title:this.lang.get("heading6"),func:"block.format"}}},bold:{title:this.lang.get("bold-abbr"),icon:!0,label:this.lang.get("bold"),func:"inline.format"},italic:{title:this.lang.get("italic-abbr"),icon:!0,label:this.lang.get("italic"),func:"inline.format"},deleted:{title:this.lang.get("deleted-abbr"),icon:!0,label:this.lang.get("deleted"),func:"inline.format"},underline:{title:this.lang.get("underline-abbr"),icon:!0,label:this.lang.get("underline"),func:"inline.format"},lists:{title:this.lang.get("lists"),icon:!0,dropdown:{unorderedlist:{title:"&bull; "+this.lang.get("unorderedlist"),func:"list.toggle"},orderedlist:{title:"1. "+this.lang.get("orderedlist"),func:"list.toggle"},outdent:{title:"< "+this.lang.get("outdent"),func:"indent.decrease",observe:{element:"li",out:{attr:{class:"redactor-dropdown-link-inactive","aria-disabled":!0}}}},indent:{title:"> "+this.lang.get("indent"),func:"indent.increase",observe:{element:"li",out:{attr:{class:"redactor-dropdown-link-inactive","aria-disabled":!0}}}}}},ul:{title:"&bull; "+this.lang.get("bulletslist"),icon:!0,func:"list.toggle"},ol:{title:"1. "+this.lang.get("numberslist"),icon:!0,func:"list.toggle"},outdent:{title:this.lang.get("outdent"),icon:!0,func:"indent.decrease"},indent:{title:this.lang.get("indent"),icon:!0,func:"indent.increase"},image:{title:this.lang.get("image"),icon:!0,func:"image.show"},file:{title:this.lang.get("file"),icon:!0,func:"file.show"},link:{title:this.lang.get("link"),icon:!0,dropdown:{link:{title:this.lang.get("link-insert"),func:"link.show",observe:{element:"a",in:{title:this.lang.get("link-edit")},out:{title:this.lang.get("link-insert")}}},unlink:{title:this.lang.get("unlink"),func:"link.unlink",observe:{element:"a",out:{attr:{class:"redactor-dropdown-link-inactive","aria-disabled":!0}}}}}},horizontalrule:{title:this.lang.get("horizontalrule"),icon:!0,func:"line.insert"}}},setFormatting:function(){r.each(this.toolbarsButtons.format.dropdown,r.proxy(function(e,t){r.inArray(e,this.opts.formatting)===-1&&delete this.toolbarsButtons.format.dropdown[e]},this))},hideButtons:function(){this.opts.buttonsHide.length!==0&&this.button.hideButtonsSlicer(this.opts.buttonsHide)},hideButtonsOnMobile:function(){this.detect.isMobile()&&this.opts.buttonsHideOnMobile.length!==0&&this.button.hideButtonsSlicer(this.opts.buttonsHideOnMobile)},hideButtonsSlicer:function(e){r.each(e,r.proxy(function(t,i){var s=this.opts.buttons.indexOf(i);s!==-1&&this.opts.buttons.splice(s,1)},this))},load:function(e){this.button.buttons=[],r.each(this.opts.buttons,r.proxy(function(t,i){!this.toolbarsButtons[i]||i==="file"&&!this.file.is()||i==="image"&&!this.image.is()||e.append(r("<li>").append(this.button.build(i,this.toolbarsButtons[i])))},this))},buildButtonTooltip:function(e,t){if(typeof this.button.toolbar()!="undefined"&&!(this.opts.air||this.detect.isMobile())){var i=r("<span>");i.addClass("re-button-tooltip"),i.html(t);var s=this,o=this.button.toolbar(),n=o.closest(".redactor-toolbar-box");n=n.length===0?o:n,n.prepend(i),e.on("mouseover",function(){if(!r(this).hasClass("redactor-button-disabled")){var a=o.hasClass("toolbar-fixed-box")?e.offset():e.position();a=s.opts.toolbarFixedTarget!==document?e.position():a;var c=o.hasClass("toolbar-fixed-box")?e.position().top:a.top,u=e.innerHeight(),h=e.innerWidth(),g=o.hasClass("toolbar-fixed-box")?"fixed":"absolute";g=s.opts.toolbarFixedTarget!==document?"absolute":g;var m=s.opts.toolbarFixedTarget!==document?o.position().top:0;i.show(),i.css({top:c+u+m+"px",left:a.left+h/2-i.innerWidth()/2+"px",position:g})}}).on("mouseout",function(){i.hide()})}},build:function(e,t){if(this.opts.toolbar!==!1){var i=typeof t.label!="undefined"?t.label:t.title,s=r('<a href="javascript:void(null);" alt="'+i+'" rel="'+e+'" />');if(s.addClass("re-button re-"+e),s.attr({role:"button","aria-label":i,tabindex:"-1"}),typeof t.icon!="undefined"&&!this.opts.buttonsTextLabeled){var o=r("<i>");o.addClass("re-icon-"+e),s.append(o),s.addClass("re-button-icon"),this.button.buildButtonTooltip(s,i)}else s.html(t.title);if((t.func||t.command||t.dropdown)&&this.button.setEvent(s,e,t),t.dropdown){s.addClass("redactor-toolbar-link-dropdown").attr("aria-haspopup",!0);var n=r('<ul class="redactor-dropdown redactor-dropdown-'+this.uuid+" redactor-dropdown-box-"+e+'" style="display: none;">');s.data("dropdown",n),this.dropdown.build(e,n,t.dropdown)}return this.button.buttons.push(s),s}},getButtons:function(){return this.button.toolbar().find("a.re-button")},getButtonsKeys:function(){return this.button.buttons},setEvent:function(e,t,i){e.on("mousedown",r.proxy(function(s){if(s.preventDefault(),e.hasClass("redactor-button-disabled"))return!1;var o="func",n=i.func;return i.command?(o="command",n=i.command):i.dropdown&&(o="dropdown",n=!1),this.button.toggle(s,t,o,n),!1},this))},toggle:function(e,t,i,s,o){(this.detect.isIe()||!this.detect.isDesktop())&&(this.utils.freezeScroll(),e.returnValue=!1),i==="command"?this.inline.format(s):i==="dropdown"?this.dropdown.show(e,t):this.button.clickCallback(e,s,t,o),i!=="dropdown"&&this.dropdown.hideAll(!1),this.opts.air&&i!=="dropdown"&&this.air.hide(e),(this.detect.isIe()||!this.detect.isDesktop())&&this.utils.unfreezeScroll()},clickCallback:function(e,t,i,s){var o;if(s=typeof s=="undefined"?i:s,r.isFunction(t))t.call(this,i);else if(t.search(/\./)!=="-1"){if(o=t.split("."),typeof this[o[0]]=="undefined")return;typeof s=="object"?this[o[0]][o[1]].apply(this,s):this[o[0]][o[1]].call(this,s)}else typeof s=="object"?this[t].apply(this,s):this[t].call(this,s);this.observe.buttons(e,i)},all:function(){return this.button.buttons},get:function(e){if(this.opts.toolbar!==!1)return this.button.toolbar().find("a.re-"+e)},set:function(e,t){if(this.opts.toolbar!==!1){var i=this.button.toolbar().find("a.re-"+e);return i.html(t).attr("aria-label",t),i}},add:function(e,t){if(this.button.isAdded(e)!==!0)return r();var i=this.button.build(e,{title:t});return this.button.toolbar().append(r("<li>").append(i)),i},addFirst:function(e,t){if(this.button.isAdded(e)!==!0)return r();var i=this.button.build(e,{title:t});return this.button.toolbar().prepend(r("<li>").append(i)),i},addAfter:function(e,t,i){if(this.button.isAdded(t)!==!0)return r();var s=this.button.build(t,{title:i}),o=this.button.get(e);return o.length!==0?o.parent().after(r("<li>").append(s)):this.button.toolbar().append(r("<li>").append(s)),s},addBefore:function(e,t,i){if(this.button.isAdded(t)!==!0)return r();var s=this.button.build(t,{title:i}),o=this.button.get(e);return o.length!==0?o.parent().before(r("<li>").append(s)):this.button.toolbar().append(r("<li>").append(s)),s},isAdded:function(e){var t=this.opts.buttonsHideOnMobile.indexOf(e);return!(this.opts.toolbar===!1||t!==-1&&this.detect.isMobile())},setIcon:function(e,t){this.opts.buttonsTextLabeled||(e.html(t).addClass("re-button-icon"),this.button.buildButtonTooltip(e,e.attr("alt")))},changeIcon:function(e,t){var i=this.button.get(e);i.length!==0&&i.find("i").removeAttr("class").addClass("re-icon-"+t)},addCallback:function(e,t){if(!(typeof e=="undefined"||this.opts.toolbar===!1)){var i=t==="dropdown"?"dropdown":"func",s=e.attr("rel");e.on("mousedown",r.proxy(function(o){if(e.hasClass("redactor-button-disabled"))return!1;this.button.toggle(o,s,i,t)},this))}},addDropdown:function(e,t){if(this.opts.toolbar!==!1){e.addClass("redactor-toolbar-link-dropdown").attr("aria-haspopup",!0);var i=e.attr("rel");this.button.addCallback(e,"dropdown");var s=r('<div class="redactor-dropdown redactor-dropdown-'+this.uuid+" redactor-dropdown-box-"+i+'" style="display: none;">');return e.data("dropdown",s),t&&this.dropdown.build(i,s,t),s}},setActive:function(e){this.button.get(e).addClass("redactor-act")},setInactive:function(e){this.button.get(e).removeClass("redactor-act")},setInactiveAll:function(e){var t=this.button.toolbar().find("a.re-button");typeof e!="undefined"&&(t=t.not(".re-"+e)),t.removeClass("redactor-act")},disable:function(e){this.button.get(e).addClass("redactor-button-disabled")},enable:function(e){this.button.get(e).removeClass("redactor-button-disabled")},disableAll:function(e){var t=this.button.toolbar().find("a.re-button");typeof e!="undefined"&&(t=t.not(".re-"+e)),t.addClass("redactor-button-disabled")},enableAll:function(){this.button.toolbar().find("a.re-button").removeClass("redactor-button-disabled")},remove:function(e){this.button.get(e).remove()}}},caret:function(){return{set:function(e,t,i){var s=this.core.editor().scrollTop();this.core.editor().focus(),this.core.editor().scrollTop(s),i=typeof i=="undefined"?0:1,e=e[0]||e,t=t[0]||t;var o=this.selection.get(),n=this.selection.range(o);try{n.setStart(e,0),n.setEnd(t,i)}catch(a){}this.selection.update(o,n)},prepare:function(e){return this.detect.isFirefox()&&typeof this.start!="undefined"&&this.core.editor().focus(),e[0]||e},start:function(e){var t,i;if(e=this.caret.prepare(e),!!e){if(e.tagName==="BR")return this.caret.before(e);var s=r(e).children().first(),o=this.utils.isInlineTag(e.tagName);e.innerHTML===""||o?this.caret.setStartEmptyOrInline(e,o):s&&s.length!==0&&this.utils.isInlineTag(s[0].tagName)&&s.text()===""?this.caret.setStartEmptyOrInline(s[0],!0):(t=window.getSelection(),t.removeAllRanges(),i=document.createRange(),i.selectNodeContents(e),i.collapse(!0),t.addRange(i))}},setStartEmptyOrInline:function(e,t){var i=window.getSelection(),s=document.createRange(),o=document.createTextNode("\u200B");s.setStart(e,0),s.insertNode(o),s.setStartAfter(o),s.collapse(!0),i.removeAllRanges(),i.addRange(s),t||this.core.editor().on("keydown.redactor-remove-textnode",function(){r(o).remove(),r(this).off("keydown.redactor-remove-textnode")})},end:function(e){var s,t;if(e=this.caret.prepare(e),!!e){if(e.tagName!=="BR"&&e.innerHTML==="")return this.caret.start(e);if(e.tagName==="BR"){var i=document.createElement("span");i.className="redactor-invisible-space",i.innerHTML="&#x200b;",r(e).after(i),s=window.getSelection(),s.removeAllRanges(),t=document.createRange(),t.setStartBefore(i),t.setEndBefore(i),s.addRange(t),r(i).replaceWith(function(){return r(this).contents()});return}if(e.lastChild&&e.lastChild.nodeType===1)return this.caret.after(e.lastChild);var s=window.getSelection();if(s.getRangeAt||s.rangeCount)try{var t=s.getRangeAt(0);t.selectNodeContents(e),t.collapse(!1),s.removeAllRanges(),s.addRange(t)}catch(o){}}},after:function(e){var t,i;if(e=this.caret.prepare(e),!!e){if(e.tagName==="BR")return this.caret.end(e);if(this.utils.isBlockTag(e.tagName)){var s=this.caret.next(e);typeof s=="undefined"?this.caret.end(e):(s.tagName==="TABLE"?s=r(s).find("th, td").first()[0]:(s.tagName==="UL"||s.tagName==="OL")&&(s=r(s).find("li").first()[0]),this.caret.start(s));return}var o=document.createTextNode("\u200B");t=window.getSelection(),t.removeAllRanges(),i=document.createRange(),i.setStartAfter(e),i.insertNode(o),i.setStartAfter(o),i.collapse(!0),t.addRange(i)}},before:function(e){var t,i;if(e=this.caret.prepare(e),!!e){if(this.utils.isBlockTag(e.tagName)){var s=this.caret.prev(e);typeof s=="undefined"?this.caret.start(e):(s.tagName==="TABLE"?s=r(s).find("th, td").last()[0]:(s.tagName==="UL"||s.tagName==="OL")&&(s=r(s).find("li").last()[0]),this.caret.end(s));return}t=window.getSelection(),t.removeAllRanges(),i=document.createRange(),i.setStartBefore(e),i.collapse(!0),t.addRange(i)}},next:function(e){var t=r(e).next();return t.hasClass("redactor-script-tag, redactor-selection-marker")?t.next()[0]:t[0]},prev:function(e){var t=r(e).prev();return t.hasClass("redactor-script-tag, redactor-selection-marker")?t.prev()[0]:t[0]},offset:function(e){return this.offset.get(e)}}},clean:function(){return{onSet:function(e){e=this.clean.savePreCode(e),e=this.clean.saveFormTags(e),this.opts.script&&(e=e.replace(/<script(.*?[^>]?)>([\w\W]*?)<\/script>/gi,'<pre class="redactor-script-tag" $1>$2</pre>')),e=e.replace(/\$/g,"&#36;"),e=e.replace(/&amp;/g,"&"),e=e.replace(/<a href="(.*?[^>]?)®(.*?[^>]?)">/gi,'<a href="$1&reg$2">'),e=e.replace(/<span id="selection-marker-1"(.*?[^>]?)>​<\/span>/gi,"###marker1###"),e=e.replace(/<span id="selection-marker-2"(.*?[^>]?)>​<\/span>/gi,"###marker2###");var t=this,i=r("<div/>").html(r.parseHTML(e,document,!0)),s=this.opts.replaceTags;if(s){var o=Object.keys(this.opts.replaceTags);i.find(o.join(",")).each(function(a,c){t.utils.replaceToTag(c,s[c.tagName.toLowerCase()])})}i.find("span, a").attr("data-redactor-span",!0),i.find(this.opts.inlineTags.join(",")).each(function(){var a=r(this);a.attr("style")&&a.attr("data-redactor-style-cache",a.attr("style"))}),e=i.html();var n=["font","html","head","link","body","meta","applet"];return this.opts.script||n.push("script"),e=this.clean.stripTags(e,n),this.opts.removeComments&&(e=e.replace(/<!--[\s\S]*?-->/gi,"")),e=this.paragraphize.load(e),e=e.replace("###marker1###",'<span id="selection-marker-1" class="redactor-selection-marker">\u200B</span>'),e=e.replace("###marker2###",'<span id="selection-marker-2" class="redactor-selection-marker">\u200B</span>'),e.search(/^(||\s||<br\s?\/?>||&nbsp;)$/i)!==-1?this.opts.emptyHtml:e},onGet:function(e){return this.clean.onSync(e)},onSync:function(e){if(e=e.replace(/\u200B/g,""),e=e.replace(/&#x200b;/gi,""),e.search(/^<p>(||\s||<br\s?\/?>||&nbsp;)<\/p>$/i)!==-1)return"";e=e.replace(/<span(.*?)id="redactor-image-box"(.*?[^>])>([\w\W]*?)<img(.*?)><\/span>/gi,"$3<img$4>"),e=e.replace(/<span(.*?)id="redactor-image-resizer"(.*?[^>])>(.*?)<\/span>/gi,""),e=e.replace(/<span(.*?)id="redactor-image-editter"(.*?[^>])>(.*?)<\/span>/gi,""),e=e.replace(/<img(.*?)style="(.*?)opacity: 0\.5;(.*?)"(.*?)>/gi,'<img$1style="$2$3"$4>');var t=r("<div/>").html(r.parseHTML(e,document,!0));t.find('*[style=""]').removeAttr("style"),t.find('*[class=""]').removeAttr("class"),t.find('*[rel=""]').removeAttr("rel"),t.find('*[data-image=""]').removeAttr("data-image"),t.find('*[alt=""]').removeAttr("alt"),t.find('*[title=""]').removeAttr("title"),t.find("*[data-redactor-style-cache]").removeAttr("data-redactor-style-cache"),t.find(".redactor-invisible-space, .redactor-unlink").each(function(){r(this).contents().unwrap()}),t.find("span, a").removeAttr("data-redactor-span data-redactor-style-cache").each(function(){this.attributes.length===0&&r(this).contents().unwrap()}),t.find("img").removeAttr("rel"),t.find(".redactor-selection-marker, #redactor-insert-marker").remove(),e=t.html(),this.opts.script&&(e=e.replace(/<pre class="redactor-script-tag"(.*?[^>]?)>([\w\W]*?)<\/pre>/gi,"<script$1>$2<\/script>")),e=this.clean.restoreFormTags(e),e=e.replace(new RegExp("<br\\s?/?></h","gi"),"</h"),e=e.replace(new RegExp("<br\\s?/?></li>","gi"),"</li>"),e=e.replace(new RegExp("</li><br\\s?/?>","gi"),"</li>"),e=e.replace(/<pre>/gi,`<pre>
`),this.opts.preClass&&(e=e.replace(/<pre>/gi,'<pre class="'+this.opts.preClass+'">')),this.opts.linkNofollow&&(e=e.replace(/<a(.*?)rel="nofollow"(.*?[^>])>/gi,"<a$1$2>"),e=e.replace(/<a(.*?[^>])>/gi,'<a$1 rel="nofollow">'));var i={"\u2122":"&trade;","\xA9":"&copy;","\u2026":"&hellip;","\u2014":"&mdash;","\u2010":"&dash;"};return r.each(i,function(s,o){e=e.replace(new RegExp(s,"g"),o)}),e=e.replace(/&amp;/g,"&"),e=e.replace(/\n{2,}/g,`
`),this.opts.removeNewlines&&(e=e.replace(/\r?\n/g,"")),e},onPaste:function(e,t,i){return i!==!0&&(e=e.replace(/<b\sid="internal-source-marker(.*?)">([\w\W]*?)<\/b>/gi,"$2"),e=e.replace(/<b(.*?)id="docs-internal-guid(.*?)">([\w\W]*?)<\/b>/gi,"$3"),e=e.replace(/<span[^>]*(font-style: italic; font-weight: bold|font-weight: bold; font-style: italic)[^>]*>([\w\W]*?)<\/span>/gi,"<b><i>$2</i></b>"),e=e.replace(/<span[^>]*(font-style: italic; font-weight: 700|font-weight: 700; font-style: italic)[^>]*>([\w\W]*?)<\/span>/gi,"<b><i>$2</i></b>"),e=e.replace(/<span[^>]*font-style: italic[^>]*>([\w\W]*?)<\/span>/gi,"<i>$1</i>"),e=e.replace(/<span[^>]*font-weight: bold[^>]*>([\w\W]*?)<\/span>/gi,"<b>$1</b>"),e=e.replace(/<span[^>]*font-weight: 700[^>]*>([\w\W]*?)<\/span>/gi,"<b>$1</b>"),e=e.replace(/<o:p[^>]*>/gi,""),e=e.replace(/<\/o:p>/gi,""),e=this.clean.cleanMsWord(e)),e=r.trim(e),t.pre?this.opts.preSpaces&&(e=e.replace(/\t/g,new Array(this.opts.preSpaces+1).join(" "))):(e=this.clean.replaceBrToNl(e),e=this.clean.removeTagsInsidePre(e)),i!==!0&&(e=this.clean.removeEmptyInlineTags(e),t.encode===!1&&(e=e.replace(/&/g,"&amp;"),e=this.clean.convertTags(e,t),e=this.clean.getPlainText(e),e=this.clean.reconvertTags(e,t))),t.text&&(e=this.clean.replaceNbspToSpaces(e),e=this.clean.getPlainText(e)),t.lists&&(e=e.replace(`
`,"<br>")),t.encode&&(e=this.clean.encodeHtml(e)),t.paragraphize&&(e=e.replace(/ \n/g," "),e=e.replace(/\n /g," "),e=this.paragraphize.load(e),e=e.replace(/<p><\/p>/g,"")),e=e.replace(/<li><p>/g,"<li>"),e=e.replace(/<\/p><\/li>/g,"</li>"),e},getCurrentType:function(e,t){var i=this.selection.blocks(),s={text:!1,encode:!1,paragraphize:!0,line:this.clean.isHtmlLine(e),blocks:this.clean.isHtmlBlocked(e),pre:!1,lists:!1,block:!0,inline:!0,links:!0,images:!0};return i.length===1&&this.utils.isCurrentOrParent(["h1","h2","h3","h4","h5","h6","a","figcaption"])?(s.text=!0,s.paragraphize=!1,s.inline=!1,s.images=!1,s.links=!1,s.line=!0):this.opts.type==="inline"||this.opts.enterKey===!1?(s.paragraphize=!1,s.block=!1,s.line=!0):i.length===1&&this.utils.isCurrentOrParent(["li"])?(s.lists=!0,s.block=!1,s.paragraphize=!1,s.images=!1):i.length===1&&this.utils.isCurrentOrParent(["th","td","blockquote"])?(s.block=!1,s.paragraphize=!1):(this.opts.type==="pre"||i.length===1&&this.utils.isCurrentOrParent("pre"))&&(s.inline=!1,s.block=!1,s.encode=!0,s.pre=!0,s.paragraphize=!1,s.images=!1,s.links=!1),s.line===!0&&(s.paragraphize=!1),t===!0&&(s.text=!1),s},isHtmlBlocked:function(e){var t=e.match(new RegExp("</("+this.opts.blockTags.join("|").toUpperCase()+")>","gi")),i=e.match(new RegExp("<hr(.*?[^>])>","gi"));return!(t===null&&i===null)},isHtmlLine:function(e){if(this.clean.isHtmlBlocked(e))return!1;var t=e.match(/<br\s?\/?>/gi),i=e.match(/\n/gi);return!t&&!i},removeEmptyInlineTags:function(e){var t=this.opts.inlineTags,i=r("<div/>").html(r.parseHTML(e,document,!0)),s=this,o=i.find("span"),n=i.find(t.join(","));return n.removeAttr("style"),n.each(function(){var a=r(this).html();this.attributes.length===0&&s.utils.isEmpty(a)&&r(this).replaceWith(function(){return r(this).contents()})}),o.each(function(){var a=r(this).html();this.attributes.length===0&&r(this).replaceWith(function(){return r(this).contents()})}),e=i.html(),e=e.replace("<!--?php","<?php"),e=e.replace("<!--?","<?"),e=e.replace("?-->","?>"),i.remove(),e},cleanMsWord:function(e){e=e.replace(/<!--[\s\S]*?-->/g,""),e=e.replace(/<o:p>[\s\S]*?<\/o:p>/gi,""),e=e.replace(/\n/g," "),e=e.replace(/<br\s?\/?>|<\/p>|<\/div>|<\/li>|<\/td>/gi,`

`);var t=r("<div/>").html(e),i=!1,s=1,o=[];return t.find("p[style]").each(function(){var n=r(this).attr("style").match(/mso\-list\:l([0-9]+)\slevel([0-9]+)/);if(n){var a=parseInt(n[1]),c=parseInt(n[2]),u=r(this).html().match(/^[\w]+\./)?"ol":"ul",h=r("<li/>").html(r(this).html());if(h.html(h.html().replace(/^([\w\.]+)</,"<")),h.find("span:first").remove(),c==1&&r.inArray(a,o)==-1){var g=r("<"+u+"/>").attr({"data-level":c,"data-list":a}).html(h);r(this).replaceWith(g),i=a,o.push(a)}else{if(c>s){for(var m=t.find('[data-level="'+s+'"][data-list="'+i+'"]'),E=m,A=s;A<c;A++)g=r("<"+u+"/>"),g.appendTo(E.find("li").last()),E=g;E.attr({"data-level":c,"data-list":a}).html(h)}else{var m=t.find('[data-level="'+c+'"][data-list="'+a+'"]').last();m.append(h)}s=c,i=a,r(this).remove()}}}),t.find("[data-level][data-list]").removeAttr("data-level data-list"),e=t.html(),e},replaceNbspToSpaces:function(e){return e.replace("&nbsp;"," ")},replaceBrToNl:function(e){return e.replace(/<br\s?\/?>/gi,`
`)},replaceNlToBr:function(e){return e.replace(/\n/g,"<br />")},convertTags:function(e,t){var i=r("<div>").html(e);i.find("iframe").remove();var s=i.find("a");if(s.removeAttr("style"),this.opts.pasteLinkTarget!==!1&&s.attr("target",this.opts.pasteLinkTarget),t.links&&this.opts.pasteLinks&&i.find("a").each(function(u,h){if(h.href){for(var g='#####[a href="'+h.href+'"',m,E=0,A=h.attributes.length;E<A;E++)m=h.attributes.item(E),m.name!=="href"&&(g+=" "+m.name+'="'+m.value+'"');h.outerHTML=g+"]#####"+h.innerHTML+"#####[/a]#####"}}),e=i.html(),t.images&&this.opts.pasteImages&&(e=e.replace(/<img(.*?)src="(.*?)"(.*?[^>])>/gi,'#####[img$1src="$2"$3]#####')),this.opts.pastePlainText)return e;var o=t.lists?["ul","ol","li"]:this.opts.pasteBlockTags,n;t.block||t.lists?n=t.inline?o.concat(this.opts.pasteInlineTags):o:n=t.inline?this.opts.pasteInlineTags:[];for(var a=n.length,c=0;c<a;c++)e=e.replace(new RegExp("</"+n[c]+">","gi"),"###/"+n[c]+"###"),n[c]==="td"||n[c]==="th"?e=e.replace(new RegExp("<"+n[c]+'(.*?[^>])((colspan|rowspan)="(.*?[^>])")?(.*?[^>])>',"gi"),"###"+n[c]+" $2###"):this.utils.isInlineTag(n[c])?(e=e.replace(new RegExp("<"+n[c]+'([^>]*)class="([^>]*)"[^>]*>',"gi"),"###"+n[c]+' class="$2"###'),e=e.replace(new RegExp("<"+n[c]+'([^>]*)data-redactor-style-cache="([^>]*)"[^>]*>',"gi"),"###"+n[c]+' cache="$2"###'),e=e.replace(new RegExp("<"+n[c]+"[^>]*>","gi"),"###"+n[c]+"###")):e=e.replace(new RegExp("<"+n[c]+"[^>]*>","gi"),"###"+n[c]+"###");return e},reconvertTags:function(e,t){if((t.links&&this.opts.pasteLinks||t.images&&this.opts.pasteImages)&&(e=e.replace(new RegExp("#####\\[","gi"),"<"),e=e.replace(new RegExp("\\]#####","gi"),">")),this.opts.pastePlainText)return e;var i=t.lists?["ul","ol","li"]:this.opts.pasteBlockTags,s;t.block||t.lists?s=t.inline?i.concat(this.opts.pasteInlineTags):i:s=t.inline?this.opts.pasteInlineTags:[];for(var o=s.length,n=0;n<o;n++)e=e.replace(new RegExp("###/"+s[n]+"###","gi"),"</"+s[n]+">");for(var n=0;n<o;n++)e=e.replace(new RegExp("###"+s[n]+"###","gi"),"<"+s[n]+">");for(var n=0;n<o;n++)if(s[n]==="td"||s[n]==="th")e=e.replace(new RegExp("###"+s[n]+"s?(.*?[^#])###","gi"),"<"+s[n]+"$1>");else if(this.utils.isInlineTag(s[n])){var a=s[n]==="span"?' data-redactor-span="true"':"";e=e.replace(new RegExp("###"+s[n]+' cache="(.*?[^#])"###',"gi"),"<"+s[n]+' style="$1"'+a+' data-redactor-style-cache="$1">'),e=e.replace(new RegExp("###"+s[n]+"s?(.*?[^#])###","gi"),"<"+s[n]+"$1>")}return e},cleanPre:function(e){e=typeof e=="undefined"?r(this.selection.block()).closest("pre",this.core.editor()[0]):e,r(e).find("br").replaceWith(function(){return document.createTextNode(`
`)}),r(e).find("p").replaceWith(function(){return r(this).contents()})},removeTagsInsidePre:function(e){var t=r("<div />").append(e);return t.find("pre").replaceWith(function(){var i=r(this).html();return i=i.replace(/<br\s?\/?>|<\/p>|<\/div>|<\/li>|<\/td>/gi,`
`),i=i.replace(/(<([^>]+)>)/gi,""),r("<pre />").append(i)}),e=t.html(),t.remove(),e},getPlainText:function(e){e=e.replace(/<!--[\s\S]*?-->/gi,""),e=e.replace(/<style[\s\S]*?style>/gi,""),e=e.replace(/<p><\/p>/g,""),e=e.replace(/<\/div>|<\/li>|<\/td>/gi,`
`),e=e.replace(/<\/p>/gi,`

`),e=e.replace(/<\/H[1-6]>/gi,`

`);var t=document.createElement("div");return t.innerHTML=e,e=t.textContent||t.innerText,r.trim(e)},savePreCode:function(e){return e=this.clean.savePreFormatting(e),e=this.clean.saveCodeFormatting(e),e=this.clean.restoreSelectionMarkers(e),e},savePreFormatting:function(e){var t=e.match(/<pre(.*?)>([\w\W]*?)<\/pre>/gi);return t===null||r.each(t,r.proxy(function(i,s){var o=[],n=!1,a,c,u;s.match(/<pre(.*?)>(([\n\r\s]+)?)<code(.*?)>/i)?(o=s.match(/<pre(.*?)>(([\n\r\s]+)?)<code(.*?)>([\w\W]*?)<\/code>(([\n\r\s]+)?)<\/pre>/i),n=!0,a=o[5],c=o[1],u=o[4]):(o=s.match(/<pre(.*?)>([\w\W]*?)<\/pre>/i),a=o[2],c=o[1]),a=a.replace(/<br\s?\/?>/g,`
`),a=a.replace(/&nbsp;/g," "),this.opts.preSpaces&&(a=a.replace(/\t/g,new Array(this.opts.preSpaces+1).join(" "))),a=this.clean.encodeEntities(a),a=a.replace(/\$/g,"&#36;"),n?e=e.replace(s,"<pre"+c+"><code"+u+">"+a+"</code></pre>"):e=e.replace(s,"<pre"+c+">"+a+"</pre>")},this)),e},saveCodeFormatting:function(e){var t=e.match(/<code(.*?)>([\w\W]*?)<\/code>/gi);return t===null||r.each(t,r.proxy(function(i,s){var o=s.match(/<code(.*?)>([\w\W]*?)<\/code>/i);o[2]=o[2].replace(/&nbsp;/g," "),o[2]=this.clean.encodeEntities(o[2]),o[2]=o[2].replace(/\$/g,"&#36;"),e=e.replace(s,"<code"+o[1]+">"+o[2]+"</code>")},this)),e},restoreSelectionMarkers:function(e){return e=e.replace(/&lt;span id=&quot;selection-marker-([0-9])&quot; class=&quot;redactor-selection-marker&quot;&gt;​&lt;\/span&gt;/g,'<span id="selection-marker-$1" class="redactor-selection-marker">\u200B</span>'),e},saveFormTags:function(e){return e.replace(/<form(.*?)>([\w\W]*?)<\/form>/gi,'<section$1 rel="redactor-form-tag">$2</section>')},restoreFormTags:function(e){return e.replace(/<section(.*?) rel="redactor-form-tag"(.*?)>([\w\W]*?)<\/section>/gi,"<form$1$2>$3</form>")},encodeHtml:function(e){return e=e.replace(/”/g,'"'),e=e.replace(/“/g,'"'),e=e.replace(/‘/g,"'"),e=e.replace(/’/g,"'"),e=this.clean.encodeEntities(e),e},encodeEntities:function(e){return e=String(e).replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"'),e=e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"),e},stripTags:function(e,t){if(typeof t=="undefined")return e.replace(/(<([^>]+)>)/gi,"");var i=/<\/?([a-z][a-z0-9]*)\b[^>]*>/gi;return e.replace(i,function(s,o){return t.indexOf(o.toLowerCase())===-1?s:""})},removeMarkers:function(e){return e.replace(/<span(.*?[^>]?)class="redactor-selection-marker"(.*?[^>]?)>([\w\W]*?)<\/span>/gi,"")},removeSpaces:function(e){return e=r.trim(e),e=e.replace(/\n/g,""),e=e.replace(/[\t]*/g,""),e=e.replace(/\n\s*\n/g,`
`),e=e.replace(/^[\s\n]*/g," "),e=e.replace(/[\s\n]*$/g," "),e=e.replace(/>\s{2,}</g,"> <"),e=e.replace(/\n\n/g,`
`),e=e.replace(/\u200B/g,""),e},removeSpacesHard:function(e){return e=r.trim(e),e=e.replace(/\n/g,""),e=e.replace(/[\t]*/g,""),e=e.replace(/\n\s*\n/g,`
`),e=e.replace(/^[\s\n]*/g,""),e=e.replace(/[\s\n]*$/g,""),e=e.replace(/>\s{2,}</g,"><"),e=e.replace(/\n\n/g,`
`),e=e.replace(/\u200B/g,""),e},normalizeCurrentHeading:function(){var e=this.selection.block();this.utils.isCurrentOrParentHeader()&&e&&e.normalize()}}},code:function(){return{syncFire:!0,html:!1,start:function(e){e=r.trim(e),e=e.replace(/^(<span id="selection-marker-1" class="redactor-selection-marker">​<\/span>)/,""),this.opts.type==="textarea"?e=this.clean.onSet(e):this.opts.type==="div"&&e===""&&(e=this.opts.emptyHtml),e=e.replace(/<p><span id="selection-marker-1" class="redactor-selection-marker">​<\/span><\/p>/,""),this.events.stopDetectChanges(),this.core.editor().html(e),this.observe.load(),this.events.startDetectChanges()},set:function(e,t){e=r.trim(e),t=t||{},t.start&&(this.start=t.start),this.opts.type==="textarea"?e=this.clean.onSet(e):this.opts.type==="div"&&e===""&&(e=this.opts.emptyHtml),this.core.editor().html(e),this.opts.type==="textarea"&&this.code.sync(),this.placeholder.enable(),t.start||this.observe.load()},get:function(){if(this.opts.type==="textarea")return this.core.textarea().val();var e=this.core.editor().html();return e=this.clean.onGet(e),e},sync:function(){if(this.code.syncFire){var e=this.core.editor().html(),t=this.code.cleaned(e);if(!this.code.isSync(t)){if(this.code.html=t,this.opts.type!=="textarea"){this.core.callback("sync",e),this.core.callback("change",e);return}this.opts.type==="textarea"&&setTimeout(r.proxy(function(){this.code.startSync(e)},this),10)}}},startSync:function(e){e=this.core.callback("syncBefore",e),e=this.clean.onSync(e),this.core.textarea().val(e),this.core.callback("sync",e),this.start===!1&&this.core.callback("change",e),this.start=!1},isSync:function(e){var t=this.code.html!==!1?this.code.html:!1;return t!==!1&&t===e},cleaned:function(e){return e=e.replace(/\u200B/g,""),this.clean.removeMarkers(e)}}},core:function(){return{id:function(){return this.$editor.attr("id")},element:function(){return this.$element},editor:function(){return typeof this.$editor=="undefined"?r():this.$editor},textarea:function(){return this.$textarea},box:function(){return this.opts.type==="textarea"?this.$box:this.$element},toolbar:function(){return this.$toolbar?this.$toolbar:!1},air:function(){return this.$air?this.$air:!1},object:function(){return r.extend({},this)},structure:function(){this.core.editor().toggleClass("redactor-structure")},addEvent:function(e){this.core.event=e},getEvent:function(){return this.core.event},callback:function(e,t,i){var s="redactor",o=!1,n=r._data(this.core.element()[0],"events");if(typeof n!="undefined"&&typeof n[e]!="undefined")for(var a=n[e].length,c=0;c<a;c++){var u=n[e][c].namespace;if(u==="callback."+s){var h=n[e][c].handler,g=typeof i=="undefined"?[t]:[t,i];o=typeof g=="undefined"?h.call(this,t):h.call(this,t,g)}}if(o)return o;if(typeof this.opts.callbacks[e]=="undefined")return typeof i=="undefined"?t:i;var m=this.opts.callbacks[e];return r.isFunction(m)?typeof i=="undefined"?m.call(this,t):m.call(this,t,i):typeof i=="undefined"?t:i},destroy:function(){this.opts.destroyed=!0,this.core.callback("destroy"),this.placeholder.destroy(),this.progress.destroy(),r("#redactor-voice-"+this.uuid).remove(),this.core.editor().removeClass("redactor-in redactor-styles redactor-structure redactor-layer-img-edit"),this.core.editor().off("keydown.redactor-remove-textnode"),this.core.editor().off(".redactor-observe."+this.uuid),this.$element.off(".redactor").removeData("redactor"),this.core.editor().off(".redactor"),r(document).off(".redactor-dropdown"),r(document).off(".redactor-air."+this.uuid),r(document).off("mousedown.redactor-blur."+this.uuid),r(document).off("mousedown.redactor."+this.uuid),r(document).off("touchstart.redactor."+this.uuid+" click.redactor."+this.uuid),r(window).off(".redactor-toolbar."+this.uuid),r(window).off("touchmove.redactor."+this.uuid),r("body").off("scroll.redactor."+this.uuid),r(this.opts.toolbarFixedTarget).off("scroll.redactor."+this.uuid);var e=this;this.opts.plugins!==!1&&r.each(this.opts.plugins,function(i,s){r(window).off(".redactor-plugin-"+s),r(document).off(".redactor-plugin-"+s),r("body").off(".redactor-plugin-"+s),e.core.editor().off(".redactor-plugin-"+s)}),this.$element.off("click.redactor-click-to-edit"),this.$element.removeClass("redactor-click-to-edit"),this.core.editor().removeClass("redactor-layer"),this.core.editor().removeAttr("contenteditable");var t=this.code.get();this.opts.toolbar&&this.$toolbar&&this.$toolbar.find("a").each(function(){var i=r(this);i.data("dropdown")&&(i.data("dropdown").remove(),i.data("dropdown",{}))}),this.opts.type==="textarea"&&(this.$box.after(this.$element),this.$box.remove(),this.$element.val(t).show()),this.opts.air&&this.$air.remove(),this.opts.toolbar&&this.$toolbar&&this.$toolbar.remove(),this.$modalBox&&this.$modalBox.remove(),this.$modalOverlay&&this.$modalOverlay.remove(),r(".redactor-link-tooltip").remove(),clearInterval(this.autosaveTimeout)}}},detect:function(){return{isWebkit:function(){return/webkit/.test(this.opts.userAgent)},isFirefox:function(){return this.opts.userAgent.indexOf("firefox")>-1},isIe:function(e){if(document.documentMode||/Edge/.test(navigator.userAgent))return"edge";var t;return t=RegExp("msie"+(isNaN(e)?"":"\\s"+e),"i").test(navigator.userAgent),t||(t=!!navigator.userAgent.match(/Trident.*rv[ :]*11\./)),t},isMobile:function(){return/(iPhone|iPod|BlackBerry|Android)/.test(navigator.userAgent)},isDesktop:function(){return!/(iPhone|iPod|iPad|BlackBerry|Android)/.test(navigator.userAgent)},isIpad:function(){return/iPad/.test(navigator.userAgent)}}},dropdown:function(){return{active:!1,button:!1,key:!1,position:[],getDropdown:function(){return this.dropdown.active},build:function(e,t,i){i=this.dropdown.buildFormatting(e,i),r.each(i,r.proxy(function(s,o){var n=this.dropdown.buildItem(s,o);this.observe.addDropdown(n,s,o),t.attr("rel",e).append(n)},this))},buildFormatting:function(e,t){return e!=="format"||this.opts.formattingAdd===!1||r.each(this.opts.formattingAdd,r.proxy(function(i,s){var o=this.utils.isBlockTag(s.args[0])?"block":"inline";t[i]={func:o==="block"?"block.format":"inline.format",args:s.args,title:s.title}},this)),t},buildItem:function(e,t){var i=r("<li />");if(typeof t.classname!="undefined"&&i.addClass(t.classname),e.search(/^divider/i)!==-1)return i.addClass("redactor-dropdown-divider"),i;var s=r('<a href="#" class="redactor-dropdown-'+e+'" role="button" />'),o=r("<span />").html(t.title);return s.append(o),s.on("mousedown",r.proxy(function(n){n.preventDefault(),this.dropdown.buildClick(n,e,t)},this)),i.append(s),i},buildClick:function(e,t,i){if(!r(e.target).hasClass("redactor-dropdown-link-inactive")){var s=this.dropdown.buildCommand(i);typeof i.args!="undefined"?this.button.toggle(e,t,s.type,s.callback,i.args):this.button.toggle(e,t,s.type,s.callback)}},buildCommand:function(e){var t={};return t.type="func",t.callback=e.func,e.command?(t.type="command",t.callback=e.command):e.dropdown&&(t.type="dropdown",t.callback=e.dropdown),t},show:function(e,t){if(this.detect.isDesktop()&&this.core.editor().focus(),this.dropdown.hideAll(!1,t),this.dropdown.key=t,this.dropdown.button=this.button.get(this.dropdown.key),this.dropdown.button.hasClass("dropact")){this.dropdown.hide();return}this.dropdown.active=this.dropdown.button.data("dropdown").appendTo(document.body),this.core.callback("dropdownShow",{dropdown:this.dropdown.active,key:this.dropdown.key,button:this.dropdown.button}),this.button.setActive(this.dropdown.key),this.dropdown.button.addClass("dropact"),this.dropdown.getButtonPosition(),this.button.toolbar().hasClass("toolbar-fixed-box")&&this.detect.isDesktop()?this.dropdown.showIsFixedToolbar():this.dropdown.showIsUnFixedToolbar(),this.detect.isDesktop()&&!this.detect.isFirefox()&&(this.dropdown.active.on("mouseover.redactor-dropdown",r.proxy(this.utils.disableBodyScroll,this)),this.dropdown.active.on("mouseout.redactor-dropdown mousedown.redactor-dropdown",r.proxy(this.utils.enableBodyScroll,this))),e.stopPropagation()},showIsFixedToolbar:function(){var e=this.dropdown.button.position().top+this.dropdown.button.innerHeight()+this.opts.toolbarFixedTopOffset,t="fixed";this.opts.toolbarFixedTarget!==document&&(e=this.dropdown.button.innerHeight()+this.$toolbar.offset().top+this.opts.toolbarFixedTopOffset,t="absolute"),this.dropdown.active.css({position:t,left:this.dropdown.position.left+"px",top:e+"px"}).show(),this.dropdown.active.redactorAnimation("slideDown",{duration:.2},r.proxy(function(){this.dropdown.enableCallback(),this.dropdown.enableEvents()},this))},showIsUnFixedToolbar:function(){this.dropdown.active.css({position:"absolute",left:this.dropdown.position.left+"px",top:this.dropdown.button.innerHeight()+this.dropdown.position.top+"px"}).show(),this.dropdown.active.redactorAnimation(this.opts.animation?"slideDown":"show",{duration:.2},r.proxy(function(){this.dropdown.enableCallback(),this.dropdown.enableEvents()},this))},enableEvents:function(){r(document).on("mousedown.redactor-dropdown",r.proxy(this.dropdown.hideAll,this)),this.core.editor().on("touchstart.redactor-dropdown",r.proxy(this.dropdown.hideAll,this)),r(document).on("keyup.redactor-dropdown",r.proxy(this.dropdown.closeHandler,this))},enableCallback:function(){this.core.callback("dropdownShown",{dropdown:this.dropdown.active,key:this.dropdown.key,button:this.dropdown.button})},getButtonPosition:function(){this.dropdown.position=this.dropdown.button.offset();var e=this.dropdown.active.width();this.dropdown.position.left+e>r(document).width()&&(this.dropdown.position.left=Math.max(0,this.dropdown.position.left-e+parseInt(this.dropdown.button.innerWidth())))},closeHandler:function(e){e.which===this.keyCode.ESC&&(this.dropdown.hideAll(e),this.core.editor().focus())},hideAll:function(e,t){if(this.detect.isDesktop()&&this.utils.enableBodyScroll(),!(e!==!1&&r(e.target).closest(".redactor-dropdown").length!==0)){var i=typeof t=="undefined"?this.button.toolbar().find("a.dropact"):this.button.toolbar().find("a.dropact").not(".re-"+t),s=typeof t=="undefined"?r(".redactor-dropdown-"+this.uuid):r(".redactor-dropdown-"+this.uuid).not(".redactor-dropdown-box-"+t);s.length!==0&&(r(document).off(".redactor-dropdown"),this.core.editor().off(".redactor-dropdown"),r.each(s,r.proxy(function(o,n){var a=r(n);this.core.callback("dropdownHide",a),a.hide(),a.off("mouseover mouseout").off(".redactor-dropdown")},this)),i.removeClass("redactor-act dropact"))}},hide:function(){this.dropdown.active!==!1&&(this.detect.isDesktop()&&this.utils.enableBodyScroll(),this.dropdown.active.redactorAnimation(this.opts.animation?"slideUp":"hide",{duration:.2},r.proxy(function(){r(document).off(".redactor-dropdown"),this.core.editor().off(".redactor-dropdown"),this.dropdown.hideOut()},this)))},hideOut:function(){this.core.callback("dropdownHide",this.dropdown.active),this.dropdown.button.removeClass("redactor-act dropact"),this.dropdown.active.off("mouseover mouseout").off(".redactor-dropdown"),this.dropdown.button=!1,this.dropdown.key=!1,this.dropdown.active=!1}}},events:function(){return{focused:!1,blured:!0,dropImage:!1,stopChanges:!1,stopDetectChanges:function(){this.events.stopChanges=!0},startDetectChanges:function(){var e=this;setTimeout(function(){e.events.stopChanges=!1},1)},dragover:function(e){e.preventDefault(),e.stopPropagation(),e.target.tagName==="IMG"&&r(e.target).addClass("redactor-image-dragover")},dragleave:function(e){this.core.editor().find("img").removeClass("redactor-image-dragover")},drop:function(e){if(e=e.originalEvent||e,this.core.editor().find("img").removeClass("redactor-image-dragover"),this.opts.type==="inline"||this.opts.type==="pre")return e.preventDefault(),!1;if(window.FormData===void 0||!e.dataTransfer)return!0;if(e.dataTransfer.files.length===0)return this.events.onDrop(e);this.events.onDropUpload(e),this.core.callback("drop",e)},click:function(e){var t=this.core.getEvent(),i=t==="click"||t==="arrow"?!1:"click";this.core.addEvent(i),this.utils.disableSelectAll(),this.core.callback("click",e)},focus:function(e){if(!this.rtePaste&&(this.events.isCallback("focus")&&this.core.callback("focus",e),this.events.focused=!0,this.events.blured=!1,this.selection.current()===!1)){var t=this.selection.get(),i=this.selection.range(t);i.setStart(this.core.editor()[0],0),i.setEnd(this.core.editor()[0],0),this.selection.update(t,i)}},blur:function(e){this.start||this.rtePaste||r(e.target).closest("#"+this.core.id()+", .redactor-toolbar, .redactor-dropdown, #redactor-modal-box").length===0&&(!this.events.blured&&this.events.isCallback("blur")&&this.core.callback("blur",e),this.events.focused=!1,this.events.blured=!0)},touchImageEditing:function(){var e=-1;this.events.imageEditing=!1,r(window).on("touchmove.redactor."+this.uuid,r.proxy(function(){this.events.imageEditing=!0,e!==-1&&clearTimeout(e),e=setTimeout(r.proxy(function(){this.events.imageEditing=!1},this),500)},this))},init:function(){this.core.editor().on("dragover.redactor dragenter.redactor",r.proxy(this.events.dragover,this)),this.core.editor().on("dragleave.redactor",r.proxy(this.events.dragleave,this)),this.core.editor().on("drop.redactor",r.proxy(this.events.drop,this)),this.core.editor().on("click.redactor",r.proxy(this.events.click,this)),this.core.editor().on("paste.redactor",r.proxy(this.paste.init,this)),this.core.editor().on("keydown.redactor",r.proxy(this.keydown.init,this)),this.core.editor().on("keyup.redactor",r.proxy(this.keyup.init,this)),this.core.editor().on("focus.redactor",r.proxy(this.events.focus,this)),r(document).on("mousedown.redactor-blur."+this.uuid,r.proxy(this.events.blur,this)),this.events.touchImageEditing(),this.events.createObserver(),this.events.setupObserver()},createObserver:function(){var e=this;this.events.observer=new MutationObserver(function(t){t.forEach(r.proxy(e.events.iterateObserver,e))})},iterateObserver:function(e){var t=!1;((this.opts.type==="textarea"||this.opts.type==="div")&&!this.detect.isFirefox()&&e.target===this.core.editor()[0]||e.attributeName==="class"&&e.target===this.core.editor()[0]||e.attributeName=="data-vivaldi-spatnav-clickable")&&(t=!0),t||(this.observe.load(),this.events.changeHandler())},setupObserver:function(){this.events.observer.observe(this.core.editor()[0],{attributes:!0,subtree:!0,childList:!0,characterData:!0,characterDataOldValue:!0})},changeHandler:function(){this.events.stopChanges||(this.code.sync(),this.autosave.is()&&(clearTimeout(this.autosaveTimeout),this.autosaveTimeout=setTimeout(r.proxy(this.autosave.send,this),300)))},onDropUpload:function(e){if(e.preventDefault(),e.stopPropagation(),!(!this.opts.dragImageUpload&&!this.opts.dragFileUpload||this.opts.imageUpload===null&&this.opts.fileUpload===null)){e.target.tagName==="IMG"&&(this.events.dropImage=e.target);for(var t=e.dataTransfer.files,i=t.length,s=0;s<i;s++)this.upload.directUpload(t[s],e)}},onDrop:function(e){this.core.callback("drop",e)},isCallback:function(e){return typeof this.opts.callbacks[e]!="undefined"&&r.isFunction(this.opts.callbacks[e])},stopDetect:function(){this.events.stopDetectChanges()},startDetect:function(){this.events.startDetectChanges()}}},file:function(){return{is:function(){return!(!this.opts.fileUpload||!this.opts.fileUpload&&!this.opts.s3)},show:function(){this.modal.load("file",this.lang.get("file"),700),this.upload.init("#redactor-modal-file-upload",this.opts.fileUpload,this.file.insert),r("#redactor-filename").val(this.selection.get().toString()),this.modal.show()},insert:function(e,t,i){if(typeof e.error!="undefined"){this.modal.close(),this.core.callback("fileUploadError",e);return}this.file.release(i,t),this.buffer.set(),this.air.collapsed();var s=this.file.text(e),o=r("<a />").attr("href",e.url).text(s),n=typeof e.id=="undefined"?"":e.id,a=typeof e.s3=="undefined"?"file":"s3";o.attr("data-"+a,n),o=r(this.insert.node(o)),this.caret.after(o),this.storage.add({type:a,node:o[0],url:e.url,id:n}),t!==null&&this.core.callback("fileUpload",o,e)},release:function(e,t){t?(this.marker.remove(),this.insert.nodeToPoint(e,this.marker.get()),this.selection.restore()):this.modal.close()},text:function(e){var t=r("#redactor-filename").val();return typeof t=="undefined"||t===""?e.name:t}}},focus:function(){return{start:function(){if(this.core.editor().focus(),this.opts.type!=="inline"){var e=this.focus.first();e!==!1&&this.caret.start(e)}},end:function(){this.core.editor().focus();var e=this.opts.inline?this.core.editor():this.focus.last();if(e.length!==0){var t=this.focus.lastChild(e);if(!this.detect.isWebkit()&&t!==!1)this.caret.end(t);else{var i=this.selection.get(),s=this.selection.range(i);s!==null?(s.selectNodeContents(e[0]),s.collapse(!1),this.selection.update(i,s)):this.caret.end(e)}}},first:function(){var e=this.core.editor().children().first();return e.length===0&&(e[0].length===0||e[0].tagName==="BR"||e[0].tagName==="HR"||e[0].nodeType===3)?!1:e[0].tagName==="UL"||e[0].tagName==="OL"?e.find("li").first():e},last:function(){return this.core.editor().children().last()},lastChild:function(e){var t=e[0].lastChild;return t!==null&&this.utils.isInlineTag(t.tagName)?t:!1},is:function(){return this.core.editor()[0]===document.activeElement}}},image:function(){return{is:function(){return!(!this.opts.imageUpload||!this.opts.imageUpload&&!this.opts.s3)},show:function(){this.modal.load("image",this.lang.get("image"),700),this.upload.init("#redactor-modal-image-droparea",this.opts.imageUpload,this.image.insert),this.modal.show()},insert:function(e,t,i){var s;if(typeof e.error!="undefined"){this.modal.close(),this.events.dropImage=!1,this.core.callback("imageUploadError",e,i);return}if(this.events.dropImage!==!1){s=r(this.events.dropImage),this.core.callback("imageDelete",s[0].src,s),s.attr("src",e.url),this.events.dropImage=!1,this.core.callback("imageUpload",s,e);return}this.placeholder.hide();var o=r("<"+this.opts.imageTag+">");s=r("<img>"),s.attr("src",e.url);var n=typeof e.id=="undefined"?"":e.id,a=typeof e.s3=="undefined"?"image":"s3";s.attr("data-"+a,n),o.append(s);var c=this.utils.isTag(this.selection.current(),"pre");if(t){this.air.collapsed(),this.marker.remove();var u=this.insert.nodeToPoint(i,this.marker.get()),h=r(u).next();this.selection.restore(),this.buffer.set(),typeof h!="undefined"&&h.length!==0&&h[0].tagName==="IMG"?(this.core.callback("imageDelete",h[0].src,h),h.closest("figure, p",this.core.editor()[0]).replaceWith(o),this.caret.after(o)):(c?r(c).after(o):this.insert.node(o),this.caret.after(o))}else this.modal.close(),this.buffer.set(),this.air.collapsed(),c?r(c).after(o):this.insert.node(o),this.caret.after(o);this.events.dropImage=!1,this.storage.add({type:a,node:s[0],url:e.url,id:n});var g=s[0].nextSibling,m=o.next(),E=r(g).text().replace(/\u200B/g,""),A=m.text().replace(/\u200B/g,"");E===""&&r(g).remove(),m.length===1&&m[0].tagName==="FIGURE"&&A===""&&m.remove(),t!==null?this.core.callback("imageUpload",s,e):this.core.callback("imageInserted",s,e)},setEditable:function(e){if(e.on("dragstart",function(i){i.preventDefault()}),this.opts.imageResizable){var t=r.proxy(function(i){this.observe.image=e,this.image.resizer=this.image.loadEditableControls(e),r(document).on("mousedown.redactor-image-resize-hide."+this.uuid,r.proxy(this.image.hideResize,this)),this.image.resizer&&this.image.resizer.on("mousedown.redactor touchstart.redactor",r.proxy(function(s){this.image.setResizable(s,e)},this))},this);e.off("mousedown.redactor").on("mousedown.redactor",r.proxy(this.image.hideResize,this)),e.off("click.redactor touchstart.redactor").on("click.redactor touchstart.redactor",t)}else e.off("click.redactor touchstart.redactor").on("click.redactor touchstart.redactor",r.proxy(function(i){setTimeout(r.proxy(function(){this.image.showEdit(e)},this),200)},this))},setResizable:function(e,t){e.preventDefault(),this.image.resizeHandle={x:e.pageX,y:e.pageY,el:t,ratio:t.width()/t.height(),h:t.height()},e=e.originalEvent||e,e.targetTouches&&(this.image.resizeHandle.x=e.targetTouches[0].pageX,this.image.resizeHandle.y=e.targetTouches[0].pageY),this.image.startResize()},startResize:function(){r(document).on("mousemove.redactor-image-resize touchmove.redactor-image-resize",r.proxy(this.image.moveResize,this)),r(document).on("mouseup.redactor-image-resize touchend.redactor-image-resize",r.proxy(this.image.stopResize,this))},moveResize:function(e){e.preventDefault(),e=e.originalEvent||e;var t=this.image.resizeHandle.h;e.targetTouches?t+=e.targetTouches[0].pageY-this.image.resizeHandle.y:t+=e.pageY-this.image.resizeHandle.y;var i=Math.round(t*this.image.resizeHandle.ratio);t<50||i<100||this.core.editor().width()<=i||(this.image.resizeHandle.el.attr({width:i,height:t}),this.image.resizeHandle.el.width(i),this.image.resizeHandle.el.height(t),this.code.sync())},stopResize:function(){this.handle=!1,r(document).off(".redactor-image-resize"),this.image.hideResize()},hideResize:function(e){if(!(e&&r(e.target).closest("#redactor-image-box",this.$editor[0]).length!==0)){if(e&&e.target.tagName=="IMG")var t=r(e.target);var i=this.$editor.find("#redactor-image-box");i.length!==0&&(r("#redactor-image-editter").remove(),r("#redactor-image-resizer").remove(),i.find("img").css({marginTop:i[0].style.marginTop,marginBottom:i[0].style.marginBottom,marginLeft:i[0].style.marginLeft,marginRight:i[0].style.marginRight}),i.css("margin",""),i.find("img").css("opacity",""),i.replaceWith(function(){return r(this).contents()}),r(document).off("mousedown.redactor-image-resize-hide."+this.uuid),typeof this.image.resizeHandle!="undefined"&&this.image.resizeHandle.el.attr("rel",this.image.resizeHandle.el.attr("style")))}},loadResizableControls:function(e,t){if(this.opts.imageResizable&&!this.detect.isMobile()){var i=r('<span id="redactor-image-resizer" data-redactor="verified"></span>');return this.detect.isDesktop()||i.css({width:"15px",height:"15px"}),i.attr("contenteditable",!1),t.append(i),t.append(e),i}else return t.append(e),!1},loadEditableControls:function(e){if(r("#redactor-image-box").length===0){var t=r('<span id="redactor-image-box" data-redactor="verified">');if(t.css("float",e.css("float")).attr("contenteditable",!1),e[0].style.margin!="auto"?(t.css({marginTop:e[0].style.marginTop,marginBottom:e[0].style.marginBottom,marginLeft:e[0].style.marginLeft,marginRight:e[0].style.marginRight}),e.css("margin","")):t.css({display:"block",margin:"auto"}),e.css("opacity",".5").after(t),this.opts.imageEditable){this.image.editter=r('<span id="redactor-image-editter" data-redactor="verified">'+this.lang.get("edit")+"</span>"),this.image.editter.attr("contenteditable",!1),this.image.editter.on("click",r.proxy(function(){this.image.showEdit(e)},this)),t.append(this.image.editter);var i=this.image.editter.innerWidth();this.image.editter.css("margin-left","-"+i/2+"px")}return this.image.loadResizableControls(e,t)}},showEdit:function(e){if(!this.events.imageEditing){this.observe.image=e;var t=e.closest("a",this.$editor[0]),i=e.closest("figure",this.$editor[0]),s=i.length!==0?i:e;if(this.modal.load("image-edit",this.lang.get("edit"),705),this.image.buttonDelete=this.modal.getDeleteButton().text(this.lang.get("delete")),this.image.buttonSave=this.modal.getActionButton().text(this.lang.get("save")),this.image.buttonDelete.on("click",r.proxy(this.image.remove,this)),this.image.buttonSave.on("click",r.proxy(this.image.update,this)),this.opts.imageCaption===!1)r("#redactor-image-caption").val("").hide().prev().hide();else{var o=e.closest(this.opts.imageTag,this.$editor[0]),n=o.find("figcaption");n!==0&&r("#redactor-image-caption").val(n.text()).show()}if(!this.opts.imagePosition)r(".redactor-image-position-option").hide();else{var a=i.length!==0?s.css("text-align")==="center":s.css("display")=="block"&&s.css("float")=="none",c=a?"center":s.css("float");r("#redactor-image-align").val(c)}r("#redactor-image-preview").html(r('<img src="'+e.attr("src")+'" style="max-width: 100%;">')),r("#redactor-image-title").val(e.attr("alt")),t.length!==0&&(r("#redactor-image-link").val(t.attr("href")),t.attr("target")==="_blank"&&r("#redactor-image-link-blank").prop("checked",!0)),r(".redactor-link-tooltip").remove(),this.modal.show(),this.detect.isDesktop()&&r("#redactor-image-title").focus()}},update:function(){var e=this.observe.image,t=e.closest("a",this.core.editor()[0]),i=r("#redactor-image-title").val().replace(/(<([^>]+)>)/ig,"");e.attr("alt",i).attr("title",i),this.image.setFloating(e);var s=r.trim(r("#redactor-image-link").val()).replace(/(<([^>]+)>)/ig,"");if(s!==""){var o="((xn--)?[a-z0-9]+(-[a-z0-9]+)*\\.)+[a-z]{2,}",n=new RegExp("^(http|ftp|https)://"+o,"i"),a=new RegExp("^"+o,"i");s.search(n)===-1&&s.search(a)===0&&this.opts.linkProtocol&&(s=this.opts.linkProtocol+"://"+s);var c=!!r("#redactor-image-link-blank").prop("checked");if(t.length===0){var u=r('<a href="'+s+'" id="redactor-img-tmp">'+this.utils.getOuterHtml(e)+"</a>");c&&u.attr("target","_blank"),e=e.replaceWith(u),t=this.core.editor().find("#redactor-img-tmp"),t.removeAttr("id")}else t.attr("href",s),c?t.attr("target","_blank"):t.removeAttr("target")}else t.length!==0&&t.replaceWith(this.utils.getOuterHtml(e));this.image.addCaption(e,t),this.modal.close(),this.buffer.set()},setFloating:function(e){var t=e.closest("figure",this.$editor[0]),i=t.length!==0?t:e,s=r("#redactor-image-align").val(),o="",n="",a="",c="";switch(s){case"left":o="left",a="0 "+this.opts.imageFloatMargin+" "+this.opts.imageFloatMargin+" 0";break;case"right":o="right",a="0 0 "+this.opts.imageFloatMargin+" "+this.opts.imageFloatMargin;break;case"center":t.length!==0?c="center":(n="block",a="auto");break}i.css({float:o,display:n,margin:a,"text-align":c}),i.attr("rel",e.attr("style"))},addCaption:function(e,t){var i=r("#redactor-image-caption").val(),s=t.length!==0?t:e,o=s.next();(o.length===0||o[0].tagName!=="FIGCAPTION")&&(o=!1),i!==""?o===!1?(o=r("<figcaption />").text(i),s.after(o)):o.text(i):o!==!1&&o.remove()},remove:function(e,t,i){t=typeof t=="undefined"?r(this.observe.image):t,typeof e!="boolean"&&this.buffer.set(),this.events.stopDetectChanges();var s=t.closest("a",this.core.editor()[0]),o=t.closest(this.opts.imageTag,this.core.editor()[0]),n=t.parent(),a=this.core.callback("imageDelete",e,t[0]);if(a===!1)return e&&e.preventDefault(),!1;r("#redactor-image-box").length!==0&&(n=r("#redactor-image-box").parent());var c,u;o.length!==0?(u=o.prev(),c=o.next(),o.remove()):s.length!==0?(n=s.parent(),s.remove()):t.remove(),r("#redactor-image-box").remove(),e!==!1&&(c&&c.length!==0?this.caret.start(c):u&&u.length!==0&&this.caret.end(u)),typeof e!="boolean"&&this.modal.close(),this.utils.restoreScroll(),this.observe.image=!1,this.events.startDetectChanges(),this.placeholder.enable(),this.code.sync()}}},indent:function(){return{increase:function(){if(this.list.get()){var e=r(this.selection.current()).closest("li"),t=e.closest("ul, ol",this.core.editor()[0]),i=e.closest("li"),s=i.prev();if(!(s.length===0||s[0].tagName!=="LI"))if(this.buffer.set(),this.utils.isCollapsed()){var o=t[0].tagName,n=r("<"+o+" />");this.selection.save();var a=s.find("ol").first();if(a.length===1)a.append(e);else{var o=t[0].tagName,n=r("<"+o+" />");n.append(e),s.append(n)}this.selection.restore()}else document.execCommand("indent"),this.selection.save(),this.indent.removeEmpty(),this.indent.normalize(),this.selection.restore()}},decrease:function(){if(this.list.get()){var e=r(this.selection.current()).closest("li"),t=e.closest("ul, ol",this.core.editor()[0]);this.buffer.set(),document.execCommand("outdent");var i=r(this.selection.current()).closest("li",this.core.editor()[0]);if(this.utils.isCollapsed()&&this.indent.repositionItem(i),i.length===0){document.execCommand("formatblock",!1,"p"),i=r(this.selection.current());var s=i.next();s.length!==0&&s[0].tagName==="BR"&&s.remove()}this.selection.save(),this.indent.removeEmpty(),this.indent.normalize(),this.selection.restore()}},repositionItem:function(e){var t=e.next();t.length!==0&&(t[0].tagName!=="UL"||t[0].tagName!=="OL")&&e.append(t);var i=e.prev();if(i.length!==0&&i[0].tagName!=="LI"){this.selection.save();var s=e.parents("li",this.core.editor()[0]);s.after(e),this.selection.restore()}},normalize:function(){this.core.editor().find("li").each(r.proxy(function(e,t){var i=r(t),s="";this.opts.keepStyleAttr.length!==0&&(s=","+this.opts.keepStyleAttr.join(",")),i.find(this.opts.inlineTags.join(",")).not("img, [data-redactor-style-cache], [data-redactor-span]"+s).removeAttr("style");var o=i.parent();if(o.length!==0&&o[0].tagName==="LI"){o.after(i);return}var n=i.next();n.length!==0&&(n[0].tagName==="UL"||n[0].tagName==="OL")&&i.append(n)},this))},removeEmpty:function(e){var t=this.core.editor().find("ul, ol"),i=this.core.editor().find("li");i.each(r.proxy(function(s,o){this.indent.removeItemEmpty(o)},this)),t.each(r.proxy(function(s,o){this.indent.removeItemEmpty(o)},this)),i.each(r.proxy(function(s,o){this.indent.removeItemEmpty(o)},this))},removeItemEmpty:function(e){var t=e.innerHTML.replace(/[\t\s\n]/g,"");t=t.replace(/<span><\/span>/g,""),t===""&&r(e).remove()}}},inline:function(){return{format:function(e,t,i,s){if(!this.utils.isCurrentOrParent(["PRE","CODE"])){var o=this.inline.getParams(t,i,s);e=this.inline.arrangeTag(e),this.placeholder.hide(),this.buffer.set(),this.utils.isCollapsed()?this.inline.formatCollapsed(e,o):this.inline.formatUncollapsed(e,o)}},formatCollapsed:function(e,t){var i,s=this.selection.inline();if(s){var o=s.tagName.toLowerCase();if(o===e)if(this.utils.isEmpty(s.innerHTML))this.caret.after(s),r(s).remove();else{var n=this.inline.insertBreakpoint(s,o);this.caret.after(n)}else if(r(s).closest(e).length===0)i=this.inline.insertInline(e),i=this.inline.setParams(i,t);else{var n=this.inline.insertBreakpoint(s,o);this.caret.after(n)}}else i=this.inline.insertInline(e),i=this.inline.setParams(i,t)},formatUncollapsed:function(e,t){this.selection.save();var i=this.inline.getClearedNodes();this.inline.setNodesStriked(i,e,t),this.selection.restore(),document.execCommand("strikethrough"),this.selection.saveInstant();var s=this;this.core.editor().find("strike").each(function(){var o=s.utils.replaceToTag(this,e);s.inline.setParams(o[0],t);var n=o.find(e),a=o.parent(),c=a.parent();if(c.length!==0&&c[0].tagName.toLowerCase()===e&&c.html()==a[0].outerHTML){o.replaceWith(function(){return r(this).contents()}),c.replaceWith(function(){return r(this).contents()});return}n.length!==0&&s.inline.cleanInsideOrParent(n,t),a.html()==o[0].outerHTML&&s.inline.cleanInsideOrParent(a,t),s.detect.isFirefox()&&s.core.editor().find(e+":empty").remove()}),this.selection.restoreInstant()},cleanInsideOrParent:function(e,t){if(t)for(var i in t.data)this.inline.removeSpecificAttr(e,i,t.data[i])},getClearedNodes:function(){for(var e=this.selection.nodes(),t=[],i=e.length,s=0,o=0;o<i;o++)if(r(e[o]).hasClass("redactor-selection-marker")){s=o+2;break}for(var o=0;o<i;o++)o>=s&&!this.utils.isBlockTag(e[o].tagName)&&t.push(e[o]);return t},isConvertableAttr:function(e,t,i){var s=r(e).attr(t);if(s){if(t==="style"){i=r.trim(i).replace(/;$/,"");for(var o=i.split(";"),n=0,a=0;a<o.length;a++){var c=o[a].split(":"),u=r.trim(c[0]),h=r.trim(c[1]);if(u.search(/color/)!==-1){var g=r(e).css(u);g&&(g===h||this.utils.rgb2hex(g)===h)&&n++}else r(e).css(u)===h&&n++}if(n===o.length)return 1}else if(s===i)return 1}return 0},isConvertable:function(e,t,i,s){if(t===i)if(s){var o=0;for(var n in s.data)o+=this.inline.isConvertableAttr(e,n,s.data[n]);if(o===Object.keys(s.data).length)return!0}else return!0;return!1},setNodesStriked:function(e,t,i){for(var s=0;s<e.length;s++){var o=e[s].tagName?e[s].tagName.toLowerCase():void 0,n=e[s].parentNode,a=n&&n.tagName?n.tagName.toLowerCase():void 0,u=this.inline.isConvertable(n,a,t,i);if(u){var c=r(n).replaceWith(function(){return r("<strike>").append(r(this).contents())});c.attr("data-redactor-inline-converted")}var u=this.inline.isConvertable(e[s],o,t,i);if(u)var c=r(e[s]).replaceWith(function(){return r("<strike>").append(r(this).contents())})}},insertBreakpoint:function(e,t){var i=document.createElement("span");i.id="redactor-inline-breakpoint",i=this.insert.node(i);var s=this.utils.isEndOfElement(e),o=this.utils.getOuterHtml(e),n=s?"":"<"+t+">";o=o.replace(/<span id="redactor-inline-breakpoint"><\/span>/i,"</"+t+">"+n);var a=r(o);return r(e).replaceWith(a),n!==""&&this.utils.cloneAttributes(e,a.last()),a.first()},insertInline:function(e){var t=document.createElement(e);return this.insert.node(t),this.caret.start(t),t},arrangeTag:function(e){var t=["b","bold","i","italic","underline","strikethrough","deleted","superscript","subscript"],i=["strong","strong","em","em","u","del","del","sup","sub"];e=e.toLowerCase();for(var s=0;s<t.length;s++)e===t[s]&&(e=i[s]);return e},getStyleParams:function(e){for(var t={},i=e.trim().replace(/;$/,"").split(";"),s=0;s<i.length;s++){var o=i[s].split(":");o&&(t[o[0].trim()]=o[1].trim())}return t},getParams:function(e,t,i){var s=!1,o="toggle";return typeof e=="object"?(s=e,o=t!==void 0?t:o):e!==void 0&&t!==void 0&&(s={},s[e]=t,o=i!==void 0?i:o),s?{func:o,data:s}:!1},setParams:function(e,t){if(t)for(var i in t.data){var s=r(e);i==="style"?(e=this.inline[t.func+"Style"](t.data[i],e),s.attr("data-redactor-style-cache",s.attr("style"))):i==="class"?e=this.inline[t.func+"Class"](t.data[i],e):e=t.func==="remove"?this.inline[t.func+"Attr"](i,e):this.inline[t.func+"Attr"](i,t.data[i],e),i==="style"&&e.tagName==="SPAN"&&s.attr("data-redactor-span",!0)}return e},eachInline:function(e,t){var i,s=e===void 0?this.selection.inlines():[e];if(s)for(var o=0;o<s.length;o++)i=t(s[o])[0];return i},replaceClass:function(e,t){return this.inline.eachInline(t,function(i){return r(i).removeAttr("class").addClass(e)})},toggleClass:function(e,t){return this.inline.eachInline(t,function(i){return r(i).toggleClass(e)})},addClass:function(e,t){return this.inline.eachInline(t,function(i){return r(i).addClass(e)})},removeClass:function(e,t){return this.inline.eachInline(t,function(i){return r(i).removeClass(e)})},removeAllClass:function(e){return this.inline.eachInline(e,function(t){return r(t).removeAttr("class")})},replaceAttr:function(e,t,i){return this.inline.eachInline(i,function(s){return r(s).removeAttr(e).attr(e.value)})},toggleAttr:function(e,t,i){return this.inline.eachInline(i,function(s){var o=r(s).attr(e);return o?r(s).removeAttr(e):r(s).attr(e.value)})},addAttr:function(e,t,i){return this.inline.eachInline(i,function(s){return r(s).attr(e,t)})},removeAttr:function(e,t){return this.inline.eachInline(t,function(i){var s=r(i);return s.removeAttr(e),e==="style"&&s.removeAttr("data-redactor-style-cache"),s})},removeAllAttr:function(e){return this.inline.eachInline(e,function(t){for(var i=r(t),s=t.attributes.length,o=0;o<s;o++)i.removeAttr(t.attributes[o].name);return i})},removeSpecificAttr:function(e,t,i){var s=r(e);if(t==="style"){var o=i.split(":"),n=o[0].trim();s.css(n,""),this.utils.removeEmptyAttr(e,"style")&&s.removeAttr("data-redactor-style-cache")}else s.removeAttr(t)[0]},hasParentStyle:function(e){var t=e.parent();return t.length===1&&t[0].tagName===e[0].tagName&&t.html()===e[0].outerHTML?t:!1},addParentStyle:function(e){var t=this.inline.hasParentStyle(e);if(t){var i=this.inline.getStyleParams(e.attr("style"));t.css(i),t.attr("data-redactor-style-cache",t.attr("style")),e.replaceWith(function(){return r(this).contents()})}else e.attr("data-redactor-style-cache",e.attr("style"));return e},replaceStyle:function(e,t){e=this.inline.getStyleParams(e);var i=this;return this.inline.eachInline(t,function(s){var o=r(s);o.removeAttr("style").css(e);var n=o.attr("style");return n&&o.attr("style",n.replace(/"/g,"'")),o=i.inline.addParentStyle(o),o})},toggleStyle:function(e,t){e=this.inline.getStyleParams(e);var i=this;return this.inline.eachInline(t,function(s){var o=r(s);for(var n in e){var a=e[n],c=o.css(n);c=i.utils.isRgb(c)?i.utils.rgb2hex(c):c.replace(/"/g,""),a=i.utils.isRgb(a)?i.utils.rgb2hex(a):a.replace(/"/g,""),c===a?o.css(n,""):o.css(n,a)}var u=o.attr("style");return u&&o.attr("style",u.replace(/"/g,"'")),i.utils.removeEmptyAttr(s,"style")?o.removeAttr("data-redactor-style-cache"):o=i.inline.addParentStyle(o),o})},addStyle:function(e,t){e=this.inline.getStyleParams(e);var i=this;return this.inline.eachInline(t,function(s){var o=r(s);o.css(e);var n=o.attr("style");return n&&o.attr("style",n.replace(/"/g,"'")),o=i.inline.addParentStyle(o),o})},removeStyle:function(e,t){e=this.inline.getStyleParams(e);var i=this;return this.inline.eachInline(t,function(s){var o=r(s);for(var n in e)o.css(n,"");return i.utils.removeEmptyAttr(s,"style")?o.removeAttr("data-redactor-style-cache"):o.attr("data-redactor-style-cache",o.attr("style")),o})},removeAllStyle:function(e){return this.inline.eachInline(e,function(t){return r(t).removeAttr("style").removeAttr("data-redactor-style-cache")})},removeStyleRule:function(e){var t=this.selection.parent(),i=this.selection.inlines();this.buffer.set(),t&&t.tagName==="SPAN"&&this.inline.removeStyleRuleAttr(r(t),e);for(var s=0;s<i.length;s++){var o=i[s],n=r(o);r.inArray(o.tagName.toLowerCase(),this.opts.inlineTags)!=-1&&!n.hasClass("redactor-selection-marker")&&this.inline.removeStyleRuleAttr(n,e)}},removeStyleRuleAttr:function(e,t){e.css(t,""),this.utils.removeEmptyAttr(e,"style")?e.removeAttr("data-redactor-style-cache"):e.attr("data-redactor-style-cache",e.attr("style"))},update:function(e,t,i,s){e=this.inline.arrangeTag(e);var o=this.inline.getParams(t,i,s),n=this.selection.inlines(),a=[];if(n)for(var c=0;c<n.length;c++){var u=n[c];(e==="*"||u.tagName.toLowerCase()===e)&&a.push(this.inline.setParams(u,o))}return a},removeFormat:function(){var e=this.selection.inlines();if(e.length===1)for(var t=e[0];t;){if(this.utils.isInlineTag(t.tagName)){if(r(t).hasClass("redactor-in"))return!1;e.push(t)}t=t.parentNode}this.selection.save();for(var i=0;i<e.length;i++)r(e[i]).replaceWith(function(){return r(this).contents()});this.selection.restore()}}},insert:function(){return{set:function(e){this.placeholder.hide(),this.code.set(e),this.focus.end(),this.placeholder.enable()},html:function(e,t){this.placeholder.hide(),this.core.editor().focus();var i=this.selection.block(),s=this.selection.inline();typeof t=="undefined"&&(t=this.clean.getCurrentType(e,!0),e=this.clean.onPaste(e,t,!0)),e=r.parseHTML(e);var o=r(e).last(),n=this.selection.get(),a=this.selection.range(n);if(a.deleteContents(),this.selection.update(n,a),t.lists){var c=r(e);if(c.length!==0&&(c[0].tagName==="UL"||c[0].tagName==="OL")){this.insert.appendLists(i,c);return}}if(t.blocks&&i)if(this.utils.isSelectAll())this.core.editor().html(e),this.focus.end();else{var u=this.utils.breakBlockTag();if(u===!1)this.insert.placeHtml(e);else{var h=r(e).children().last();h.append(this.marker.get()),u.type==="start"?u.$block.before(e):u.$block.after(e),this.selection.restore(),this.core.editor().find("p").each(function(){r.trim(this.innerHTML)===""&&r(this).remove()})}}else{if(s){var g=r("<div/>").html(e);g.find(s.tagName.toLowerCase()).each(function(){r(this).contents().unwrap()}),e=g.html(),e=r.parseHTML(e),o=r(e).last()}if(this.utils.isSelectAll()){var m=r(this.opts.emptyHtml);this.core.editor().html("").append(m),m.html(e),this.caret.end(m)}else this.insert.placeHtml(e)}this.utils.disableSelectAll(),t.pre&&this.clean.cleanPre(),this.caret.end(o),this.linkify.format(!0)},text:function(e){e=e.toString(),e=r.trim(e);var t=document.createElement("div");if(t.innerHTML=e,e=t.textContent||t.innerText,typeof e!="undefined"){this.placeholder.hide(),this.core.editor().focus();var i=this.selection.blocks();if(e=e.replace(/\n/g," "),this.utils.isSelectAll()){var s=r(this.opts.emptyHtml);this.core.editor().html("").append(s),s.html(e),this.caret.end(s)}else{var o=this.selection.get(),n=document.createTextNode(e);if(o.getRangeAt&&o.rangeCount){var a=o.getRangeAt(0);a.deleteContents(),a.insertNode(n),a.setStartAfter(n),a.collapse(!0),this.selection.update(o,a)}i.length>1&&(r(n).wrap("<p>"),this.caret.after(n))}this.utils.disableSelectAll(),this.linkify.format(),this.clean.normalizeCurrentHeading()}},raw:function(e){this.placeholder.hide(),this.core.editor().focus();var t=this.selection.get(),i=this.selection.range(t);i.deleteContents();var s=document.createElement("div");s.innerHTML=e;for(var o=document.createDocumentFragment(),n,a;n=s.firstChild;)a=o.appendChild(n);i.insertNode(o),a&&(i=i.cloneRange(),i.setStartAfter(a),i.collapse(!0),t.removeAllRanges(),t.addRange(i))},node:function(e,t){this.placeholder.hide(),typeof this.start!="undefined"&&this.core.editor().focus(),e=e[0]||e;var i=this.selection.block(),s=this.utils.isBlockTag(e.tagName),o=!0;if(this.utils.isSelectAll())s?this.core.editor().html(e):this.core.editor().html(r("<p>").html(e)),this.code.sync();else if(s&&i){var n=this.utils.breakBlockTag();n===!1?this.insert.placeNode(e,t):(n.type==="start"?n.$block.before(e):n.$block.after(e),this.core.editor().find("p:empty").remove())}else o=this.insert.placeNode(e,t);return this.utils.disableSelectAll(),o&&this.caret.end(e),e},appendLists:function(e,t){var i=r(e),s,o=this.utils.isEmpty(e.innerHTML);if(o||this.utils.isEndOfElement(e))s=i,t.find("li").each(function(){s.after(this),s=r(this)}),o&&i.remove();else if(this.utils.isStartOfElement(e))t.find("li").each(function(){i.before(this),s=r(this)});else{var n=this.selection.extractEndOfNode(e);i.after(r("<li>").append(n)),i.append(t),s=t}this.marker.remove(),s&&this.caret.end(s),this.linkify.format()},placeHtml:function(e){var t=document.createElement("span");t.id="redactor-insert-marker",t=this.insert.node(t),r(t).before(e),this.selection.restore(),this.caret.after(t),r(t).remove()},placeNode:function(e,t){var i=this.selection.get(),s=this.selection.range(i);if(s==null)return!1;t!==!1&&s.deleteContents(),s.insertNode(e),s.collapse(!1),this.selection.update(i,s)},nodeToPoint:function(e,t){if(this.placeholder.hide(),t=t[0]||t,this.utils.isEmpty())return t=this.utils.isBlock(t)?t:r("<p />").append(t),this.core.editor().html(t),t;var i,s=e.clientX,o=e.clientY;if(document.caretPositionFromPoint){var n=document.caretPositionFromPoint(s,o),a=document.getSelection();i=a.getRangeAt(0),i.setStart(n.offsetNode,n.offset),i.collapse(!0),i.insertNode(t)}else if(document.caretRangeFromPoint)i=document.caretRangeFromPoint(s,o),i.insertNode(t);else if(typeof document.body.createTextRange!="undefined"){i=document.body.createTextRange(),i.moveToPoint(s,o);var c=i.duplicate();c.moveToPoint(s,o),i.setEndPoint("EndToEnd",c),i.select()}return t},nodeToCaretPositionFromPoint:function(e,t){this.insert.nodeToPoint(e,t)},marker:function(){this.marker.insert()}}},keydown:function(){return{init:function(e){if(!this.rtePaste){var t=e.which,i=t>=37&&t<=40;this.keydown.ctrl=e.ctrlKey||e.metaKey,this.keydown.parent=this.selection.parent(),this.keydown.current=this.selection.current(),this.keydown.block=this.selection.block(),this.keydown.pre=this.utils.isTag(this.keydown.current,"pre"),this.keydown.blockquote=this.utils.isTag(this.keydown.current,"blockquote"),this.keydown.figcaption=this.utils.isTag(this.keydown.current,"figcaption"),this.keydown.figure=this.utils.isTag(this.keydown.current,"figure");var s=this.core.callback("keydown",e);if(s===!1)return e.preventDefault(),!1;if(this.shortcuts.init(e,t),this.keydown.checkEvents(i,t),this.keydown.setupBuffer(e,t),this.utils.isSelectAll()&&(t===this.keyCode.ENTER||t===this.keyCode.BACKSPACE||t===this.keyCode.DELETE)){e.preventDefault(),this.code.set(this.opts.emptyHtml),this.events.changeHandler();return}if(this.keydown.addArrowsEvent(i),this.keydown.setupSelectAll(e,t),!this.opts.enterKey&&t===this.keyCode.ENTER){e.preventDefault();var o=this.selection.get(),n=this.selection.range(o);n.collapsed||n.deleteContents();return}if(this.opts.enterKey&&t===this.keyCode.DOWN&&this.keydown.onArrowDown(),this.opts.enterKey&&t===this.keyCode.UP&&this.keydown.onArrowUp(),(this.opts.type==="textarea"||this.opts.type==="div")&&this.keydown.current&&this.keydown.current.nodeType===3&&r(this.keydown.parent).hasClass("redactor-in")&&this.keydown.wrapToParagraph(),!this.keyup.lastShiftKey&&t===this.keyCode.SPACE&&(e.ctrlKey||e.shiftKey))return e.preventDefault(),this.keydown.onShiftSpace();if(t===this.keyCode.ENTER&&(e.ctrlKey||e.shiftKey))return e.preventDefault(),this.keydown.onShiftEnter(e);if(t===this.keyCode.ENTER&&!e.shiftKey&&!e.ctrlKey&&!e.metaKey)return this.keydown.onEnter(e);if(t===this.keyCode.TAB||e.metaKey&&t===221||e.metaKey&&t===219)return this.keydown.onTab(e,t);if(this.detect.isFirefox()&&t===this.keyCode.BACKSPACE&&this.keydown.block&&this.keydown.block.tagName==="P"&&this.utils.isStartOfElement(this.keydown.block)){var a=r(this.keydown.block).prev();if(a.length!==0){e.preventDefault(),a.append(this.marker.get()),a.append(r(this.keydown.block).html()),r(this.keydown.block).remove(),this.selection.restore();return}}if(t===this.keyCode.BACKSPACE||t===this.keyCode.DELETE){if(this.observe.image&&typeof this.observe.image!="undefined"&&r("#redactor-image-box").length!==0){e.preventDefault();var a=this.observe.image.closest("figure, p").prev();this.image.remove(!1),this.observe.image=!1,a&&a.length!==0?this.caret.end(a):this.core.editor().focus();return}this.keydown.onBackspaceAndDeleteBefore()}if(t===this.keyCode.DELETE){var c=r(this.keydown.block).next();if(this.utils.isEndOfElement(this.keydown.block)&&c.length!==0&&c[0].tagName==="FIGURE")return c.remove(),!1;var u=this.keydown.block&&this.keydown.block.tagName==="LI"?this.keydown.block:!1;if(u){var h=r(this.keydown.block).parents("ul, ol").last(),g=h.next();if(this.utils.isRedactorParent(h)&&this.utils.isEndOfElement(h)&&g.length!==0&&(g[0].tagName==="UL"||g[0].tagName==="OL"))return e.preventDefault(),h.append(g.contents()),g.remove(),!1}if(this.utils.isEndOfElement(this.keydown.block)&&c.length!==0&&c[0].tagName==="PRE")return r(this.keydown.block).append(c.text()),c.remove(),!1}if(t===this.keyCode.DELETE&&r("#redactor-image-box").length!==0&&this.image.remove(),t===this.keyCode.BACKSPACE){if(this.detect.isFirefox()&&this.line.removeOnBackspace(e),this.list.combineAfterAndBefore(this.keydown.block)){e.preventDefault();return}var m=this.selection.block();if(m&&m.tagName==="LI"&&this.utils.isCollapsed()&&this.utils.isStartOfElement()){this.indent.decrease(),e.preventDefault();return}this.keydown.removeInvisibleSpace(),this.keydown.removeEmptyListInTable(e)}(t===this.keyCode.BACKSPACE||t===this.keyCode.DELETE)&&this.keydown.onBackspaceAndDeleteAfter(e)}},onShiftSpace:function(){return this.buffer.set(),this.insert.raw("&nbsp;"),!1},onShiftEnter:function(e){return this.buffer.set(),this.keydown.pre?this.keydown.insertNewLine(e):this.insert.raw("<br>")},onBackspaceAndDeleteBefore:function(){this.utils.saveScroll()},onBackspaceAndDeleteAfter:function(e){setTimeout(r.proxy(function(){this.code.syncFire=!1,this.keydown.removeEmptyLists();var t="";this.opts.keepStyleAttr.length!==0&&(t=","+this.opts.keepStyleAttr.join(","));var i=this.core.editor().find("*[style]");i.not("img, figure, iframe, #redactor-image-box, #redactor-image-editter, [data-redactor-style-cache], [data-redactor-span]"+t).removeAttr("style"),this.keydown.formatEmpty(e),this.code.syncFire=!0},this),1)},onEnter:function(e){var t=this.core.callback("enter",e);if(t===!1)return e.preventDefault(),!1;if(this.keydown.blockquote&&this.keydown.exitFromBlockquote(e)===!0)return!1;if(this.keydown.pre)return this.keydown.insertNewLine(e);if(this.keydown.blockquote||this.keydown.figcaption)return this.keydown.insertBreakLine(e);if(this.keydown.figure)setTimeout(r.proxy(function(){this.keydown.replaceToParagraph("FIGURE")},this),1);else if(this.keydown.block){if(setTimeout(r.proxy(function(){this.keydown.replaceToParagraph("DIV")},this),1),this.keydown.block.tagName==="LI"){var i=this.selection.current(),s=r(i).closest("li",this.$editor[0]),o=s.parents("ul,ol",this.$editor[0]).last();if(s.length!==0&&this.utils.isEmpty(s.html())&&o.next().length===0&&this.utils.isEmpty(o.find("li").last().html())){o.find("li").last().remove();var n=r(this.opts.emptyHtml);return o.after(n),this.caret.start(n),!1}}}else if(!this.keydown.block)return this.keydown.insertParagraph(e);if(this.detect.isFirefox()&&this.utils.isInline(this.keydown.parent)){this.keydown.insertBreakLine(e);return}this.opts.keepInlineOnEnter||setTimeout(r.proxy(function(){var a=this.selection.inline();if(a&&this.utils.isEmpty(a.innerHTML)){var c=this.selection.block();r(a).remove();var u=document.createRange();u.setStart(c,0);var h=document.createTextNode("\u200B");u.insertNode(h),u.setStartAfter(h),u.collapse(!0);var g=window.getSelection();g.removeAllRanges(),g.addRange(u)}},this),1)},checkEvents:function(e,t){!e&&(this.core.getEvent()==="click"||this.core.getEvent()==="arrow")&&(this.core.addEvent(!1),this.keydown.checkKeyEvents(t)&&this.buffer.set())},checkKeyEvents:function(e){var t=this.keyCode,i=[t.BACKSPACE,t.DELETE,t.ENTER,t.ESC,t.TAB,t.CTRL,t.META,t.ALT,t.SHIFT];return r.inArray(e,i)===-1},addArrowsEvent:function(e){if(e){if(this.core.getEvent()==="click"||this.core.getEvent()==="arrow"){this.core.addEvent(!1);return}this.core.addEvent("arrow")}},setupBuffer:function(e,t){if(this.keydown.ctrl&&t===90&&!e.shiftKey&&!e.altKey&&this.sBuffer.length){e.preventDefault(),this.buffer.undo();return}else if(this.keydown.ctrl&&t===90&&e.shiftKey&&!e.altKey&&this.sRebuffer.length!==0){e.preventDefault(),this.buffer.redo();return}else this.keydown.ctrl||(t===this.keyCode.SPACE||t===this.keyCode.BACKSPACE||t===this.keyCode.DELETE||t===this.keyCode.ENTER&&!e.ctrlKey&&!e.shiftKey)&&this.buffer.set()},exitFromBlockquote:function(e){if(this.utils.isEndOfElement(this.keydown.blockquote)){var t=this.clean.removeSpacesHard(r(this.keydown.blockquote).html());if(t.search(/(<br\s?\/?>){1}$/i)!==-1){e.preventDefault();var i=r(this.keydown.blockquote).children().last();i.filter("br").remove(),r(this.keydown.blockquote).children().last().filter("span").remove();var s=r(this.opts.emptyHtml);return r(this.keydown.blockquote).after(s),this.caret.start(s),!0}}},onArrowDown:function(){for(var e=[this.keydown.blockquote,this.keydown.pre,this.keydown.figcaption],t=0;t<e.length;t++)if(e[t])return this.keydown.insertAfterLastElement(e[t]),!1},onArrowUp:function(){for(var e=[this.keydown.blockquote,this.keydown.pre,this.keydown.figcaption],t=0;t<e.length;t++)if(e[t])return this.keydown.insertBeforeFirstElement(e[t]),!1},insertAfterLastElement:function(e){if(this.utils.isEndOfElement(e)){var t=this.core.editor().contents().last(),i=e.tagName==="FIGCAPTION"?r(this.keydown.block).parent().next():r(this.keydown.block).next();if(i.length===0)if(t.length===0&&t[0]!==e){this.caret.start(t);return}else{var s=r(this.opts.emptyHtml);e.tagName==="FIGCAPTION"?r(e).parent().after(s):r(e).after(s),this.caret.start(s)}}},insertBeforeFirstElement:function(e){if(this.utils.isStartOfElement()&&!(this.core.editor().contents().length>1&&this.core.editor().contents().first()[0]!==e)){var t=r(this.opts.emptyHtml);r(e).before(t),this.caret.start(t)}},onTab:function(e,t){if(!this.opts.tabKey)return!0;var i=this.keydown.block&&this.keydown.block.tagName==="LI";if(this.utils.isEmpty(this.code.get())||!i&&!this.keydown.pre&&this.opts.tabAsSpaces===!1)return!0;e.preventDefault(),this.buffer.set();var s=i&&this.utils.isStartOfElement(this.keydown.block),o;return this.keydown.pre&&!e.shiftKey?(o=this.opts.preSpaces?document.createTextNode(Array(this.opts.preSpaces+1).join("\xA0")):document.createTextNode("	"),this.insert.node(o)):this.opts.tabAsSpaces!==!1&&!s?(o=document.createTextNode(Array(this.opts.tabAsSpaces+1).join("\xA0")),this.insert.node(o)):e.metaKey&&t===219?this.indent.decrease():e.metaKey&&t===221?this.indent.increase():e.shiftKey?this.indent.decrease():this.indent.increase(),!1},setupSelectAll:function(e,t){(e.ctrlKey||e.metaKey)&&!e.altKey&&t===65?(e.preventDefault(),this.utils.enableSelectAll(),this.selection.all()):t!==this.keyCode.LEFT_WIN&&!this.keydown.ctrl&&this.utils.disableSelectAll()},insertNewLine:function(e){e.preventDefault();var t=document.createTextNode(`
`),i=this.selection.get(),s=this.selection.range(i);return s.deleteContents(),s.insertNode(t),this.caret.after(t),!1},insertParagraph:function(e){e.preventDefault();var t=document.createElement("p");t.innerHTML="<br>";var i=this.selection.get(),s=this.selection.range(i);return s.deleteContents(),s.insertNode(t),this.caret.start(t),!1},insertBreakLine:function(e){return this.keydown.insertBreakLineProcessing(e)},insertDblBreakLine:function(e){return this.keydown.insertBreakLineProcessing(e,!0)},insertBreakLineProcessing:function(e,t){e.stopPropagation();var i=document.createElement("br");if(this.insert.node(i),t===!0){var s=document.createElement("br");this.insert.node(s),this.caret.after(s)}else this.caret.after(i);return!1},wrapToParagraph:function(){var e=r(this.keydown.current),t=r("<p>").append(e.clone());e.replaceWith(t);var i=r(t).next();typeof i[0]!="undefined"&&i[0].tagName==="BR"&&i.remove(),this.caret.end(t)},replaceToParagraph:function(e){var t=this.selection.block(),i=r(t).prev(),s=t.innerHTML.replace(/<br\s?\/?>/gi,"");if(t.tagName===e&&this.utils.isEmpty(s)&&!r(t).hasClass("redactor-in")){var o=document.createElement("p");return r(t).replaceWith(o),this.keydown.setCaretToParagraph(o),!1}else{if(t.tagName==="P")return r(t).removeAttr("class").removeAttr("style"),this.detect.isIe()&&this.utils.isEmpty(s)&&this.utils.isInline(this.keydown.parent)&&r(t).on("input",r.proxy(function(){var n=this.selection.parent();if(this.utils.isInline(n)){var a=r(n).html();r(t).html(a),this.caret.end(t)}r(t).off("keyup")},this)),!1;if(i.hasClass(this.opts.videoContainerClass)){i.removeAttr("class");var o=document.createElement("p");return i.replaceWith(o),this.keydown.setCaretToParagraph(o),!1}}},setCaretToParagraph:function(e){var t=document.createRange();t.setStart(e,0);var i=document.createTextNode("\u200B");t.insertNode(i),t.setStartAfter(i),t.collapse(!0);var s=window.getSelection();s.removeAllRanges(),s.addRange(t)},removeInvisibleSpace:function(){var e=r(this.keydown.current);e.text().search(/^\u200B$/g)===0&&e.remove()},removeEmptyListInTable:function(e){var t=r(this.keydown.current),i=r(this.keydown.parent),s=t.closest("td",this.$editor[0]);if(s.length!==0&&t.closest("li",this.$editor[0])&&i.children("li").length===1){if(!this.utils.isEmpty(t.text()))return;e.preventDefault(),t.remove(),i.remove(),this.caret.start(s)}},removeEmptyLists:function(){var e=function(){var t=r.trim(this.innerHTML).replace(/\/t\/n/g,"");t===""&&r(this).remove()};this.core.editor().find("li").each(e),this.core.editor().find("ul, ol").each(e)},formatEmpty:function(e){var t=r.trim(this.core.editor().html());if(this.utils.isEmpty(t))return e.preventDefault(),this.opts.type==="inline"||this.opts.type==="pre"?(this.core.editor().html(this.marker.html()),this.selection.restore()):(this.core.editor().html(this.opts.emptyHtml),this.focus.start()),!1}}},keyup:function(){return{init:function(e){if(!this.rtePaste){var t=e.which;this.keyup.block=this.selection.block(),this.keyup.current=this.selection.current(),this.keyup.parent=this.selection.parent(),this.keyup.lastShiftKey=e.shiftKey;var i=this.core.callback("keyup",e);if(i===!1)return e.preventDefault(),!1;if(t===this.keyCode.ENTER&&this.keyup.block&&this.keyup.block.tagName==="FIGURE"){var s=r(this.keyup.block).prev();if(s.length!==0&&s[0].tagName==="FIGURE"){var o=this.utils.replaceToTag(s,"p");this.caret.start(o);return}}if(t===this.keyCode.BACKSPACE||t===this.keyCode.DELETE){if(this.utils.isSelectAll()){this.focus.start(),this.toolbar.setUnfixed();return}if(this.keyup.block&&this.keydown.block&&this.keyup.block.tagName==="FIGURE"&&this.utils.isStartOfElement(this.keydown.block)){e.preventDefault(),this.selection.save(),r(this.keyup.block).find("figcaption").remove(),r(this.keyup.block).find("img").first().remove(),this.utils.replaceToTag(this.keyup.block,"p");var n=this.marker.find();r("html, body").animate({scrollTop:n.position().top+20},500),this.selection.restore();return}if(this.keyup.block&&this.keyup.block.tagName==="P"){var a=r(this.keyup.block).find("img").length,c=r(this.keyup.block).text().replace(/\u200B/g,"");c===""&&a!==0&&this.utils.replaceToTag(this.keyup.block,"figure")}this.keyup.block&&this.keyup.block.tagName==="FIGURE"&&r(this.keyup.block).find("img").length===0&&(this.selection.save(),this.utils.replaceToTag(this.keyup.block,"p"),this.selection.restore())}this.linkify.isKey(t)&&(this.selection.save(),this.linkify.format(),this.selection.restore())}}}},lang:function(){return{load:function(){this.opts.curLang=this.opts.langs[this.opts.lang]},get:function(e){return typeof this.opts.curLang[e]!="undefined"?this.opts.curLang[e]:""}}},line:function(){return{insert:function(){this.buffer.set(),this.insert.html(this.line.getLineHtml());var e=this.core.editor().find("#redactor-hr-tmp-id");return e.removeAttr("id"),this.core.callback("insertedLine",e),e},getLineHtml:function(){var e='<hr id="redactor-hr-tmp-id" />';return!this.detect.isFirefox()&&this.utils.isEmpty()&&(e+="<p>"+this.opts.emptyHtml+"</p>"),e},removeOnBackspace:function(e){if(this.utils.isCollapsed()){var t=r(this.selection.block());if(!(t.length===0||!this.utils.isStartOfElement(t))){var i=t.prev();i&&i.length!==0&&i[0].tagName==="HR"&&(e.preventDefault(),i.remove())}}}}},link:function(){return{get:function(){return r(this.selection.inlines("a"))},is:function(){var e=this.selection.nodes(),t=r(this.selection.current()).closest("a",this.core.editor()[0]);return t.length===0||e.length>1?!1:t},unlink:function(e){typeof e!="undefined"&&e.preventDefault&&e.preventDefault(),this.buffer.set();var t=this.selection.inlines("a");if(t.length!==0){var i=this.link.replaceLinksToText(t);this.observe.closeAllTooltip(),this.core.callback("deletedLink",i)}},insert:function(e,t){var i=this.link.is();if(t!==!0&&(e=this.link.buildLinkFromObject(i,e),e===!1))return!1;if(this.buffer.set(),e=this.core.callback("beforeInsertingLink",e),i===!1){i=r("<a />"),i=this.link.update(i,e),i=r(this.insert.node(i));var s=i.parent();this.utils.isRedactorParent(s)===!1&&i.wrap("<p>"),s.hasClass("redactor-unlink")&&s.replaceWith(function(){return r(this).contents()}),this.caret.after(i),this.core.callback("insertedLink",i)}else i=this.link.update(i,e),this.caret.after(i);return i},update:function(e,t){return e.text(t.text),e.attr("href",t.url),this.link.target(e,t.target),e},target:function(e,t){return t?e.attr("target","_blank"):e.removeAttr("target")},show:function(e){typeof e!="undefined"&&e.preventDefault&&e.preventDefault(),this.observe.closeAllTooltip();var t=this.link.is();this.link.buildModal(t);var i=this.link.buildLinkFromElement(t);i.url=this.link.removeSelfHostFromUrl(i.url),this.opts.linkNewTab&&!t&&(i.target=!0),this.opts.linkNewTabHide&&r("#redactor-link-blank-section").hide(),this.link.setModalValues(i),this.modal.show(),this.detect.isDesktop()&&r("#redactor-link-url").focus()},setModalValues:function(e){r("#redactor-link-blank").prop("checked",e.target),r("#redactor-link-url").val(e.url),r("#redactor-link-url-text").val(e.text)},buildModal:function(e){this.modal.load("link",this.lang.get(e===!1?"link-insert":"link-edit"),600);var t=this.modal.getActionButton();t.text(this.lang.get(e===!1?"insert":"save")).on("click",r.proxy(this.link.callback,this))},callback:function(){var e=this.link.buildLinkFromModal();if(e===!1)return!1;this.modal.close(),this.link.insert(e,!0)},cleanUrl:function(e){return typeof e=="undefined"?"":r.trim(e.replace(/[^\W\w\D\d+&\'@#/%?=~_|!:,.;\(\)]/gi,""))},cleanText:function(e){return typeof e=="undefined"?"":r.trim(e.replace(/(<([^>]+)>)/gi,""))},getText:function(e){return e.text===""&&e.url!==""?this.link.truncateUrl(e.url.replace(/<|>/g,"")):e.text},isUrl:function(e){var t=/^(?:(?:(?:https?|ftp):)?\/\/)?(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:[/?#]\S*)?$/i;return t.test(e)?e:!1},isMailto:function(e){return e.search("@")!==-1&&/(http|ftp|https):\/\//i.test(e)===!1},isEmpty:function(e){return e.url===""||e.text===""&&e.url===""},truncateUrl:function(e){return e.length>this.opts.linkSize?e.substring(0,this.opts.linkSize)+"...":e},parse:function(e){return this.link.isMailto(e.url)?e.url="mailto:"+e.url.replace("mailto:",""):e.url.search("#")!==0&&this.opts.linkValidation&&(e.url=this.link.isUrl(e.url)&&e.url.search(/^(ftp|https?)/i)===-1?"http://"+e.url.replace(/(ftp|https?):\/\//gi,""):e.url),this.link.isEmpty(e)||e.url===!1?!1:e},buildLinkFromModal:function(){var e={};return e.url=this.link.cleanUrl(r("#redactor-link-url").val()),e.text=this.link.cleanText(r("#redactor-link-url-text").val()),e.text=this.link.getText(e),e.target=!!r("#redactor-link-blank").prop("checked"),this.link.parse(e)},buildLinkFromObject:function(e,t){return t.url=this.link.cleanUrl(t.url),t.text=typeof t.text=="undefined"&&this.selection.is()?this.selection.text():this.link.cleanText(t.text),t.text=this.link.getText(t),t.target=e===!1?t.target:this.link.buildTarget(e),this.link.parse(t)},buildLinkFromElement:function(e){var t={url:"",text:this.selection.is()?this.selection.text():"",target:!1};return e!==!1&&(t.url=e.attr("href"),t.text=e.text(),t.target=this.link.buildTarget(e)),t},buildTarget:function(e){return typeof e.attr("target")!="undefined"&&e.attr("target")==="_blank"},removeSelfHostFromUrl:function(e){var t=self.location.href.replace("#","").replace(/\/$/i,"");return e.replace(/^\/\#/,"#").replace(t,"").replace("mailto:","")},replaceLinksToText:function(e){var t,i=r.each(e,function(s,o){var n=r(o),a=r('<span class="redactor-unlink" />').append(n.contents());return n.replaceWith(a),s===0&&(t=a),n});return e.length===1&&this.selection.isCollapsed()&&this.caret.after(t),i}}},linkify:function(){return{isKey:function(e){return e===this.keyCode.ENTER||e===this.keyCode.SPACE},isLink:function(e){return e.nodeValue.match(this.opts.regexps.linkyoutube)||e.nodeValue.match(this.opts.regexps.linkvimeo)||e.nodeValue.match(this.opts.regexps.linkimage)||e.nodeValue.match(this.opts.regexps.url)},isFiltered:function(e,t){return t.nodeType===3&&r.trim(t.nodeValue)!==""&&!r(t).parent().is("pre")&&this.linkify.isLink(t)},handler:function(e,t){var i=r(t),s=i.text(),o=s;o.match(this.opts.regexps.linkyoutube)||o.match(this.opts.regexps.linkvimeo)?o=this.linkify.convertVideoLinks(o):o.match(this.opts.regexps.linkimage)?o=this.linkify.convertImages(o):o=this.linkify.convertLinks(o),i.before(s.replace(s,o)).remove()},format:function(e){if(!(!this.opts.linkify||this.utils.isCurrentOrParent("pre"))){this.core.editor().find(":not(iframe,img,a,pre,code,.redactor-unlink)").addBack().contents().filter(r.proxy(this.linkify.isFiltered,this)).each(r.proxy(this.linkify.handler,this));var t,i=this.core.editor().find(".redactor-linkify-object").each(r.proxy(function(s,o){return t=r(o),t.removeClass("redactor-linkify-object"),t.attr("class")===""&&t.removeAttr("class"),o.tagName==="A"&&this.core.callback("insertedLink",t),t},this));setTimeout(r.proxy(function(){e===!0&&i.length>0&&this.caret.end(i.last()),this.code.sync(),this.core.callback("linkify",i)},this),100)}},convertVideoLinks:function(e){var t='<div class="'+this.opts.videoContainerClass+' redactor-linkify-object"><iframe class="redactor-linkify-object" width="500" height="281" src="',i='" frameborder="0" allowfullscreen></iframe></div>';return e.match(this.opts.regexps.linkyoutube)&&(e=e.replace(this.opts.regexps.linkyoutube,t+"//www.youtube.com/embed/$1"+i)),e.match(this.opts.regexps.linkvimeo)&&(e=e.replace(this.opts.regexps.linkvimeo,t+"//player.vimeo.com/video/$2"+i)),e},convertImages:function(e){var t=e.match(this.opts.regexps.linkimage);return t?e.replace(e,'<img src="'+t+'"  class="redactor-linkify-object" />'):e},convertLinks:function(e){var t=e.match(this.opts.regexps.url);if(!t)return e;t=r.grep(t,function(g,m){return r.inArray(g,t)===m});for(var i=t.length,s=0;s<i;s++){var o=t[s],n=o,a=o.match(/(https?|ftp):\/\//i)!==null?"":"http://";n.length>this.opts.linkSize&&(n=n.substring(0,this.opts.linkSize)+"..."),n.search("%")===-1&&(n=decodeURIComponent(n));var c="\\b";r.inArray(o.slice(-1),["/","&","="])!==-1&&(c="");var u=new RegExp("("+o.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")+c+")","g"),h="";this.opts.pasteLinkTarget!==!1&&(h=' target="'+this.opts.pasteLinkTarget+'"'),e=e.replace(u,'<a href="'+a+r.trim(o)+'"'+h+' class="redactor-linkify-object">'+r.trim(n)+"</a>")}return e}}},list:function(){return{toggle:function(e){if(!this.utils.inBlocks(["table","td","th","tr"])){e=e==="orderedlist"?"ol":e,e=e==="unorderedlist"?"ul":e,e=e.toLowerCase(),this.buffer.set(),this.selection.save();var t=this.list._getBlocks(),i=this.selection.block(),s=r(i).parents("ul, ol",this.core.editor()[0]).last();return t.length===0&&s.length!==0&&(t=[s.get(0)]),t=this.list._isUnformat(e,t)?this.list._unformat(e,t):this.list._format(e,t),this.selection.restore(),t}},get:function(){var e=this.selection.current(),t=r(e).closest("ul, ol",this.core.editor()[0]);return t.length===0?!1:t},combineAfterAndBefore:function(e){var t=r(e).prev(),i=r(e).next(),s=e&&e.tagName==="P"&&(e.innerHTML==="<br>"||e.innerHTML===""),o=t.closest("ol, ul",this.core.editor()[0]).length===1&&i.closest("ol, ul",this.core.editor()[0]).length===1;return s&&o?(t.children("li").last().append(this.marker.get()),t.append(i.contents()),this.selection.restore(),!0):!1},_getBlocks:function(){for(var e=[],t=this.selection.blocks(),i=0;i<t.length;i++){var s=r(t[i]),o=s.parent().hasClass("redactor-in");o&&e.push(t[i])}return e},_isUnformat:function(e,t){for(var i=0,s=0;s<t.length;s++)if(t[s].nodeType!==3){var o=t[s].tagName.toLowerCase();(o===e||o==="figure")&&i++}return i===t.length},_uniteBlocks:function(e,t){for(var i=0,s={0:[]},o=!1,n=0;n<e.length;n++){var a=r(e[n]),c=a.closest("th, td");c.length!==0?(c.get(0)!==o&&(i++,s[i]=[]),this.list._isUniteBlock(e[n],t)&&s[i].push(e[n])):this.list._isUniteBlock(e[n],t)?s[i].push(e[n]):(i++,s[i]=[]),o=c.get()}return s},_isUniteBlock:function(e,t){return e.nodeType===3||t.indexOf(e.tagName.toLowerCase())!==-1},_createList:function(e,t,i){var s=t[t.length-1],o=r(s),n=r("<"+e+">");return o.after(n),n},_createListItem:function(e){var t=r("<li>");if(e.nodeType===3)t.append(e);else{var i=r(e);t.append(i.contents()),i.remove()}return t},_format:function(e,t){var i=["p","div","blockquote","pre","h1","h2","h3","h4","h5","h6","ul","ol"],s=this.list._uniteBlocks(t,i),o=[];for(var n in s){for(var a=s[n],c=this.list._createList(e,s[n]),u=0;u<a.length;u++){var h;a[u].nodeType!==3&&(a[u].tagName==="UL"||a[u].tagName==="OL")?(h=r(a[u]).contents(),c.append(h)):(h=this.list._createListItem(a[u]),c.append(h))}o.push(c.get(0))}return o},_unformat:function(e,t){if(t.length===1){var i=r(t[0]),s=i.find("li"),o=this.selection.blocks(["li"]),n=this.selection.block(),a=r(n).closest("li");if(o.length===0&&a.length!==0&&(o=[a.get(0)]),o.length===s.length)return this.list._unformatEntire(t[0]);var c=this.list._getItemsPosition(s,o);if(c==="Top")return this.list._unformatAtSide("before",o,i);if(c==="Bottom")return o.reverse(),this.list._unformatAtSide("after",o,i);if(c==="Middle"){var u=r(o[o.length-1]),h=!1,g=!1,m=r("<"+i.get(0).tagName.toLowerCase()+">");s.each(function(j,X){if(h){var q=r(X),U=q.children("ul, ol").length!==0;q.closest(".redactor-split-item").length===0&&(g===!1||q.closest(g).length===0)&&q.addClass("redactor-split-item"),g=q}X===u.get(0)&&(h=!0)}),s.filter(".redactor-split-item").each(function(j,X){var q=r(X);q.removeClass("redactor-split-item"),m.append(X)}),i.after(m),o.reverse();for(var E=0;E<o.length;E++){var A=r(o[E]),J=this.list._createUnformatContainer(A);i.after(J),J.find("ul, ol").remove(),A.remove()}return}}else for(var E=0;E<t.length;E++)t[E].nodeType!==3&&t[E].tagName.toLowerCase()===e&&this.list._unformatEntire(t[E])},_unformatEntire:function(e){var t=r(e),i=t.find("li");i.each(function(s,o){var n=r(o),a=this.list._createUnformatContainer(n);n.remove(),t.before(a)}.bind(this)),t.remove()},_unformatAtSide:function(e,t,i){for(var s=0;s<t.length;s++){var o=r(t[s]),n=this.list._createUnformatContainer(o);i[e](n);var a=n.find("ul, ol").first();o.append(a),a.each(function(c,u){var h=r(u),g=h.closest("li");g.get(0)===t[c]&&(h.unwrap(),g.addClass("r-unwrapped"))}),this.utils.isEmpty(o.html())&&o.remove()}i.find(".r-unwrapped").each(function(c){var u=r(c);u.html().trim()===""?u.remove():u.removeClass("r-unwrapped")})},_getItemsPosition:function(e,t){var i="Middle",s=t[0],o=t[t.length-1],n=e.first().get(0),a=e.last().get(0);return n===s&&a!==o?i="Top":n!==s&&a===o&&(i="Bottom"),i},_createUnformatContainer:function(e){var t=r("<p>");return t.append(e.contents()),t}}},marker:function(){return{get:function(e){e=typeof e=="undefined"?1:e;var t=document.createElement("span");return t.id="selection-marker-"+e,t.className="redactor-selection-marker",t.innerHTML=this.opts.invisibleSpace,t},html:function(e){return this.utils.getOuterHtml(this.marker.get(e))},find:function(e){return e=typeof e=="undefined"?1:e,this.core.editor().find("span#selection-marker-"+e)},insert:function(){var e=this.selection.get(),t=this.selection.range(e);this.marker.insertNode(t,this.marker.get(1),!0),t&&t.collapsed===!1&&this.marker.insertNode(t,this.marker.get(2),!1)},remove:function(){this.core.editor().find(".redactor-selection-marker").each(this.marker.iterateRemove)},insertNode:function(e,t,i){var s=this.selection.parent();if(!(e===null||r(s).closest(".redactor-in").length===0)){e=e.cloneRange();try{e.collapse(i),e.insertNode(t)}catch(o){this.focus.start()}}},iterateRemove:function(e,t){var i=r(t),s=i.text().replace(/\u200B/g,""),o=i.parent()[0];s===""?i.remove():i.replaceWith(function(){return r(this).contents()})}}},modal:function(){return{callbacks:{},templates:function(){this.opts.modal={"image-edit":'<div class="redactor-modal-tab redactor-group" data-title="General"><div id="redactor-image-preview" class="redactor-modal-tab-side"></div><div class="redactor-modal-tab-area"><section><label>'+this.lang.get("title")+'</label><input type="text" id="redactor-image-title" /></section><section><label>'+this.lang.get("caption")+'</label><input type="text" id="redactor-image-caption" aria-label="'+this.lang.get("caption")+'" /></section><section><label>'+this.lang.get("link")+'</label><input type="text" id="redactor-image-link" aria-label="'+this.lang.get("link")+'" /></section><section><label class="redactor-image-position-option">'+this.lang.get("image-position")+'</label><select class="redactor-image-position-option" id="redactor-image-align" aria-label="'+this.lang.get("image-position")+'"><option value="none">'+this.lang.get("none")+'</option><option value="left">'+this.lang.get("left")+'</option><option value="center">'+this.lang.get("center")+'</option><option value="right">'+this.lang.get("right")+'</option></select></section><section><label class="checkbox"><input type="checkbox" id="redactor-image-link-blank" aria-label="'+this.lang.get("link-in-new-tab")+'"> '+this.lang.get("link-in-new-tab")+'</label></section><section><button id="redactor-modal-button-action">'+this.lang.get("insert")+'</button><button id="redactor-modal-button-cancel">'+this.lang.get("cancel")+'</button><button id="redactor-modal-button-delete" class="redactor-modal-button-offset">'+this.lang.get("delete")+"</button></section></div></div>",image:'<div class="redactor-modal-tab" data-title="Upload"><section><div id="redactor-modal-image-droparea"></div></section></div>',file:'<div class="redactor-modal-tab" data-title="Upload"><section><label>'+this.lang.get("filename")+' <span class="desc">('+this.lang.get("optional")+')</span></label><input type="text" id="redactor-filename" aria-label="'+this.lang.get("filename")+'" /><br><br></section><section><div id="redactor-modal-file-upload"></div></section></div>',link:'<div class="redactor-modal-tab" data-title="General"><section><label>URL</label><input type="url" id="redactor-link-url" aria-label="URL" /></section><section><label>'+this.lang.get("text")+'</label><input type="text" id="redactor-link-url-text" aria-label="'+this.lang.get("text")+'" /></section><section id="redactor-link-blank-section"><label class="checkbox"><input type="checkbox" id="redactor-link-blank"> '+this.lang.get("link-in-new-tab")+'</label></section><section><button id="redactor-modal-button-action">'+this.lang.get("insert")+'</button><button id="redactor-modal-button-cancel">'+this.lang.get("cancel")+"</button></section></div>"},r.extend(this.opts,this.opts.modal)},addCallback:function(e,t){this.modal.callbacks[e]=t},addTemplate:function(e,t){this.opts.modal[e]=t},getTemplate:function(e){return this.opts.modal[e]},getModal:function(){return this.$modalBody},getActionButton:function(){return this.$modalBody.find("#redactor-modal-button-action")},getCancelButton:function(){return this.$modalBody.find("#redactor-modal-button-cancel")},getDeleteButton:function(){return this.$modalBody.find("#redactor-modal-button-delete")},load:function(e,t,i){typeof this.$modalBox!="undefined"&&this.$modalBox.hasClass("open")||(this.modal.templateName=e,this.modal.width=i,this.modal.build(),this.modal.enableEvents(),this.modal.setTitle(t),this.modal.setDraggable(),this.modal.setContent(),typeof this.modal.callbacks[e]!="undefined"&&this.modal.callbacks[e].call(this))},show:function(){this.detect.isDesktop()||document.activeElement.blur(),this.selection.save(),this.modal.buildTabber(),this.detect.isMobile()&&(this.modal.width="96%"),setTimeout(r.proxy(this.modal.buildWidth,this),0),r(window).on("resize.redactor-modal",r.proxy(this.modal.buildWidth,this)),this.$modalOverlay.redactorAnimation("fadeIn",{duration:.25}),this.$modalBox.addClass("open").show(),this.$modal.redactorAnimation("fadeIn",{timing:"cubic-bezier(0.175, 0.885, 0.320, 1.105)"},r.proxy(function(){this.utils.saveScroll(),this.utils.disableBodyScroll(),this.core.callback("modalOpened",this.modal.templateName,this.$modal),r(document).off("focusin.modal");var e=this.$modal.find("input[type=text],input[type=url],input[type=email]");e.on("keydown.redactor-modal",r.proxy(this.modal.setEnter,this))},this))},buildWidth:function(){var e=r(window).height(),t=r(window).width(),i=typeof this.modal.width=="number";!i&&this.modal.width.match(/%$/)?this.$modal.css({width:this.modal.width,"margin-bottom":"16px"}):parseInt(this.modal.width)>t?this.$modal.css({width:"96%","margin-bottom":"2%"}):(i&&(this.modal.width+="px"),this.$modal.css({width:this.modal.width,"margin-bottom":"16px"}));var s=this.$modal.outerHeight(),o=e/2-s/2+"px";this.detect.isMobile()?o="2%":s>e&&(o="16px"),this.$modal.css("margin-top",o)},buildTabber:function(){this.modal.tabs=this.$modal.find(".redactor-modal-tab"),!(this.modal.tabs.length<2)&&(this.modal.$tabsBox=r('<div id="redactor-modal-tabber" />'),r.each(this.modal.tabs,r.proxy(function(e,t){var i=r('<a href="#" rel="'+e+'" />').text(r(t).attr("data-title"));i.on("click",r.proxy(this.modal.showTab,this)),e===0&&i.addClass("active"),this.modal.$tabsBox.append(i)},this)),this.$modalBody.prepend(this.modal.$tabsBox))},showTab:function(e){e.preventDefault();var t=r(e.target),i=t.attr("rel");return this.modal.tabs.hide(),this.modal.tabs.eq(i).show(),r("#redactor-modal-tabber").find("a").removeClass("active"),t.addClass("active"),!1},setTitle:function(e){this.$modalHeader.html(e)},setContent:function(){this.$modalBody.html(this.modal.getTemplate(this.modal.templateName)),this.modal.getCancelButton().on("mousedown",r.proxy(this.modal.close,this))},setDraggable:function(){typeof r.fn.draggable!="undefined"&&(this.$modal.draggable({handle:this.$modalHeader}),this.$modalHeader.css("cursor","move"))},setEnter:function(e){e.which===13&&(e.preventDefault(),this.modal.getActionButton().click())},build:function(){this.modal.buildOverlay(),this.$modalBox=r('<div id="redactor-modal-box"/>').hide(),this.$modal=r('<div id="redactor-modal" role="dialog" />'),this.$modalHeader=r('<div id="redactor-modal-header" />'),this.$modalClose=r('<button type="button" id="redactor-modal-close" aria-label="'+this.lang.get("close")+'" />').html("&times;"),this.$modalBody=r('<div id="redactor-modal-body" />'),this.$modal.append(this.$modalHeader),this.$modal.append(this.$modalBody),this.$modal.append(this.$modalClose),this.$modalBox.append(this.$modal),this.$modalBox.appendTo(document.body)},buildOverlay:function(){this.$modalOverlay=r('<div id="redactor-modal-overlay">').hide(),r("body").prepend(this.$modalOverlay)},enableEvents:function(){this.$modalClose.on("mousedown.redactor-modal",r.proxy(this.modal.close,this)),r(document).on("keyup.redactor-modal",r.proxy(this.modal.closeHandler,this)),this.core.editor().on("keyup.redactor-modal",r.proxy(this.modal.closeHandler,this)),this.$modalBox.on("click.redactor-modal",r.proxy(this.modal.close,this))},disableEvents:function(){this.$modalClose.off("mousedown.redactor-modal"),r(document).off("keyup.redactor-modal"),this.core.editor().off("keyup.redactor-modal"),this.$modalBox.off("click\xA7.redactor-modal"),r(window).off("resize.redactor-modal")},closeHandler:function(e){e.which===this.keyCode.ESC&&this.modal.close(!1)},close:function(e){if(e){if(r(e.target).attr("id")!=="redactor-modal-button-cancel"&&e.target!==this.$modalClose[0]&&e.target!==this.$modalBox[0])return;e.preventDefault()}this.$modalBox&&(this.selection.restore(),this.modal.disableEvents(),this.utils.enableBodyScroll(),this.utils.restoreScroll(),this.$modalOverlay.redactorAnimation("fadeOut",{duration:.4},r.proxy(function(){this.$modalOverlay.remove()},this)),this.$modal.redactorAnimation("fadeOut",{duration:.3,timing:"cubic-bezier(0.175, 0.885, 0.320, 1.175)"},r.proxy(function(){typeof this.$modalBox!="undefined"&&(this.$modalBox.remove(),this.$modalBox=void 0),this.core.callback("modalClosed",this.modal.templateName)},this)))}}},observe:function(){return{load:function(){typeof this.opts.destroyed=="undefined"&&(this.observe.links(),this.observe.images())},isCurrent:function(e,t){return typeof t=="undefined"&&(t=r(this.selection.current())),t.is(e)||t.parents(e).length>0},toolbar:function(){this.observe.buttons(),this.observe.dropdowns()},buttons:function(e,t){var i=this.selection.current(),s=this.selection.parent();if(e!==!1?this.button.setInactiveAll():this.button.setInactiveAll(t),e===!1&&t!=="html"){r.inArray(t,this.opts.activeButtons)!==-1&&this.button.toggleActive(t);return}this.utils.isRedactorParent(i)&&(this.core.editor().css("display")!=="none"&&(this.utils.isCurrentOrParentHeader()||this.utils.isCurrentOrParent(["table","pre","blockquote","li"])?this.button.disable("horizontalrule"):this.button.enable("horizontalrule")),r.each(this.opts.activeButtonsStates,r.proxy(function(o,n){var a=r(s).closest(o,this.$editor[0]),c=r(i).closest(o,this.$editor[0]);a.length!==0&&!this.utils.isRedactorParent(a)||this.utils.isRedactorParent(c)&&(a.length!==0||c.closest(o,this.$editor[0]).length!==0)&&this.button.setActive(n)},this)))},dropdowns:function(){var e=r("<div />").html(this.selection.html()).find("a").length,t=r(this.selection.current()),i=this.utils.isRedactorParent(t);r.each(this.opts.observe.dropdowns,r.proxy(function(s,o){var n=o.observe,a=n.element,c=o.item,u=typeof n.in!="undefined"?n.in:!1,h=typeof n.out!="undefined"?n.out:!1;t.closest(a).length>0&&i||a==="a"&&e!==0?this.observe.setDropdownProperties(c,u,h):this.observe.setDropdownProperties(c,h,u)},this))},setDropdownProperties:function(e,t,i){i&&typeof i.attr!="undefined"&&this.observe.setDropdownAttr(e,i.attr,!0),typeof t.attr!="undefined"&&this.observe.setDropdownAttr(e,t.attr),typeof t.title!="undefined"&&e.find("span").text(t.title)},setDropdownAttr:function(e,t,i){r.each(t,function(s,o){s==="class"?i?e.removeClass(o):e.addClass(o):i?e.removeAttr(s):e.attr(s,o)})},addDropdown:function(e,t,i){typeof i.observe!="undefined"&&(i.item=e,this.opts.observe.dropdowns.push(i))},images:function(){this.opts.imageEditable&&(this.core.editor().addClass("redactor-layer-img-edit"),this.core.editor().find("img").each(r.proxy(function(e,t){var i=r(t);i.closest("a",this.$editor[0]).on("click",function(s){s.preventDefault()}),this.image.setEditable(i)},this)))},links:function(){this.opts.linkTooltip&&this.core.editor().find("a").each(r.proxy(function(e,t){var i=r(t);i.data("cached")!==!0&&(i.data("cached",!0),i.on("touchstart.redactor."+this.uuid+" click.redactor."+this.uuid,r.proxy(this.observe.showTooltip,this)))},this))},getTooltipPosition:function(e){return e.offset()},showTooltip:function(e){var t=r(e.target);if(t[0].tagName!=="IMG"&&(t[0].tagName!=="A"&&(t=t.closest("a",this.$editor[0])),t[0].tagName==="A")){var i=t,s=this.observe.getTooltipPosition(i),o=r('<span class="redactor-link-tooltip"></span>'),n=i.attr("href");n===void 0&&(n=""),n.length>24&&(n=n.substring(0,24)+"...");var a=r('<a href="'+i.attr("href")+'" target="_blank" />').html(n).addClass("redactor-link-tooltip-action"),c=r('<a href="#" />').html(this.lang.get("edit")).on("click",r.proxy(this.link.show,this)).addClass("redactor-link-tooltip-action"),u=r('<a href="#" />').html(this.lang.get("unlink")).on("click",r.proxy(this.link.unlink,this)).addClass("redactor-link-tooltip-action");o.append(a).append(" | ").append(c).append(" | ").append(u);var h=parseInt(i.css("line-height"),10),g=Math.ceil((e.pageY-s.top)/h),m=s.top+g*h;o.css({top:m+"px",left:s.left+"px"}),r(".redactor-link-tooltip").remove(),r("body").append(o),this.core.editor().on("touchstart.redactor."+this.uuid+" click.redactor."+this.uuid,r.proxy(this.observe.closeTooltip,this)),r(document).on("touchstart.redactor."+this.uuid+" click.redactor."+this.uuid,r.proxy(this.observe.closeTooltip,this))}},closeAllTooltip:function(){r(".redactor-link-tooltip").remove()},closeTooltip:function(e){e=e.originalEvent||e;var t=e.target,i=r(t).closest("a",this.$editor[0]);i.length!==0&&i[0].tagName==="A"&&t.tagName!=="A"||t.tagName==="A"&&this.utils.isRedactorParent(t)||r(t).hasClass("redactor-link-tooltip-action")||(this.observe.closeAllTooltip(),this.core.editor().off("touchstart.redactor."+this.uuid+" click.redactor."+this.uuid,r.proxy(this.observe.closeTooltip,this)),r(document).off("touchstart.redactor."+this.uuid+" click.redactor."+this.uuid,r.proxy(this.observe.closeTooltip,this)))}}},offset:function(){return{get:function(e){var t=this.offset.clone(e);if(t===!1)return 0;var i=document.createElement("div");i.appendChild(t.cloneContents()),i.innerHTML=i.innerHTML.replace(/<img(.*?[^>])>$/gi,"i");var s=r.trim(r(i).text()).replace(/[\t\n\r\n]/g,"").replace(/\u200B/g,"");return s.length},clone:function(e){var t=this.selection.get(),i=this.selection.range(t);if(i===null&&typeof e=="undefined"||(e=typeof e=="undefined"?this.$editor:e,e===!1))return!1;e=e[0]||e;var s=i.cloneRange();return s.selectNodeContents(e),s.setEnd(i.endContainer,i.endOffset),s},set:function(e,t){t=typeof t=="undefined"?e:t,this.focus.is()||this.focus.start();for(var i=this.selection.get(),s=this.selection.range(i),o,n=0,a=document.createTreeWalker(this.$editor[0],NodeFilter.SHOW_TEXT,null,null);(o=a.nextNode())!==null;)if(n+=o.nodeValue.length,n>e&&(s.setStart(o,o.nodeValue.length+e-n),e=1/0),n>=t){s.setEnd(o,o.nodeValue.length+t-n);break}s.collapse(!1),this.selection.update(i,s)}}},paragraphize:function(){return{load:function(e){return this.opts.paragraphize===!1||this.opts.type==="inline"||this.opts.type==="pre"?e:e===""||e==="<p></p>"?this.opts.emptyHtml:(e=e+`
`,this.paragraphize.safes=[],this.paragraphize.z=0,e=e.replace(/(<br\s?\/?>){1,}\n?<\/blockquote>/gi,"</blockquote>"),e=e.replace(/<\/pre>/gi,`</pre>

`),e=e.replace(/<p>\s<br><\/p>/gi,"<p></p>"),e=this.paragraphize.getSafes(e),e=e.replace("<br>",`
`),e=this.paragraphize.convert(e),e=this.paragraphize.clear(e),e=this.paragraphize.restoreSafes(e),e=e.replace(new RegExp(`<br\\s?/?>
?<(`+this.opts.paragraphizeBlocks.join("|")+")(.*?[^>])>","gi"),`<p><br /></p>
<$1$2>`),r.trim(e))},getSafes:function(e){var t=r("<div />").append(e);return t.find("blockquote p").replaceWith(function(){return r(this).append("<br />").contents()}),t.find(this.opts.paragraphizeBlocks.join(", ")).each(r.proxy(function(i,s){return this.paragraphize.z++,this.paragraphize.safes[this.paragraphize.z]=s.outerHTML,r(s).replaceWith(`
#####replace`+this.paragraphize.z+`#####

`)},this)),t.find("span.redactor-selection-marker").each(r.proxy(function(i,s){return this.paragraphize.z++,this.paragraphize.safes[this.paragraphize.z]=s.outerHTML,r(s).replaceWith(`
#####replace`+this.paragraphize.z+`#####

`)},this)),t.html()},restoreSafes:function(e){return r.each(this.paragraphize.safes,function(t,i){i=typeof i!="undefined"?i.replace(/\$/g,"&#36;"):i,e=e.replace("#####replace"+t+"#####",i)}),e},convert:function(e){e=e.replace(/\r\n/g,"xparagraphmarkerz"),e=e.replace(/\n/g,"xparagraphmarkerz"),e=e.replace(/\r/g,"xparagraphmarkerz");var t=/\s+/g;e=e.replace(t," "),e=r.trim(e);var i=/xparagraphmarkerzxparagraphmarkerz/gi;e=e.replace(i,"</p><p>");var s=/xparagraphmarkerz/gi;return e=e.replace(s,"<br>"),e="<p>"+e+"</p>",e=e.replace("<p></p>",""),e=e.replace(`\r
\r
`,""),e=e.replace(/<\/p><p>/g,`</p>\r
\r
<p>`),e=e.replace(new RegExp("<br\\s?/?></p>","g"),"</p>"),e=e.replace(new RegExp("<p><br\\s?/?>","g"),"<p>"),e=e.replace(new RegExp("<p><br\\s?/?>","g"),"<p>"),e=e.replace(new RegExp("<br\\s?/?></p>","g"),"</p>"),e=e.replace(/<p>&nbsp;<\/p>/gi,""),e=e.replace(/<p>\s?<br>&nbsp;<\/p>/gi,""),e=e.replace(/<p>\s?<br>/gi,"<p>"),e},clear:function(e){return e=e.replace(/<p>(.*?)#####replace(.*?)#####\s?<\/p>/gi,"<p>$1</p>#####replace$2#####"),e=e.replace(/(<br\s?\/?>){2,}<\/p>/gi,"</p>"),e=e.replace(new RegExp("</blockquote></p>","gi"),"</blockquote>"),e=e.replace(new RegExp("<p></blockquote>","gi"),"</blockquote>"),e=e.replace(new RegExp("<p><blockquote>","gi"),"<blockquote>"),e=e.replace(new RegExp("<blockquote></p>","gi"),"<blockquote>"),e=e.replace(new RegExp("<p><p ","gi"),"<p "),e=e.replace(new RegExp("<p><p>","gi"),"<p>"),e=e.replace(new RegExp("</p></p>","gi"),"</p>"),e=e.replace(new RegExp("<p>\\s?</p>","gi"),""),e=e.replace(new RegExp(`
</p>`,"gi"),"</p>"),e=e.replace(new RegExp(`<p>	?	?
?<p>`,"gi"),"<p>"),e=e.replace(new RegExp("<p>	*</p>","gi"),""),e}}},paste:function(){return{init:function(e){this.rtePaste=!0;var t=!!(this.opts.type==="pre"||this.utils.isCurrentOrParent("pre"));if(this.detect.isDesktop()&&!this.paste.pre&&this.opts.clipboardImageUpload&&this.opts.imageUpload&&this.paste.detectClipboardUpload(e)){this.detect.isIe()&&setTimeout(r.proxy(this.paste.clipboardUpload,this),100);return}this.utils.saveScroll(),this.selection.save(),this.paste.createPasteBox(t),r(window).on("scroll.redactor-freeze",r.proxy(function(){r(window).scrollTop(this.saveBodyScroll)},this));var i;t&&this.detect.isFirefox()&&(e=e.originalEvent||e,e.preventDefault(),i=e.clipboardData.getData("text/plain")),setTimeout(r.proxy(function(){var s=i||this.paste.getPasteBoxCode(t);this.buffer.set(),this.selection.restore(),this.utils.restoreScroll();var o=this.clean.getCurrentType(s);s=this.clean.onPaste(s,o);var n=this.core.callback("paste",s);s=typeof n=="undefined"?s:n,this.paste.insert(s,o),this.rtePaste=!1,t&&this.clean.cleanPre(),r(window).off("scroll.redactor-freeze")},this),1)},getPasteBoxCode:function(e){var t=e?this.$pasteBox.val():this.$pasteBox.html();return this.$pasteBox.remove(),t},createPasteBox:function(e){var t={position:"fixed",width:"1px",top:0,left:"-9999px"};this.$pasteBox=e?r("<textarea>").css(t):r("<div>").attr("contenteditable","true").css(t),this.paste.appendPasteBox(),this.$pasteBox.focus()},appendPasteBox:function(){if(this.detect.isIe())this.core.box().append(this.$pasteBox);else{var e=r(".modal-body:visible");e.length>0?e.append(this.$pasteBox):r("body").prepend(this.$pasteBox)}},detectClipboardUpload:function(e){e=e.originalEvent||e;var t=e.clipboardData;if(this.detect.isIe()||this.detect.isFirefox())return!1;var i=t.types;if(i.indexOf("public.tiff")!==-1)return e.preventDefault(),!1;if(!(!t.items||!t.items.length)){var s=t.items[0].getAsFile();if(s===null)return!1;var o=new FileReader;return o.readAsDataURL(s),o.onload=r.proxy(this.paste.insertFromClipboard,this),!0}},clipboardUpload:function(){var e=this.$editor.find("img");r.each(e,r.proxy(function(t,i){if(i.src.search(/^data\:image/i)!==-1){var s=window.FormData?new FormData:null;if(window.FormData){this.upload.direct=!0,this.upload.type="image",this.upload.url=this.opts.imageUpload,this.upload.callback=r.proxy(function(n){if(this.detect.isIe())r(i).wrap(r("<figure />"));else{var a=r(i).parent();this.utils.replaceToTag(a,"figure")}i.src=n.url,this.core.callback("imageUpload",r(i),n)},this);var o=this.utils.dataURItoBlob(i.src);s.append("clipboard",1),s.append(this.opts.imageUploadParam,o),this.progress.show(),this.upload.send(s,!1),this.code.sync(),this.rtePaste=!1}}},this))},insertFromClipboard:function(e){var t=window.FormData?new FormData:null;if(window.FormData){this.upload.direct=!0,this.upload.type="image",this.upload.url=this.opts.imageUpload,this.upload.callback=this.image.insert;var i=this.utils.dataURItoBlob(e.target.result);t.append("clipboard",1),t.append(this.opts.imageUploadParam,i),this.progress.show(),this.upload.send(t,e),this.rtePaste=!1}},insert:function(e,t){t.pre?this.insert.raw(e):t.text?this.insert.text(e):this.insert.html(e,t),this.detect.isFirefox()&&this.opts.imageUpload&&this.opts.clipboardImageUpload&&setTimeout(r.proxy(this.paste.clipboardUpload,this),100)}}},placeholder:function(){return{enable:function(){setTimeout(r.proxy(function(){return this.placeholder.isEditorEmpty()?this.placeholder.show():this.placeholder.hide()},this),5)},show:function(){this.core.editor().addClass("redactor-placeholder")},update:function(e){this.opts.placeholder=e,this.core.editor().attr("placeholder",e)},hide:function(){this.core.editor().removeClass("redactor-placeholder")},is:function(){return this.core.editor().hasClass("redactor-placeholder")},init:function(){this.placeholder.enabled()&&(this.utils.isEditorRelative()||this.utils.setEditorRelative(),this.placeholder.build(),this.placeholder.buildPosition(),this.placeholder.enable(),this.placeholder.enableEvents())},enabled:function(){return this.opts.placeholder?this.core.element().attr("placeholder",this.opts.placeholder):this.placeholder.isAttr()},enableEvents:function(){this.core.editor().on("keydown.redactor-placeholder."+this.uuid,r.proxy(this.placeholder.enable,this))},disableEvents:function(){this.core.editor().off(".redactor-placeholder."+this.uuid)},build:function(){this.core.editor().attr("placeholder",this.core.element().attr("placeholder"))},buildPosition:function(){var e=r("<style />");e.addClass("redactor-placeholder-style-tag"),e.html("#"+this.core.id()+".redactor-placeholder::after "+this.placeholder.getPosition()),r("head").append(e)},getPosition:function(){return"{ top: "+this.core.editor().css("padding-top")+"; left: "+this.core.editor().css("padding-left")+"; }"},isEditorEmpty:function(){var e=r.trim(this.core.editor().html()).replace(/[\t\n]/g,""),t=["","<p>\u200B</p>","<p>\u200B<br></p>",this.opts.emptyHtmlRendered];return r.inArray(e,t)!==-1},isAttr:function(){return typeof this.core.element().attr("placeholder")!="undefined"&&this.core.element().attr("placeholder")!==""},destroy:function(){this.core.editor().removeAttr("placeholder"),this.placeholder.hide(),this.placeholder.disableEvents(),r(".redactor-placeholder-style-tag").remove()}}},progress:function(){return{$box:null,$bar:null,target:document.body,show:function(){this.progress.is()?this.progress.$box.show():(this.progress.build(),this.progress.$box.redactorAnimation("fadeIn"))},hide:function(){this.progress.is()&&this.progress.$box.redactorAnimation("fadeOut",{duration:.35},r.proxy(this.progress.destroy,this))},update:function(e){this.progress.show(),this.progress.$bar.css("width",e+"%")},is:function(){return this.progress.$box!==null},build:function(){this.progress.$bar=r("<span />"),this.progress.$box=r('<div id="redactor-progress" />'),this.progress.$box.append(this.progress.$bar),r(this.progress.target).append(this.progress.$box)},destroy:function(){this.progress.is()&&this.progress.$box.remove(),this.progress.$box=null,this.progress.$bar=null}}},selection:function(){return{get:function(){return window.getSelection?window.getSelection():document.selection&&document.selection.type!=="Control"?document.selection:null},range:function(e){return typeof e=="undefined"&&(e=this.selection.get()),e.getRangeAt&&e.rangeCount?e.getRangeAt(0):null},is:function(){return!this.selection.isCollapsed()},isRedactor:function(){var e=this.selection.range();if(e!==null){var t=e.startContainer.parentNode;if(r(t).hasClass("redactor-in")||r(t).parents(".redactor-in").length!==0)return!0}return!1},isCollapsed:function(){var e=this.selection.get();return e===null?!1:e.isCollapsed},update:function(e,t){t!==null&&(e.removeAllRanges(),e.addRange(t))},current:function(){var e=this.selection.get();return e===null?!1:e.anchorNode},parent:function(){var e=this.selection.current();return e===null?!1:e.parentNode},block:function(e){for(e=e||this.selection.current();e;){if(this.utils.isBlockTag(e.tagName))return r(e).hasClass("redactor-in")?!1:e;e=e.parentNode}return!1},inline:function(e){for(e=e||this.selection.current();e;){if(this.utils.isInlineTag(e.tagName))return r(e).hasClass("redactor-in")?!1:e;e=e.parentNode}return!1},element:function(e){for(e||(e=this.selection.current());e;){if(e.nodeType===1)return r(e).hasClass("redactor-in")?!1:e;e=e.parentNode}return!1},prev:function(){var e=this.selection.current();return e===null?!1:this.selection.current().previousSibling},next:function(){var e=this.selection.current();return e===null?!1:this.selection.current().nextSibling},blocks:function(e){var t=[],i=this.selection.nodes(e);r.each(i,r.proxy(function(o,n){this.utils.isBlock(n)&&t.push(n)},this));var s=this.selection.block();return t.length===0&&s===!1?[]:t.length===0&&s!==!1?[s]:t},inlines:function(e){var t=[],i=this.selection.nodes(e);r.each(i,r.proxy(function(o,n){this.utils.isInline(n)&&t.push(n)},this));var s=this.selection.inline();return t.length===0&&s===!1?[]:t.length===0&&s!==!1?[s]:t},nodes:function(e){var t=typeof e=="undefined"?[]:r.isArray(e)?e:[e],i=this.selection.get(),s=this.selection.range(i),o=[],n=[];if(this.utils.isCollapsed())o=[this.selection.current()];else{var a=s.startContainer,c=s.endContainer;if(a===c)return[a];for(;a&&a!==c;)o.push(a=this.selection.nextNode(a));for(a=s.startContainer;a&&a!==s.commonAncestorContainer;)o.unshift(a),a=a.parentNode}return r.each(o,function(u,h){if(h){var g=h.nodeType!==1?!1:h.tagName.toLowerCase();if(r(h).hasClass("redactor-script-tag")||r(h).hasClass("redactor-selection-marker"))return;if(g&&t.length!==0&&r.inArray(g,t)===-1)return;n.push(h)}}),n.length===0?[]:n},nextNode:function(e){if(e.hasChildNodes())return e.firstChild;for(;e&&!e.nextSibling;)e=e.parentNode;return e?e.nextSibling:null},save:function(){this.marker.insert(),this.savedSel=this.core.editor().html()},restore:function(e){var t=this.marker.find(1),i=this.marker.find(2);this.detect.isFirefox()&&this.core.editor().focus(),t.length!==0&&i.length!==0?this.caret.set(t,i):t.length!==0?this.caret.start(t):this.core.editor().focus(),e!==!1&&(this.marker.remove(),this.savedSel=!1)},saveInstant:function(){var e=this.core.editor()[0],t=e.ownerDocument,i=t.defaultView,s=i.getSelection();if(!(!s.getRangeAt||!s.rangeCount)){var o=s.getRangeAt(0),n=o.cloneRange();n.selectNodeContents(e),n.setEnd(o.startContainer,o.startOffset);var a=n.toString().length;return this.saved={start:a,end:a+o.toString().length,node:o.startContainer},this.saved}},restoreInstant:function(e){if(!(typeof e=="undefined"&&!this.saved)){this.saved=typeof e!="undefined"?e:this.saved;var t=this.core.editor().find(this.saved.node);if(t.length!==0&&t.text().trim().replace(/\u200B/g,"").length===0){try{var a=document.createRange();a.setStart(t[0],0);var A=window.getSelection();A.removeAllRanges(),A.addRange(a)}catch(J){}return}var i=this.core.editor()[0],s=i.ownerDocument,o=s.defaultView,n=0,a=s.createRange();a.setStart(i,0),a.collapse(!0);for(var c=[i],u,h=!1,g=!1;!g&&(u=c.pop());)if(u.nodeType==3){var m=n+u.length;!h&&this.saved.start>=n&&this.saved.start<=m&&(a.setStart(u,this.saved.start-n),h=!0),h&&this.saved.end>=n&&this.saved.end<=m&&(a.setEnd(u,this.saved.end-n),g=!0),n=m}else for(var E=u.childNodes.length;E--;)c.push(u.childNodes[E]);var A=o.getSelection();A.removeAllRanges(),A.addRange(a)}},node:function(e){r(e).prepend(this.marker.get(1)),r(e).append(this.marker.get(2)),this.selection.restore()},all:function(){this.core.editor().focus();var e=this.selection.get(),t=this.selection.range(e);t.selectNodeContents(this.core.editor()[0]),this.selection.update(e,t)},remove:function(){this.selection.get().removeAllRanges()},replace:function(e){this.insert.html(e)},text:function(){return this.selection.get().toString()},html:function(){var e="",t=this.selection.get();if(t.rangeCount){for(var i=document.createElement("div"),s=t.rangeCount,o=0;o<s;++o)i.appendChild(t.getRangeAt(o).cloneContents());e=this.clean.onGet(i.innerHTML)}return e},extractEndOfNode:function(e){var t=this.selection.get(),i=this.selection.range(t),s=i.cloneRange();return s.selectNodeContents(e),s.setStart(i.endContainer,i.endOffset),s.extractContents()},removeMarkers:function(){this.marker.remove()},marker:function(e){return this.marker.get(e)},markerHtml:function(e){return this.marker.html(e)}}},shortcuts:function(){return{hotkeysSpecialKeys:{8:"backspace",9:"tab",10:"return",13:"return",16:"shift",17:"ctrl",18:"alt",19:"pause",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"insert",46:"del",59:";",61:"=",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9",106:"*",107:"+",109:"-",110:".",111:"/",112:"f1",113:"f2",114:"f3",115:"f4",116:"f5",117:"f6",118:"f7",119:"f8",120:"f9",121:"f10",122:"f11",123:"f12",144:"numlock",145:"scroll",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},hotkeysShiftNums:{"`":"~",1:"!",2:"@",3:"#",4:"$",5:"%",6:"^",7:"&",8:"*",9:"(",0:")","-":"_","=":"+",";":": ","'":'"',",":"<",".":">","/":"?","\\":"|"},init:function(e,t){if(this.opts.shortcuts===!1)return(e.ctrlKey||e.metaKey)&&(t===66||t===73)&&e.preventDefault(),!1;r.each(this.opts.shortcuts,r.proxy(function(i,s){this.shortcuts.build(e,i,s)},this))},build:function(e,t,i){for(var s=r.proxy(function(){this.shortcuts.buildHandler(i)},this),o=t.split(","),n=o.length,a=0;a<n;a++)typeof o[a]=="string"&&this.shortcuts.handler(e,r.trim(o[a]),s)},buildHandler:function(e){var t;e.func.search(/\./)!=="-1"?(t=e.func.split("."),typeof this[t[0]]!="undefined"&&this[t[0]][t[1]].apply(this,e.params)):this[e.func].apply(this,e.params)},handler:function(e,t,i){t=t.toLowerCase().split(" ");var s=this.shortcuts.hotkeysSpecialKeys[e.keyCode],o=String.fromCharCode(e.which).toLowerCase(),n="",a={};r.each(["alt","ctrl","meta","shift"],function(h,g){e[g+"Key"]&&s!==g&&(n+=g+"+")}),s&&(a[n+s]=!0),o&&(a[n+o]=!0,a[n+this.shortcuts.hotkeysShiftNums[o]]=!0,n==="shift+"&&(a[this.shortcuts.hotkeysShiftNums[o]]=!0));for(var c=t.length,u=0;u<c;u++)if(a[t[u]])return e.preventDefault(),i.apply(this,arguments)}}},storage:function(){return{data:[],add:function(e){e.status=!0,e.url=decodeURI(e.url),this.storage.data[e.url]=e},status:function(e,t){this.storage.data[decodeURI(e)].status=t},observe:function(){var e=this,t=this.core.editor().find("[data-image]");t.each(function(o,n){e.storage.add({type:"image",node:n,url:n.src,id:r(n).attr("data-image")})});var i=this.core.editor().find("[data-file]");i.each(function(o,n){e.storage.add({type:"file",node:n,url:n.href,id:r(n).attr("data-file")})});var s=this.core.editor().find("[data-s3]");s.each(function(o,n){var a=n.tagName==="IMG"?n.src:n.href;e.storage.add({type:"s3",node:n,url:a,id:r(n).attr("data-s3")})})},changes:function(){for(var e in this.storage.data){var t=this.storage.data[e],i=t.node.tagName==="IMG"?"src":"href",s=this.core.editor().find("[data-"+t.type+"]["+i+'="'+t.url+'"]');s.length===0?this.storage.status(t.url,!1):this.storage.status(t.url,!0)}return this.storage.data}}},toolbar:function(){return{build:function(){this.button.hideButtons(),this.button.hideButtonsOnMobile(),this.$toolbarBox=r("<div />").addClass("redactor-toolbar-box"),this.$toolbar=this.toolbar.createContainer(),this.$toolbarBox.append(this.$toolbar),this.toolbar.append(),this.button.$toolbar=this.$toolbar,this.button.setFormatting(),this.button.load(this.$toolbar),this.toolbar.setOverflow(),this.toolbar.setFixed()},createContainer:function(){return r("<ul>").addClass("redactor-toolbar").attr({id:"redactor-toolbar-"+this.uuid,role:"toolbar"})},append:function(){this.opts.toolbarExternal?(this.$toolbar.addClass("redactor-toolbar-external"),r(this.opts.toolbarExternal).html(this.$toolbarBox)):this.opts.type==="textarea"?this.$box.prepend(this.$toolbarBox):this.$element.before(this.$toolbarBox)},setOverflow:function(){this.opts.toolbarOverflow&&this.$toolbar.addClass("redactor-toolbar-overflow")},setFixed:function(){if(!(!this.opts.toolbarFixed||this.opts.toolbarExternal)){if(this.opts.toolbarFixedTarget!==document){var e=r(this.opts.toolbarFixedTarget);this.toolbar.toolbarOffsetTop=e.length===0?0:this.core.box().offset().top-e.offset().top}var t=this.core.box().closest(".modal-body").length!==0?1e3:0;setTimeout(r.proxy(function(){var i=this;this.toolbar.observeScroll(!1),this.detect.isDesktop()?r(this.opts.toolbarFixedTarget).on("scroll.redactor."+this.uuid,function(){i.core.editor().height()<100||i.placeholder.isEditorEmpty()||i.toolbar.observeScroll()}):r(this.opts.toolbarFixedTarget).on("scroll.redactor."+this.uuid,function(){i.core.editor().height()<100||i.placeholder.isEditorEmpty()||(i.core.toolbar().hide(),clearTimeout(r.data(this,"scrollCheck")),r.data(this,"scrollCheck",setTimeout(function(){i.core.toolbar().show(),i.toolbar.observeScroll()},250)))})},this),t)}},setUnfixed:function(){this.toolbar.observeScrollDisable()},getBoxTop:function(){return this.opts.toolbarFixedTarget===document?this.core.box().offset().top:this.toolbar.toolbarOffsetTop},observeScroll:function(e){var t=0;e!==!1&&(t=this.opts.toolbarFixedTarget===document?20:0);var i=r(this.opts.toolbarFixedTarget).scrollTop(),s=this.toolbar.getBoxTop();i!==s&&(i+this.opts.toolbarFixedTopOffset+t>s?this.toolbar.observeScrollEnable(i,s):this.toolbar.observeScrollDisable())},observeScrollResize:function(){this.$toolbar.css({width:this.core.box().innerWidth(),left:this.core.box().offset().left})},observeScrollEnable:function(e,t){if(typeof this.fullscreen!="undefined"&&this.fullscreen.isOpened===!1){this.toolbar.observeScrollDisable();return}var i=t+this.core.box().outerHeight()-32,s=this.core.box().innerWidth(),o=this.detect.isDesktop()?"fixed":"absolute",n=this.detect.isDesktop()?this.opts.toolbarFixedTopOffset:r(this.opts.toolbarFixedTarget).scrollTop()-t+this.opts.toolbarFixedTopOffset,a=this.detect.isDesktop()?this.core.box().offset().left:0;this.opts.toolbarFixedTarget!==document&&(o="absolute",n=this.opts.toolbarFixedTopOffset+r(this.opts.toolbarFixedTarget).scrollTop()-t,a=0),this.$toolbar.addClass("toolbar-fixed-box"),this.$toolbar.css({position:o,width:s,top:n,left:a}),e>i&&r(".redactor-dropdown-"+this.uuid+":visible").hide(),this.toolbar.setDropdownsFixed(),this.$toolbar.css("visibility",e<i?"visible":"hidden"),r(window).on("resize.redactor-toolbar."+this.uuid,r.proxy(this.toolbar.observeScrollResize,this))},observeScrollDisable:function(){this.$toolbar.css({position:"relative",width:"auto",top:0,left:0,visibility:"visible"}),this.toolbar.unsetDropdownsFixed(),this.$toolbar.removeClass("toolbar-fixed-box"),r(window).off("resize.redactor-toolbar."+this.uuid)},setDropdownsFixed:function(){var e=this.opts.toolbarFixedTarget===document&&this.detect.isDesktop()?"fixed":"absolute";this.toolbar.setDropdownPosition(e)},unsetDropdownsFixed:function(){this.toolbar.setDropdownPosition("absolute")},setDropdownPosition:function(e){var t=this;r(".redactor-dropdown-"+this.uuid).each(function(){var i=r(this),s=t.button.get(i.attr("rel")),o=e==="fixed"?t.opts.toolbarFixedTopOffset:s.offset().top;i.css({position:e,top:s.innerHeight()+o+"px"})})}}},upload:function(){return{init:function(e,t,i){this.upload.direct=!1,this.upload.callback=i,this.upload.url=t,this.upload.$el=r(e),this.upload.$droparea=r('<div id="redactor-droparea" />'),this.upload.$placeholdler=r('<div id="redactor-droparea-placeholder" />').text(this.lang.get("upload-label")),this.upload.$input=r('<input type="file" name="file" multiple />'),this.upload.$placeholdler.append(this.upload.$input),this.upload.$droparea.append(this.upload.$placeholdler),this.upload.$el.append(this.upload.$droparea),this.upload.$droparea.off("redactor.upload"),this.upload.$input.off("redactor.upload"),this.upload.$droparea.on("dragover.redactor.upload",r.proxy(this.upload.onDrag,this)),this.upload.$droparea.on("dragleave.redactor.upload",r.proxy(this.upload.onDragLeave,this)),this.upload.$input.on("change.redactor.upload",r.proxy(function(s){s=s.originalEvent||s;for(var o=this.upload.$input[0].files.length,n=0;n<o;n++){var a=o-1-n;this.upload.traverseFile(this.upload.$input[0].files[a],s)}},this)),this.upload.$droparea.on("drop.redactor.upload",r.proxy(function(s){s.preventDefault(),this.upload.$droparea.removeClass("drag-hover").addClass("drag-drop"),this.upload.onDrop(s)},this))},directUpload:function(e,t){this.upload.direct=!0,this.upload.traverseFile(e,t)},onDrop:function(e){e=e.originalEvent||e;var t=e.dataTransfer.files;if(this.opts.multipleImageUpload)for(var i=t.length,s=0;s<i;s++)this.upload.traverseFile(t[s],e);else this.upload.traverseFile(t[0],e)},traverseFile:function(e,t){if(this.opts.s3){this.upload.setConfig(e),this.uploads3.send(e,t);return}var i=window.FormData?new FormData:null;if(window.FormData){this.upload.setConfig(e);var s=this.upload.type==="image"?this.opts.imageUploadParam:this.opts.fileUploadParam;i.append(s,e)}var o=this.core.callback("uploadStart",t,i);o!==!1&&(this.progress.show(),this.upload.send(i,t))},setConfig:function(e){this.upload.getType(e),this.upload.direct&&(this.upload.url=this.upload.type==="image"?this.opts.imageUpload:this.opts.fileUpload,this.upload.callback=this.upload.type==="image"?this.image.insert:this.file.insert)},getType:function(e){this.upload.type=this.opts.imageTypes.indexOf(e.type)===-1?"file":"image",this.opts.imageUpload===null&&this.opts.fileUpload!==null&&(this.upload.type="file")},getHiddenFields:function(e,t){return e===!1||typeof e!="object"||r.each(e,r.proxy(function(i,s){s!==null&&s.toString().indexOf("#")===0&&(s=r(s).val()),t.append(i,s)},this)),t},send:function(e,t){this.upload.type==="image"?(e=this.utils.appendFields(this.opts.imageUploadFields,e),e=this.utils.appendForms(this.opts.imageUploadForms,e),e=this.upload.getHiddenFields(this.upload.imageFields,e)):(e=this.utils.appendFields(this.opts.fileUploadFields,e),e=this.utils.appendForms(this.opts.fileUploadForms,e),e=this.upload.getHiddenFields(this.upload.fileFields,e));var i=new XMLHttpRequest;i.open("POST",this.upload.url),i.setRequestHeader("X-Requested-With","XMLHttpRequest"),i.onreadystatechange=r.proxy(function(){if(i.readyState===4){var o=i.responseText;o=o.replace(/^\[/,""),o=o.replace(/\]$/,"");var n;try{n=typeof o=="string"?JSON.parse(o):o}catch(a){n={error:!0}}this.progress.hide(),this.upload.direct||this.upload.$droparea.removeClass("drag-drop"),this.upload.callback(n,this.upload.direct,t)}},this);var s=this.core.callback("uploadBeforeSend",i);s!==!1&&i.send(e)},onDrag:function(e){e.preventDefault(),this.upload.$droparea.addClass("drag-hover")},onDragLeave:function(e){e.preventDefault(),this.upload.$droparea.removeClass("drag-hover")},clearImageFields:function(){this.upload.imageFields={}},addImageFields:function(e,t){this.upload.imageFields[e]=t},removeImageFields:function(e){delete this.upload.imageFields[e]},clearFileFields:function(){this.upload.fileFields={}},addFileFields:function(e,t){this.upload.fileFields[e]=t},removeFileFields:function(e){delete this.upload.fileFields[e]}}},uploads3:function(){return{send:function(e,t){this.uploads3.executeOnSignedUrl(e,r.proxy(function(i){this.uploads3.sendToS3(e,i,t)},this))},executeOnSignedUrl:function(e,t){var i=new XMLHttpRequest,s=this.opts.s3.search(/\?/)===-1?"?":"&";i.open("GET",this.opts.s3+s+"name="+e.name+"&type="+e.type,!0),i.overrideMimeType&&i.overrideMimeType("text/plain; charset=x-user-defined");var o=this;i.onreadystatechange=function(n){this.readyState===4&&this.status===200&&(o.progress.show(),t(decodeURIComponent(this.responseText)))},i.send()},createCORSRequest:function(e,t){var i=new XMLHttpRequest;return"withCredentials"in i?i.open(e,t,!0):typeof XDomainRequest!="undefined"?(i=new XDomainRequest,i.open(e,t)):i=null,i},sendToS3:function(e,t,i){var s=this.uploads3.createCORSRequest("PUT",t);s&&(s.onload=r.proxy(function(){var o;if(this.progress.hide(),s.status!==200){o={error:!0},this.upload.callback(o,this.upload.direct,s);return}var n=t.split("?");if(!n[0])return!1;if(this.upload.direct||this.upload.$droparea.removeClass("drag-drop"),o={url:n[0],id:n[0],s3:!0},this.upload.type==="file"){var a=n[0].split("/");o.name=a[a.length-1]}this.upload.callback(o,this.upload.direct,i)},this),s.onerror=function(){},s.upload.onprogress=function(o){},s.setRequestHeader("Content-Type",e.type),s.setRequestHeader("x-amz-acl","public-read"),s.send(e))}}},utils:function(){return{isEmpty:function(e){return e=typeof e=="undefined"?this.core.editor().html():e,e=e.replace(/[\u200B-\u200D\uFEFF]/g,""),e=e.replace(/&nbsp;/gi,""),e=e.replace(/<\/?br\s?\/?>/g,""),e=e.replace(/\s/g,""),e=e.replace(/^<p>[^\W\w\D\d]*?<\/p>$/i,""),e=e.replace(/<iframe(.*?[^>])>$/i,"iframe"),e=e.replace(/<source(.*?[^>])>$/i,"source"),e=e.replace(/<[^\/>][^>]*><\/[^>]+>/gi,""),e=e.replace(/<[^\/>][^>]*><\/[^>]+>/gi,""),e=r.trim(e),e===""},isElement:function(e){try{return e instanceof HTMLElement}catch(t){return typeof e=="object"&&e.nodeType===1&&typeof e.style=="object"&&typeof e.ownerDocument=="object"}},strpos:function(e,t,i){var s=e.indexOf(t,i);return s>=0?s:!1},dataURItoBlob:function(e){var t;e.split(",")[0].indexOf("base64")>=0?t=atob(e.split(",")[1]):t=unescape(e.split(",")[1]);for(var i=e.split(",")[0].split(":")[1].split(";")[0],s=new Uint8Array(t.length),o=0;o<t.length;o++)s[o]=t.charCodeAt(o);return new Blob([s],{type:i})},getOuterHtml:function(e){return r("<div>").append(r(e).eq(0).clone()).html()},cloneAttributes:function(e,t){e=e[0]||e,t=r(t);for(var i=e.attributes,s=i.length;s--;){var o=i[s];t.attr(o.name,o.value)}return t},breakBlockTag:function(){var e=this.selection.block();if(!e)return!1;var t=this.utils.isEmpty(e.innerHTML),i=e.tagName.toLowerCase();if(i==="pre"||i==="li"||i==="td"||i==="th")return!1;if(!t&&this.utils.isStartOfElement(e))return{$block:r(e),$next:r(e).next(),type:"start"};if(!t&&this.utils.isEndOfElement(e))return{$block:r(e),$next:r(e).next(),type:"end"};var s=this.selection.extractEndOfNode(e),o=r("<"+i+" />").append(s);return o=this.utils.cloneAttributes(e,o),r(e).after(o),{$block:r(e),$next:o,type:"break"}},inBlocks:function(e){e=r.isArray(e)?e:[e];for(var t=this.selection.blocks(),i=t.length,s=!1,o=0;o<i;o++)if(t[o]!==!1){var n=t[o].tagName.toLowerCase();r.inArray(n,e)!==-1&&(s=!0)}return s},inInlines:function(e){e=r.isArray(e)?e:[e];for(var t=this.selection.inlines(),i=t.length,s=!1,o=0;o<i;o++){var n=t[o].tagName.toLowerCase();r.inArray(n,e)!==-1&&(s=!0)}return s},isTag:function(e,t){var i=r(e).closest(t,this.core.editor()[0]);return i.length===1?i[0]:!1},isBlock:function(e){return e===null?!1:(e=e[0]||e,e&&this.utils.isBlockTag(e.tagName))},isBlockTag:function(e){return typeof e=="undefined"?!1:this.reIsBlock.test(e)},isInline:function(e){return e=e[0]||e,e&&this.utils.isInlineTag(e.tagName)},isInlineTag:function(e){return typeof e=="undefined"?!1:this.reIsInline.test(e)},isRedactorParent:function(e){return!e||r(e).parents(".redactor-in").length===0||r(e).hasClass("redactor-in")?!1:e},isCurrentOrParentHeader:function(){return this.utils.isCurrentOrParent(["H1","H2","H3","H4","H5","H6"])},isCurrentOrParent:function(e){var t=this.selection.parent(),i=this.selection.current();if(r.isArray(e)){var s=0;return r.each(e,r.proxy(function(o,n){this.utils.isCurrentOrParentOne(i,t,n)&&s++},this)),s!==0}else return this.utils.isCurrentOrParentOne(i,t,e)},isCurrentOrParentOne:function(e,t,i){return i=i.toUpperCase(),t&&t.tagName===i?t:e&&e.tagName===i?e:!1},isEditorRelative:function(){var e=this.core.editor().css("position"),t=["absolute","fixed","relative"];return r.inArray(t,e)!==-1},setEditorRelative:function(){this.core.editor().addClass("redactor-relative")},getScrollTarget:function(){var e=r(this.opts.scrollTarget);return e.length!==0?e:r(document)},freezeScroll:function(){this.freezeScrollTop=this.utils.getScrollTarget().scrollTop(),this.utils.getScrollTarget().scrollTop(this.freezeScrollTop)},unfreezeScroll:function(){typeof this.freezeScrollTop!="undefined"&&this.utils.getScrollTarget().scrollTop(this.freezeScrollTop)},saveScroll:function(){this.tmpScrollTop=this.utils.getScrollTarget().scrollTop()},restoreScroll:function(){typeof this.tmpScrollTop!="undefined"&&this.utils.getScrollTarget().scrollTop(this.tmpScrollTop)},isStartOfElement:function(e){return typeof e=="undefined"&&(e=this.selection.block(),!e)?!1:this.offset.get(e)===0},isEndOfElement:function(e){if(typeof e=="undefined"&&(e=this.selection.block(),!e))return!1;var t=r.trim(r(e).text()).replace(/[\t\n\r\n]/g,"").replace(/\u200B/g,""),i=this.offset.get(e);return i===t.length},removeEmptyAttr:function(e,t){var i=r(e);return typeof i.attr(t)=="undefined"?!0:i.attr(t)===""?(i.removeAttr(t),!0):!1},replaceToTag:function(e,t){var i;return r(e).replaceWith(function(){i=r("<"+t+" />").append(r(this).contents());for(var s=0;s<this.attributes.length;s++)i.attr(this.attributes[s].name,this.attributes[s].value);return i}),i},isSelectAll:function(){return this.selectAll},enableSelectAll:function(){this.selectAll=!0},disableSelectAll:function(){this.selectAll=!1},disableBodyScroll:function(){var e=r("html"),t=window.innerWidth;if(!t){var i=document.documentElement.getBoundingClientRect();t=i.right-Math.abs(i.left)}var s=document.body.clientWidth<t,o=this.utils.measureScrollbar();e.css("overflow","hidden"),s&&e.css("padding-right",o)},measureScrollbar:function(){var e=r("body"),t=document.createElement("div");t.className="redactor-scrollbar-measure",e.append(t);var i=t.offsetWidth-t.clientWidth;return e[0].removeChild(t),i},enableBodyScroll:function(){r("html").css({overflow:"","padding-right":""}),r("body").remove("redactor-scrollbar-measure")},appendFields:function(e,t){if(e){if(typeof e=="object")return r.each(e,function(o,n){n!==null&&n.toString().indexOf("#")===0&&(n=r(n).val()),t.append(o,n)}),t}else return t;var i=r(e);if(i.length===0)return t;var s="";return i.each(function(){t.append(r(this).attr("name"),r(this).val())}),t},appendForms:function(e,t){if(!e)return t;var i=r(e);if(i.length===0)return t;var s=i.serializeArray();return r.each(s,function(o,n){t.append(n.name,n.value)}),t},isRgb:function(e){return e.search(/^rgb/i)===0},rgb2hex:function(e){return e=e.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i),e&&e.length===4?"#"+("0"+parseInt(e[1],10).toString(16)).slice(-2)+("0"+parseInt(e[2],10).toString(16)).slice(-2)+("0"+parseInt(e[3],10).toString(16)).slice(-2):""},isCollapsed:function(){return this.selection.isCollapsed()},isMobile:function(){return this.detect.isMobile()},isDesktop:function(){return this.detect.isDesktop()},isPad:function(){return this.detect.isIpad()}}},browser:function(){return{webkit:function(){return this.detect.isWebkit()},ff:function(){return this.detect.isFirefox()},ie:function(){return this.detect.isIe()}}}},r(window).on("load.tools.redactor",function(){r('[data-tools="redactor"]').redactor()}),f.prototype.init.prototype=f.prototype})(jQuery),function(r){r.fn.redactorAnimation=function(f,e,t){return this.each(function(){new d(this,f,e,t)})};function d(f,e,t,i){var s={duration:.5,iterate:1,delay:0,prefix:"redactor-",timing:"linear"};this.animation=e,this.slide=this.animation==="slideDown"||this.animation==="slideUp",this.$element=r(f),this.prefixes=["","-moz-","-o-animation-","-webkit-"],this.queue=[],typeof t=="function"?(i=t,this.opts=s):this.opts=r.extend(s,t),this.slide&&this.$element.height(this.$element.height()),this.init(i)}d.prototype={init:function(f){this.queue.push(this.animation),this.clean(),this.animation==="show"?(this.opts.timing="linear",this.$element.removeClass("hide").show(),typeof f=="function"&&f(this)):this.animation==="hide"?(this.opts.timing="linear",this.$element.hide(),typeof f=="function"&&f(this)):this.animate(f)},animate:function(f){this.$element.addClass("redactor-animated").css("display","").removeClass("hide"),this.$element.addClass(this.opts.prefix+this.queue[0]),this.set(this.opts.duration+"s",this.opts.delay+"s",this.opts.iterate,this.opts.timing);var e=this.queue.length>1?null:f;this.complete("AnimationEnd",r.proxy(function(){this.$element.hasClass(this.opts.prefix+this.queue[0])&&(this.clean(),this.queue.shift(),this.queue.length&&this.animate(f))},this),e)},set:function(f,e,t,i){for(var s=this.prefixes.length;s--;)this.$element.css(this.prefixes[s]+"animation-duration",f),this.$element.css(this.prefixes[s]+"animation-delay",e),this.$element.css(this.prefixes[s]+"animation-iteration-count",t),this.$element.css(this.prefixes[s]+"animation-timing-function",i)},clean:function(){this.$element.removeClass("redactor-animated"),this.$element.removeClass(this.opts.prefix+this.queue[0]),this.set("","","","")},complete:function(f,e,t){this.$element.one(f.toLowerCase()+" webkit"+f+" o"+f+" MS"+f,r.proxy(function(){typeof e=="function"&&e(),typeof t=="function"&&t(this);var i=["fadeOut","slideUp","zoomOut","slideOutUp","slideOutRight","slideOutLeft"];r.inArray(this.animation,i)!==-1&&this.$element.css("display","none"),this.slide&&this.$element.css("height","")},this))}}}(jQuery),function(r){r.Redactor.prototype.source=function(){return{init:function(){var d=this.button.addFirst("html","HTML");this.button.setIcon(d,'<i class="re-icon-html"></i>'),this.button.addCallback(d,this.source.toggle);var f={width:"100%",margin:"0",background:"#1d1d1d","box-sizing":"border-box",color:"#ccc","font-size":"15px",outline:"none",padding:"20px","line-height":"24px","font-family":'Consolas, Menlo, Monaco, "Courier New", monospace'};this.source.$textarea=r("<textarea />"),this.source.$textarea.css(f).hide(),this.opts.type==="textarea"?this.core.box().append(this.source.$textarea):this.core.box().after(this.source.$textarea),this.core.element().on("destroy.callback.redactor",r.proxy(function(){this.source.$textarea.remove()},this))},toggle:function(){this.source.$textarea.hasClass("open")?this.source.hide():(this.source.show(),this.source.$textarea.on("keyup.redactor-source",r.proxy(function(){var d=this.source.$textarea.val();this.core.callback("change",d)},this)))},setCaretOnShow:function(){this.source.offset=this.offset.get();var d=r(window).scrollTop(),f=this.core.editor().innerWidth(),e=this.core.editor().innerHeight();this.source.start=0,this.source.end=0;var t=r("<div/>").append(r.parseHTML(this.core.editor().html(),document,!0)),i=t.find("span.redactor-selection-marker");if(i.length>0){var s=t.html().replace(/&amp;/g,"&");i.length===1?(this.source.start=this.utils.strpos(s,t.find("#selection-marker-1").prop("outerHTML")),this.source.end=this.source.start):i.length===2&&(this.source.start=this.utils.strpos(s,t.find("#selection-marker-1").prop("outerHTML")),this.source.end=this.utils.strpos(s,t.find("#selection-marker-2").prop("outerHTML"))-t.find("#selection-marker-1").prop("outerHTML").toString().length)}},setCaretOnHide:function(d){if(this.source.start=this.source.$textarea.get(0).selectionStart,this.source.end=this.source.$textarea.get(0).selectionEnd,this.source.start>this.source.end&&this.source.end>0){var f=this.source.end,e=this.source.start;this.source.start=f,this.source.end=e}if(this.source.start=this.source.enlargeOffset(d,this.source.start),this.source.end=this.source.enlargeOffset(d,this.source.end),d=d.substr(0,this.source.start)+this.marker.html(1)+d.substr(this.source.start),this.source.end>this.source.start){var t=this.marker.html(1).toString().length;d=d.substr(0,this.source.end+t)+this.marker.html(2)+d.substr(this.source.end+t)}return d},hide:function(){this.source.$textarea.removeClass("open").hide(),this.source.$textarea.off(".redactor-source");var d=this.source.$textarea.val();d=this.paragraphize.load(d),d=this.source.setCaretOnHide(d),d=d.replace('&amp;<span id="selection-marker-1" class="redactor-selection-marker">\u200B</span>','<span id="selection-marker-1" class="redactor-selection-marker">\u200B</span>&amp;'),this.code.start(d),this.button.enableAll(),this.core.editor().show().focus(),this.selection.restore(),this.placeholder.enable(),this.core.callback("visual")},show:function(){this.selection.save(),this.source.setCaretOnShow();var d=this.core.editor().height(),f=this.code.get();f=this.core.callback("source",f),this.core.editor().hide(),this.button.disableAll("html"),this.source.$textarea.val(f).height(d).addClass("open").show(),this.source.$textarea.on("keyup.redactor-source",r.proxy(function(){this.opts.type==="textarea"&&this.core.textarea().val(this.source.$textarea.val())},this)),this.marker.remove(),r(window).scrollTop(scroll),this.source.$textarea[0].setSelectionRange&&this.source.$textarea[0].setSelectionRange(this.source.start,this.source.end),this.source.$textarea[0].scrollTop=0,setTimeout(r.proxy(function(){this.source.$textarea.focus()},this),0)},enlargeOffset:function(d,f){var e=d.length,t=0;if(d[f]===">")t++;else for(var i=f;i<=e&&(t++,d[i]!==">");i++)if(d[i]==="<"||i===e){t=0;break}return f+t}}}}(jQuery),function(r){r.Redactor.prototype.table=function(){return{langs:{en:{table:"Table","insert-table":"Insert table","insert-row-above":"Insert row above","insert-row-below":"Insert row below","insert-column-left":"Insert column left","insert-column-right":"Insert column right","add-head":"Add head","delete-head":"Delete head","delete-column":"Delete column","delete-row":"Delete row","delete-table":"Delete table"}},init:function(){var d={};d.insert_table={title:this.lang.get("insert-table"),func:this.table.insert,observe:{element:"table",in:{attr:{class:"redactor-dropdown-link-inactive","aria-disabled":!0}}}},d.insert_row_above={title:this.lang.get("insert-row-above"),func:this.table.addRowAbove,observe:{element:"table",out:{attr:{class:"redactor-dropdown-link-inactive","aria-disabled":!0}}}},d.insert_row_below={title:this.lang.get("insert-row-below"),func:this.table.addRowBelow,observe:{element:"table",out:{attr:{class:"redactor-dropdown-link-inactive","aria-disabled":!0}}}},d.insert_column_left={title:this.lang.get("insert-column-left"),func:this.table.addColumnLeft,observe:{element:"table",out:{attr:{class:"redactor-dropdown-link-inactive","aria-disabled":!0}}}},d.insert_column_right={title:this.lang.get("insert-column-right"),func:this.table.addColumnRight,observe:{element:"table",out:{attr:{class:"redactor-dropdown-link-inactive","aria-disabled":!0}}}},d.add_head={title:this.lang.get("add-head"),func:this.table.addHead,observe:{element:"table",out:{attr:{class:"redactor-dropdown-link-inactive","aria-disabled":!0}}}},d.delete_head={title:this.lang.get("delete-head"),func:this.table.deleteHead,observe:{element:"table",out:{attr:{class:"redactor-dropdown-link-inactive","aria-disabled":!0}}}},d.delete_column={title:this.lang.get("delete-column"),func:this.table.deleteColumn,observe:{element:"table",out:{attr:{class:"redactor-dropdown-link-inactive","aria-disabled":!0}}}},d.delete_row={title:this.lang.get("delete-row"),func:this.table.deleteRow,observe:{element:"table",out:{attr:{class:"redactor-dropdown-link-inactive","aria-disabled":!0}}}},d.delete_table={title:this.lang.get("delete-table"),func:this.table.deleteTable,observe:{element:"table",out:{attr:{class:"redactor-dropdown-link-inactive","aria-disabled":!0}}}};var f=this.button.addBefore("link","table",this.lang.get("table"));this.button.setIcon(f,'<i class="re-icon-table"></i>'),this.button.addDropdown(f,d)},insert:function(){if(!this.table.getTable()){this.placeholder.hide();for(var d=2,f=3,e=r("<div>"),t=r("<table />"),i=0;i<d;i++){for(var s=r("<tr>"),o=0;o<f;o++){var n=r("<td>"+this.opts.invisibleSpace+"</td>");i===0&&o===0&&n.append(this.marker.get()),r(s).append(n)}t.append(s)}e.append(t);var a=e.html();this.buffer.set();var c=this.selection.current();r(c).closest("li",this.core.editor()[0]).length!==0?r(c).closest("ul, ol").first().after(a):(this.air.collapsed(),this.insert.html(a)),this.selection.restore(),this.core.callback("insertedTable",t)}},getTable:function(){var d=r(this.selection.current()).closest("table");return!this.utils.isRedactorParent(d)||d.length===0?!1:d},restoreAfterDelete:function(d){this.selection.restore(),d.find("span.redactor-selection-marker").remove()},deleteTable:function(){var d=this.table.getTable();if(d){this.buffer.set();var f=d.next();!this.opts.linebreaks&&f.length!==0?this.caret.start(f):this.caret.after(d),d.remove()}},deleteRow:function(){var d=this.table.getTable();if(d){var f=r(this.selection.current());this.buffer.set();var e=f.closest("tr"),t=e.prev().length?e.prev():e.next();if(t.length){var i=t.children("td, th").first();i.length&&i.prepend(this.marker.get())}e.remove(),this.table.restoreAfterDelete(d)}},deleteColumn:function(){var d=this.table.getTable();if(d){this.buffer.set();var f=r(this.selection.current()),e=f.closest("td, th"),t=e[0].cellIndex;d.find("tr").each(r.proxy(function(i,s){var o=r(s),n=t-1<0?t+1:t-1;i===0&&o.find("td, th").eq(n).prepend(this.marker.get()),o.find("td, th").eq(t).remove()},this)),this.table.restoreAfterDelete(d)}},addHead:function(){var d=this.table.getTable();if(d){if(this.buffer.set(),d.find("thead").length!==0){this.table.deleteHead();return}var f=d.find("tr").first().clone();f.find("td").replaceWith(r.proxy(function(){return r("<th>").html(this.opts.invisibleSpace)},this)),$thead=r("<thead></thead>").append(f),d.prepend($thead)}},deleteHead:function(){var d=this.table.getTable();if(d){var f=d.find("thead");f.length!==0&&(this.buffer.set(),f.remove())}},addRowAbove:function(){this.table.addRow("before")},addRowBelow:function(){this.table.addRow("after")},addColumnLeft:function(){this.table.addColumn("before")},addColumnRight:function(){this.table.addColumn("after")},addRow:function(d){var f=this.table.getTable();if(f){this.buffer.set();var e=r(this.selection.current()),t=e.closest("tr"),i=t.clone();i.find("th").replaceWith(function(){var s=r("<td>");return s[0].attributes=this.attributes,s.append(r(this).contents())}),i.find("td").html(this.opts.invisibleSpace),d==="after"?t.after(i):t.before(i)}},addColumn:function(d){var f=this.table.getTable();if(f){var e=0,t=r(this.selection.current());this.buffer.set();var i=t.closest("tr"),s=t.closest("td, th");i.find("td, th").each(r.proxy(function(o,n){r(n)[0]===s[0]&&(e=o)},this)),f.find("tr").each(r.proxy(function(o,n){var a=r(n).find("td, th").eq(e),c=a.clone();c.html(this.opts.invisibleSpace),d==="after"?a.after(c):a.before(c)},this))}}}}}(jQuery),function(r){r.Redactor.prototype.alignment=function(){return{langs:{en:{align:"Align","align-left":"Align Left","align-center":"Align Center","align-right":"Align Right","align-justify":"Align Justify"}},init:function(){var d=this,f={};f.left={title:d.lang.get("align-left"),func:d.alignment.setLeft},f.center={title:d.lang.get("align-center"),func:d.alignment.setCenter},f.right={title:d.lang.get("align-right"),func:d.alignment.setRight},f.justify={title:d.lang.get("align-justify"),func:d.alignment.setJustify};var e=this.button.add("alignment",this.lang.get("align"));this.button.setIcon(e,'<i class="re-icon-alignment"></i>'),this.button.addDropdown(e,f)},removeAlign:function(){this.block.removeClass("text-center"),this.block.removeClass("text-right"),this.block.removeClass("text-justify")},setLeft:function(){this.buffer.set(),this.alignment.removeAlign()},setCenter:function(){this.buffer.set(),this.alignment.removeAlign(),this.block.addClass("text-center"),this.core.editor().focus()},setRight:function(){this.buffer.set(),this.alignment.removeAlign(),this.block.addClass("text-right"),this.core.editor().focus()},setJustify:function(){this.buffer.set(),this.alignment.removeAlign(),this.block.addClass("text-justify"),this.core.editor().focus()}}}}(jQuery),function(r){r.Redactor.prototype.video=function(){return{reUrlYoutube:/https?:\/\/(?:[0-9A-Z-]+\.)?(?:youtu\.be\/|youtube\.com\S*[^\w\-\s])([\w\-]{11})(?=[^\w\-]|$)(?![?=&+%\w.-]*(?:['"][^<>]*>|<\/a>))[?=&+%\w.-]*/ig,reUrlVimeo:/https?:\/\/(www\.)?vimeo.com\/(\d+)($|\/)/,langs:{en:{video:"Video","video-html-code":"Video Embed Code or Youtube/Vimeo Link"}},getTemplate:function(){return'<div class="modal-section" id="redactor-modal-video-insert"><section><label>'+this.lang.get("video-html-code")+'</label><textarea id="redactor-insert-video-area" style="height: 160px;"></textarea></section><section><button id="redactor-modal-button-action">Insert</button><button id="redactor-modal-button-cancel">Cancel</button></section></div>'},init:function(){var d=this.button.addAfter("image","video",this.lang.get("video"));this.button.setIcon(d,'<i class="re-icon-video"></i>'),this.button.addCallback(d,this.video.show)},show:function(){this.modal.addTemplate("video",this.video.getTemplate()),this.modal.load("video",this.lang.get("video"),700),this.modal.getActionButton().text(this.lang.get("insert")).on("click",this.video.insert),this.modal.show(),this.detect.isDesktop()&&setTimeout(function(){r("#redactor-insert-video-area").focus()},1)},insert:function(){var d=r("#redactor-insert-video-area").val(),f=d.match(/<iframe|<video/gi);if(f&&f.length!==0){var e=["iframe","video"],t=/<\/?([a-z][a-z0-9]*)\b[^>]*>/gi;d=d.replace(t,function(o,n){return e.indexOf(n.toLowerCase())===-1?"":o}),this.opts.videoContainerClass=typeof this.opts.videoContainerClass=="undefined"?"video-container":this.opts.videoContainerClass;var i='<div class="'+this.opts.videoContainerClass+'"><iframe style="width: 500px; height: 281px;" src="',s='" frameborder="0" allowfullscreen></iframe></div>';d.match(this.video.reUrlYoutube)?d=d.replace(this.video.reUrlYoutube,i+"//www.youtube.com/embed/$1"+s):d.match(this.video.reUrlVimeo)?d=d.replace(this.video.reUrlVimeo,i+"//player.vimeo.com/video/$2"+s):d='<div class="'+this.opts.videoContainerClass+'">'+d+"</div>"}this.modal.close(),this.placeholder.hide(),this.buffer.set(),this.air.collapsed(),this.insert.html(d)}}}}(jQuery);var _t=Pt(Ft()),Ye,je;window.DOMPurify=_t.default,typeof I!="undefined"&&I!==null&&(Ye=I.libs)!=null&&(je=Ye.redactor)!=null&&je.resolve($.fn.redactor)})();/*! @license DOMPurify 3.2.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.3/LICENSE */
