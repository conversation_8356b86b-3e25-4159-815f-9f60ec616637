<JiraImport><ProjectKey>GAMEJAM</ProjectKey><IssueTypes><IssueType name="Epic">Epic</IssueType><IssueType name="Story">Story</IssueType><IssueType name="Task">Task</IssueType><IssueType name="Sub-Task">Sub-Task</IssueType></IssueTypes><Fields><Field name="Summary">Summary</Field><Field name="Description">Description</Field><Field name="Priority">Priority</Field><Field name="StoryPoints">Story Points</Field><Field name="EpicLink">Epic Name</Field></Fields><Workflow><Status name="To Do">To Do</Status><Status name="In Progress">In Progress</Status><Status name="Done">Done</Status></Workflow><Assignees><Assignee name="Default">Unassigned</Assignee></Assignees><EpicField>Epic Name</EpicField></JiraImport>