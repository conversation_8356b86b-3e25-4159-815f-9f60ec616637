//  --------------------------------------------------------------------------------------
//  DESCRIPTION
//  Converts info about a serial number to HTML
//  ---------------------------------------------------------------------------------------
//  Revision        Date        Who             What
//  1.0             2023-10-24  A. Labrecque    Initial Development
//  1.1             2024-06-24  Anthony <PERSON>.      Remove references to Target Time and Duration
//  1.2             2024-06-17  Anthony D.      Logic to display icon for no entries logged 
//  1.3             2024-07-02  Anthony D.      Added Mandatory and IsException columns for variable details 
//  1.4             2024-07-31  Globensky G.    Added Deviation Data
//  1.5             2024-08-09  <PERSON><PERSON> G.    Added Exception Data
//  1.6             2024-08-28  <PERSON><PERSON> G.    Added Endpoint Logging Data
//  1.7             2024-09-17  <PERSON>. <PERSON>     Added LinkedSerialNumberUIDs
//  1.8             2024-09-19  L. Beliveau     Fix issue when no variables
//  1.9             2024-10-10  Olivier S.      Add entry type to log sheet steps + Add header on each new step or new work instruction
//  2.0             2024-10-18  Olivier S.      Add OOL signoff user to log sheet exceptions
//  2.1             2024-10-21  Olivier S.      Add note contexts
//	2.2				2024-10-25	A. Senska		No longer include body + json parse response for endpoint logging
//	2.3				2024-10-28	A. Senska		Wrong column values for endpoint logging
//	2.4				2024-10-29	A. Senska		Include Reviewed On/Reviewed By
//  2.5             2024-11-13  A. Senska       Add e-signature icons where applicable
//	2.6				2024-11-20	A. Senska		Attribute E-SIG icons
//	2.7				2024-12-02	A. Senska		Smart Tool Exception
//	2.6				2024-11-26	N. Pitsakis		Update certificate columns to show icon instead of cert details for multi certificate
//	2.7				2024-12-09	N. Pitsakis		Add execution event symbol
//	2.8				2025-01-15	A. Senska		Final Value column updates
//	2.9				2025-02-24	F. Hackney		Update Sign Off Column to Unique Token, align column tokens, route status
//  3.0             2025-04-29  Anthony D.      Changes for CWCE97UP-578
//  3.1             2025-05-06  Olivier S.      Add certificate bypass details grid
//  3.11            2025-05-14  Globensky G.	Allowed for defined columns with fixed width
//  3.12            2025-06-09  Olivier S.      Fix critical error for manual exception
//	3.13			2025-07-28	A. Senska		TWXAE-692 - missing IsException for Calc
// --------------------------------------------------------------------------------------
/** @type {html} */
// eslint-disable-next-line no-unused-vars
var result;
/** @type {long} */
var SerialNumberUID;
/** @type {string} */
var BrowserTimeZone;
/** @type {boolean} */
var IsDeviationEnabled;
/** @type {boolean} */
var IsManualExceptionEnabled;
/** @type {boolean} */
var IncludeEndpointLoggingDetails;
/** @type {Infotable} PTC.FSU.CWC.EXT.QAReport.LinkedOrderInfo_DS */
var  LinkedSerialNumberUIDs;
/** ***************************** SERVICE CODE STARTS HERE ************************************/
(function () {
    'use strict';

    var SERVICE_NAME = 'GetQAReportHTML_CWC_EXT';
    var LOG_PREFIX = me.name + '[' + SERVICE_NAME + '] error: ';
    var TOOLBOX = Things[me.GetToolboxName()];
    var RESULT_CODES = TOOLBOX.GetResultCodes_TBX();
    var QA_REPORT_MANAGER = Things[me.GetCWCExtendedFeatureManager_CWC_EXT({ FeatureName: 'QAReport' })];
    var ATTACHMENT_MANAGER = Things[me.GetCWCExtendedFeatureManager_CWC_EXT({ FeatureName: 'AssemblyExecutionExtended' })];
    var QA_REPORT_CONFIGURATION = Things[me.GetCWCExtendedFeatureConfiguration_CWC_EXT({ FeatureName: 'QAReport' })];
    var ENDPOINT_MANAGER = Things[me.GetCWCExtendedFeatureManager_CWC_EXT({ FeatureName: 'EndpointStepType' })];

    var LAUNCH_POINT = Things[me.GetDBConfigurationThingName()];
    var GLOBAL_CONFIG = Things[LAUNCH_POINT.GetCoreConfigurationManager_LHP()];

    var NOTES = 'Notes';
    var ATTACHMENTS = 'Attachments';
    var NO_ENTRIES = 'No Entries';
    var EXCEPTIONS = 'Exceptions';
    var E_SIG = 'E-Signature';
    var CERTIFICATE = 'Certificate';
    var EXECUTION_EVENT = 'Execution Event';
    var FINAL_VALUE = 'FinalValue';

    function logError(errorMessage) {
        TOOLBOX.AddToMessageQueue_TBX({
            Code: RESULT_CODES.ERROR_CODE,
            Token: 'PTC.FSU.COREUI.UsrMsg.GeneralCriticalError'
        });

        logger.error(LOG_PREFIX + errorMessage + ' with inputs: ' + JSON.stringify({
            SerialNumberUID: SerialNumberUID
        }));
    }

    var tokens = {};

    function localize(token) {
        // Populate local token table to reduce amounts of calls to Toolbox
        if (!tokens[token]) {
            tokens[token] = TOOLBOX.GetTokenTranslation({ Token: token });
        }
        return tokens[token];
    }


    // Retrieve server and host for Attachments and Media entities
    var host;

    function getHost() {
        if (!host) {
            var serverSettings = GLOBAL_CONFIG.GetConfigurationTable({
                tableName: 'ServerSettings'
            });

            if (TOOLBOX.IsInfotableEmpty({ Infotable: serverSettings })) {
                logError('No ServerSettings on + ' + GLOBAL_CONFIG.toString());
                host = '';
                return host;
            }

            host = [
                serverSettings.rows[0].IsSecurePort ? 'https://' : 'http://',
                serverSettings.rows[0].ServerName,
                serverSettings.rows[0].PortNumber ?  ':' + serverSettings.rows[0].PortNumber : ''
            ].join('');
        }
        return host;
    }

    function createLink(obj) {
        /*
        obj exemple:
        {
            url: 'https://www.google.com/',
            text: 'Google'
        }
        */
        return '<a href = "' + obj.url + '" target="_blank">' + obj.text + '</a>';
    }

    function boolToHTML(bool, type) {
        var html;
        var checkmark = bool ? '&#x2611' : '&#x2610';

        var MEDIA_PATH = '/Thingworx/MediaEntities/';

        var URL = {};
        URL[NOTES] = 'PTC.FSU.COREUI.Global.ButtonIconNotes-BLUE_MD';
        URL[ATTACHMENTS] = 'PTC.FSU.COREUI.GlobalUI.WorkDocumentTypeDocumentIcon_MD';
        URL[NO_ENTRIES] = 'PTC.FSU.CWC.EXT.NoEntriesLoggedIcon_MD';
        URL[EXCEPTIONS] = 'PTC.SCA.SCO.MnfgCommonMsgIconError';
        URL[E_SIG] = 'PTC.FSU.CWC.EXT.QAReport.ESignatureIcon_MD';
        URL[CERTIFICATE] = 'PTC.FSU.CWC.EXT.HasCertificates_MD';
        URL[EXECUTION_EVENT] = 'PTC.FSU.CWC.EXT.HasExecutionEvents_MD';
        URL[FINAL_VALUE] = 'PTC.FSU.CWC.EXT.QAReport.FinalValue_MD';

        switch (type) {
            case 'Notes':
            case 'Attachments':
            case 'No Entries':
            case 'Exceptions':
            case 'Certificate':
            case 'Execution Event':
                html = bool ? '<img src="' + getHost() + MEDIA_PATH + URL[type] + '" alt= Has"' + type + ': ' + checkmark + '" style="padding: 4px;"  height="24" width="24"/>' : ' ';
                break;
            case 'E-Signature':
                html = bool ? '<img src="' + getHost() + MEDIA_PATH + URL[type] + '" alt= Has"' + type + ': ' + checkmark + '" style="padding: 4px;"  height="24" width="24"/>' : ' ';
                break;
            case 'FinalValue':
                html = bool ? '<img src="' + getHost() + MEDIA_PATH + URL[type] + '" alt= Has"' + type + ': ' + checkmark + '" style="padding: 4px;"  height="24" width="24"/>' : ' ';
                break;
            default:
                html = bool ? '&#x2611' : '&#x2610';
        }
        return html;
    }

    function showTwoNumbers(number) {
        return number.toString().replace(/^(\d)$/, '0$1');
    }

    function dateToHTML(timestamp) {
        var date = new Date(timestamp);
        var formattedDate;
        var year = date.getUTCFullYear();
        var month = showTwoNumbers(date.getUTCMonth() + 1);
        var day = showTwoNumbers(date.getUTCDate());
        var hours = showTwoNumbers(date.getUTCHours());
        var minutes = showTwoNumbers(date.getUTCMinutes());
        var seconds = showTwoNumbers(date.getUTCSeconds());

        if (timestamp) {
            formattedDate = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;// + ' ' + timezoneSign + timezoneOffsetHours + ':' + timezoneOffsetMinutes;
        }
        else {
            formattedDate = '-';
        }
        return formattedDate;
    }

    // List columns required for each step type
    var VARIABLE_FORMATS = {
        'Smart Tool': ['IsException', 'SequenceNumber', 'SmartToolActionName', 'SmartToolActionTypeName', 'SmartToolDisplayName', 'AttributeDisplayName', 'UnitOfMeasureName', 'DataformatName', 'IsOverridden', 'AttributeValue', 'AttributeValueTimestampString', 'LowReject', 'LowWarning', 'Nominal', 'HighWarning', 'HighReject', 'OverriddenBy', 'Username', 'IsFinalValue'],
        'Manual Entry': ['IsException', 'SequenceNumber', 'AttributeValueTimestampString', 'UnitOfMeasureName', 'AttributeDisplayName', 'AttributeValue', 'LowReject', 'LowWarning', 'Nominal', 'HighWarning', 'HighReject', 'Username', 'IsFinalValue'],
        'List': ['SelectionListDisplayName', 'SelectionListItemDisplayName', 'Username', 'IsFinalValue'],
        'Calculation': ['IsException', 'SequenceNumber', 'AttributeDisplayName', 'UnitOfMeasureName', 'DataformatName', 'AttributeValue', 'AttributeValueTimestampString', 'LowReject', 'LowWarning', 'Nominal', 'HighWarning', 'HighReject', 'Username', 'IsFinalValue'],
        'Instructional / Log Sheet': ['IsException', 'Mandatory', 'EntryTypes', 'SequenceNumber', 'AttributeValueTimestampString', 'UnitOfMeasureName', 'AttributeDisplayName', 'AttributeValue', 'AttributeValueESign', 'LowReject', 'LowWarning', 'Nominal', 'HighWarning', 'HighReject', 'Username', 'SelfSignOffESig', 'OOLSignoffUser', 'OOLSignOffESig'],
        'Part Validation': ['SequenceNumber', 'IsSubstitution', 'BillOfMaterialItemDisplayName', 'BillOfMaterialItemProductName', 'BillOfMaterialItemQuantity', 'BillOfMaterialItemUOMName', 'OverriddenBy', 'Username', 'IsFinalValue'],
        'EmptyLogSheetSignOff': ['ESigIcon', 'Username', 'Timestamp']
    };

    function buildColumn(token, fn) {
        return {
            header: token,
            getValue: fn
        };
    }

    // For each column in VARIABLE_FORMAT, specify the Header, and how to retrieve value from row obj
    var VARIABLE_COLUMNS = {
        Type: buildColumn('PTC.FSU.CWC.Label.StepType', (obj) => {
            return obj.Type;
        }),
        SmartToolActionName: buildColumn('PTC.FSU.COREUI.Label.Actions', (obj) => {
            return obj.SmartToolActionName;
        }),
        SmartToolActionTypeName: buildColumn('PTC.FSU.COREUI.Label.ReadWrite', (obj) => {
            return obj.SmartToolActionTypeName;
        }),
        SmartToolDisplayName: buildColumn('PTC.FSU.COREUI.Grid.SmartTool', (obj) => {
            return obj.SmartToolDisplayName;
        }),
        SequenceNumber: buildColumn('#', (obj) => {
            return obj.SequenceNumber;
        }),
        AttributeValue: buildColumn('PTC.FSU.COREUI.Label.Value', (obj) => {
            return obj.AttributeValue;
        }),
        LowReject: buildColumn('PTC.FSU.COREUI.Label.LowReject', (obj) => {
            return obj.LowReject;
        }),
        LowWarning: buildColumn('PTC.FSU.COREUI.Label.LowWarn', (obj) => {
            return obj.LowWarning;
        }),
        Nominal: buildColumn('PTC.FSU.COREUI.Label.Nominal', (obj) => {
            return obj.NominalValue;
        }),
        HighWarning: buildColumn('PTC.FSU.COREUI.Label.HighWarn', (obj) => {
            return obj.HighWarning;
        }),
        HighReject: buildColumn('PTC.FSU.COREUI.Label.HighReject', (obj) => {
            return obj.HighReject;
        }),
        Username: buildColumn('PTC.FSU.COREUI.Label.Username', (obj) => {
            return obj.Username;
        }),
        SelfSignOffESig: buildColumn('', (obj) => {
            return boolToHTML(obj.SelfSignOffESig, E_SIG);
        }),
        AttributeDisplayName: buildColumn('PTC.FSU.COREUI.Grid.AttributeName', (obj) => {
            return obj.AttributeDisplayName;
        }),
        UnitOfMeasureName: buildColumn('PTC.FSU.CWC.Grid.UnitOfMeasure', (obj) => {
            return obj.UnitOfMeasureName;
        }),
        DataformatName: buildColumn('PTC.FSU.COREUI.Label.DataFormat', (obj) => {
            return obj.DataformatName;
        }),
        AttributeValueTimestamp: buildColumn('PTC.FSU.COREUI.Label.Timestamp', (obj) => {
            return obj.AttributeValueTimestamp;
        }),
        AttributeValueTimestampString: buildColumn('PTC.FSU.COREUI.Label.Timestamp', (obj) => {
            return obj.AttributeValueTimestampString;
        }),
        IsOverridden: buildColumn('PTC.FSU.CWC.EXT.Label.IsOverridden', (obj) => {
            return obj.IsOverridden;
        }),
        IsSubstitution: buildColumn('PTC.FSU.CWC.EXT.Label.IsSubstitution', (obj) => {
            return obj.IsSubstitution;
        }),
        SelectionListDisplayName: buildColumn('PTC.FSU.CWC.Label.List', (obj) => {
            return obj.SelectionListDisplayName;
        }),
        SelectionListItemDisplayName: buildColumn('PTC.FSU.CWC.EXT.Label.SelectedListItem', (obj) => {
            return obj.SelectionListItemDisplayName;
        }),
        BillOfMaterialItemDisplayName: buildColumn('PTC.FSU.CWC.EXT.Label.BOMItem', (obj) => {
            return obj.BillOfMaterialItemDisplayName;
        }),
        BillOfMaterialItemProductName: buildColumn('PTC.FSU.CWC.EXT.Label.EnteredPartNumber', (obj) => {
            return obj.BillOfMaterialItemProductName;
        }),
        BillOfMaterialItemQuantity: buildColumn('PTC.FSU.CWC.EXT.Label.EnteredQuantity', (obj) => {
            return obj.BillOfMaterialItemQuantity;
        }),
        BillOfMaterialItemUOMName: buildColumn('PTC.FSU.COREUI.Grid.UOM', (obj) => {
            return obj.BillOfMaterialItemUOMName;
        }),
        Mandatory: buildColumn('PTC.FSU.CWC.EXT.Button.LogSheet.Mandatory', (obj) => {
            return boolToHTML(obj.Mandatory);
        }),
        EntryTypes: buildColumn('PTC.FSU.CWC.EXT.Label.EntryType', (obj) => {
            return obj.EntryTypes; 
        }),
        OOLSignoffUser: buildColumn('PTC.FSU.CWC.EXT.Label.SignOffUser', (obj) => {
            return obj.OOLSignoffUser; 
        }),
        OOLSignOffESig: buildColumn('', (obj) => {
            return boolToHTML(obj.OOLSignOffESig, E_SIG);
        }),
        IsException: buildColumn('', (obj) => {
            return boolToHTML(obj.IsException, EXCEPTIONS);
        }),
        AttributeValueESign: buildColumn('', (obj) => {
            return boolToHTML(obj.AttributeValueESign, E_SIG);
        }),
        OverriddenBy: buildColumn('PTC.FSU.CWC.EXT.Label.OverriddenBy', (obj) => {
			return obj.OverriddenBy;
		}),
        IsFinalValue: buildColumn('', (obj) => {
            return boolToHTML(obj.IsFinalValue, FINAL_VALUE);
        }),
        ESigIcon: buildColumn('', (obj) => {
            return boolToHTML(obj.ESig, E_SIG);
        }),
        Timestamp: buildColumn('PTC.FSU.COREUI.Label.Timestamp', (obj) => {
            return dateToHTML(obj.Timestamp);
        })
    };

    // Default colors used for style
    var colorConfig = {
        SerialNumberTableMainHex: 'rgba(0,148,200,1)',
        RouteTableMainHex: 'rgba(0,148,200,1)',
        StepTableMainHex: 'rgba(0,148,200,.75)',
        VariableTableMainHex: 'rgba(0,148,200,.5)',
        NotesTableMainHex: 'rgba(0,148,200,.5)',
        CertBypassTableMainHex: 'rgba(0,148,200,.5)',
        AttachmentsTableMainHex: 'rgba(0,148,200,.5)',
        TitleCaptionHex: 'rgb(255,255,255)'
    };

    // HTML Style codes. Placeholders from colorConfig can be used.
    var STYLES = {
        body: 'font-family: \'Open Sans\',Arial,sans-serif;',
        table: 'table-layout: auto;' +
            'border-style: solid;' +
            'border-width: 1px;' +
            'background-color: rgb(250,250,250);' +
            'border-spacing: 0;' +
            'border-collapse: collapse;' +
            'margin: 5px;' + //24px;' +
            'width: 100%;', //calc(100% - 48px);',
        serialNumberTableTableStyle: 'border-color: {SerialNumberTableMainHex};',
        routeTableTableStyle: 'border-color: {RouteTableMainHex};',
        stepTableTableStyle: 'border-color: {StepTableMainHex};',
        variableTableTableStyle: 'border-color: {VariableTableMainHex};',
        certBypassTableTableStyle: 'border-color: {CertBypassTableMainHex};',
        notesTableTableStyle: 'border-color: {NotesTableMainHex};',
        attachmentsTableTableStyle: 'border-color: {AttachmentsTableMainHex};',
        caption: 'height: 32px;border-style: solid;border-width: 1px;',
        serialNumberTableCaptionStyle: 'background-color: {SerialNumberTableMainHex}; border-color: {SerialNumberTableMainHex};',
        routeTableCaptionStyle: 'background-color: {RouteTableMainHex}; border-color: {RouteTableMainHex};',
        stepTableCaptionStyle: 'background-color: {StepTableMainHex}; border-color: {StepTableMainHex}',
        variableTableCaptionStyle: 'background-color: {VariableTableMainHex}; border-color: {VariableTableMainHex}',
        notesTableCaptionStyle: 'background-color: {NotesTableMainHex}; border-color: {NotesTableMainHex}',
        certBypassTableCaptionStyle: 'background-color: {CertBypassTableMainHex}; border-color: {CertBypassTableMainHex}',
        attachmentsTableCaptionStyle: 'background-color: {AttachmentsTableMainHex}; border-color: {AttachmentsTableMainHex}',
        contentDiv: 'word-wrap: break-word;' +
            'white-space: pre-wrap;' +
            'height: auto;' +
            'min-height: 36px;' +
            'display: flex;' +
            'justify-content: left;' +
            'align-items: center;',
        StepNameDiv: 'max-width: 360px;' +
            'width: 360px;' +
            'word-wrap: break-word;' +
            'white-space: pre-wrap;' +
            'height: auto;' +
            'min-height: 36px;' + 
            'display: flex;' +
            'justify-content: left;' +
            'align-items: center;',
        captionDiv: 'font-size: 14px;' +
            'font-weight: 500;' +
            'color: {TitleCaptionHex};' +
            'padding: 7px 0 0 8px;' +
            'text-align: left;',
        thead: 'height: 24px;' +
            'margin: 0;',
        th: 'font-size: 12px;' +
            'font-weight: 400;' +
            'text-transform: uppercase;' +
            'color: rgb(110,113,124);' +
            'text-align: left;' +
            'border-style: solid;' +
            'border-color: rgb(110,113,124);' +
            'border-width: 1px 0;' +
            'padding-right: 4px;',
        td: 'background-color: rgb(255,255,255);' +
            'border-style: solid;' +
            'border-color: rgb(216,219,222);' +
            'border-width: 1px 0 0 0;' +
            'padding-right: 8px;'  +
            'white-space:nowrap;',
        firstColumn: 'padding-left: 4px;',
        tbodyTr: 'min-height: 32px; height: auto;',
        tableCell: 'background-color: rgb(250,250,250);' +
            'padding-left: 0 !important;'/*,
        avoidPageBreak: 'page-break-inside: avoid ;'*/
    };

    function getStyles(array, dynamicStyle) {
        var styles = ' style="';
        array.forEach(style => {
            styles += STYLES[style];
        });

        if (!!dynamicStyle)
        {
            styles += dynamicStyle;
        }

        styles += '"';
        return styles;
    }

    function startHTML() {
        return '<!DOCTYPE html> ' +
            '<html> ' +
            '<head> ' +
            '</head>' +
            '<body' + getStyles(['body']) + '>';
    }

    function closeHTML() {
        return '</body> </html>';
    }

    function newTable(obj) {
        /*
        obj exemple:
        {
            tableClass: 'serialNumberTable',
            title: 'PTC.XXXXX.MyTitleToken',
            tdColspan: null,
            columns: [
                'PTC.XXXXX.MyColumnAToken',
                'PTC.XXXXX.MyColumnBToken'
            ]
        }
        */
        var html = '';
        if (!!obj.tdColspan) {
            html += '<tr> <td' + getStyles(['td', 'tableCell']) + ' colspan="' + obj.tdColspan + '">';
        }

        html += '<table' + getStyles(['table', obj.tableClass + 'TableStyle']) + '>';
        html += '<caption align="top"' + getStyles(['caption', obj.tableClass + 'CaptionStyle']) + '>' +
            '<div' + getStyles(['captionDiv']) + '>' + localize(obj.title) + '</div> </caption>';
        html += '<tr' + getStyles(['thead', 'avoidPageBreak']) + '> ';
        obj.columns.forEach((column, index) => {
            var styles = ['th'];
            if (index === 0) {
                styles.push('firstColumn');
            }
            html += '<th' + getStyles(styles) + '>' + localize(column) + '</th>';
        });
        html += '</tr><tbody>';
        return html;
    }

    function closeTable(obj) {
        /*
        obj exemple:
        {
            imbricated: true
        }
        */
        var html = '</tbody></table>';

        if (obj.imbricated) {
            html += '</td> </tr>';
        }

        return html;
    }

    function addRow(obj, fixedWidths) {
        /*
        obj exemple:
        {
            columns: [
                1,
                2,
            ]
        }
        fixedWidths example:
        [
            3: 360
        ]
            The key is the column index and the value is the fixed width we want
        */
        var html = '<tr' + getStyles(['tbodyTr'/*, 'avoidPageBreak'*/]) + '>';

        obj.columns.forEach((column, index) => {
            var styles = ['td'];
            if (index === 0) {
                styles.push('firstColumn');
            }

            // Default value
            var divWidthStyle = 'max-width: 300;';
            var tdWidthStyle = '';

            if (!!fixedWidths && !!fixedWidths[index])
            {
                divWidthStyle = `width: ${fixedWidths[index]}; max-width: ${fixedWidths[index]};`;
                tdWidthStyle = divWidthStyle;
            }

            var contentDivStyle = getStyles(['contentDiv'], divWidthStyle)


            html += '<td' + getStyles(styles, tdWidthStyle) + '><div ' + contentDivStyle + '>' + ((!!column || (column === 0)) ? column : '-') + '</div></td>';
        });

        html += '</tr>';

        return html;
    }

    try {
        // Validate mandatory inputs
        if (!BrowserTimeZone) {
            logError('BrowserTimeZone is a mandatory input.');
            return;
        }

        if (!SerialNumberUID) {
            logError('SerialNumberUID is a mandatory input.');
            return;
        }

        // Get SQL compliant timezone
        var timeZone = TOOLBOX.ConvertIANATimezoneToMicrosoftTimezone_TBX({
            IANATimeZoneName: BrowserTimeZone
        });

        // Process colors from configuration in styles
        var printConfigHTML = QA_REPORT_CONFIGURATION.GetConfigurationTable({
            tableName: 'QAReport_PrintConfigHTML'
        });

        if (!TOOLBOX.IsInfotableEmpty({ Infotable: printConfigHTML })) {
            var configs = printConfigHTML.rows[0];
            for (var config in configs) {
                if (!(/^#[0-9A-F]{6}[0-9a-f]{0,2}$/i.test(configs[config]))) {
                    logError('Invalid hex code for ' + config + ' in PrintConfigHTML on the QA Report Configuration. Using default value: ' + colorConfig[config]);
                    continue;
                }
                colorConfig[config] = configs[config];
            }
        }

        for (var style in STYLES) {
            if (STYLES.hasOwnProperty(style)) {
                var updatedStyle = STYLES[style].replace(
                    /{(\w+)}/g,
                    (placeholderWithDelimiters, placeholderWithoutDelimiters) =>
                        colorConfig[placeholderWithoutDelimiters] || placeholderWithDelimiters
                );
                STYLES[style] = updatedStyle;
            }
        }

        // Retrieve Serial Number info
        var serialNumbersArray = [];
        serialNumbersArray.push(SerialNumberUID);

        if (!TOOLBOX.IsInfotableEmpty({ Infotable: LinkedSerialNumberUIDs })) {
            LinkedSerialNumberUIDs.rows.toArray().forEach(row => {
                serialNumbersArray.push(row.SerialNumberUID);
            });
        }

        var snResult = QA_REPORT_MANAGER.GetQAReportOverview_CWC_EXT({
            SerialNumberUIDs: serialNumbersArray.join(','),
            Limit: serialNumbersArray.length,
            Offset: 0,
            OffsetTimezone: timeZone
        });

        if (TOOLBOX.IsInfotableEmpty({ Infotable: snResult })) {
            logError('Cannot retrieve SerialNumberInfo with specified SerialNumberUID.');
            return;
        }
        
        // Build HTML
        var html = startHTML();
        var routeResult;

        snResult.rows.toArray().forEach(row => {
            // Serial Number Details
            html += newTable ({
                tableClass: 'serialNumberTable',
                title: 'PTC.FSU.CWC.EXT.Global.Label.SerialNumberDetails',
                tdColspan: null,
                columns: [
                    'PTC.FSU.CWC.EXT.Global.Label.SerialNumber',
                    'PTC.FSU.COREUI.Label.WorkOrder',
                    'PTC.FSU.CWC.EXT.Global.Label.Route',
                    'PTC.FSU.CWC.EXT.Global.Label.Product',
                    'PTC.FSU.CWC.EXT.Global.Label.StartTime',
                    'PTC.FSU.CWC.EXT.Global.Label.EndTime',
                    'PTC.FSU.CWC.EXT.Global.Label.Status',
                    'PTC.FSU.CWC.EXT.Global.Label.ReviewedBy',
                    '',
                    'PTC.FSU.CWC.EXT.Global.Label.ReviewedOn'
                ]
            });

            row.Status = TOOLBOX.GetTokenTranslation({
				Token: row.Status
            });

            html+= addRow({
                columns: [
                    row.SerialNumber,
                    row.JobOrder,
                    row.Route,
                    row.Product,
                    dateToHTML(row.StartTime),
                    dateToHTML(row.EndTime),
                    row.Status,
                    row.ReleasedBy,
                    boolToHTML((row.ReleasedBy || '').length > 0 ? 1 : 0, E_SIG),
                    dateToHTML(row.ReleasedOn)
                ]
            });

            var openedImbricatedTable = false;
            if (IsDeviationEnabled) {
                var snDeviations = me.GetDeviations_CWC_EXT({
                    SerialNumberUID: row.SerialNumberUID,
                    OffsetTimezone: timeZone
                });
    
                if (snDeviations.getRowCount() > 0) {
    
                    openedImbricatedTable = true;
    
                    if (TOOLBOX.IsInfotableEmpty({ Infotable: snDeviations })) {
                        logError('Cannot retrieve Deviations for SerialNumberUID ' + row.SerialNumberUID + '.');
                        return;
                    }
                    // Deviation Table
                    html += newTable({
                        tableClass: 'notesTable',
                        title: 'PTC.FSU.CWC.EXT.QAReport.Grid.Deviations',
                        tdColspan: 11,
                        columns: [
                            'PTC.FSU.CWC.EXT.QAReport.Grid.Timestamp',
                            'PTC.FSU.COREUI.Label.Username',
                            'PTC.FSU.COREUI.Grid.Body'
                        ]
                    });
    
                    snDeviations.rows.toArray().forEach(item => {
                        html += addRow({
                            columns: [
                                dateToHTML(new Date(item.Modified)),
                                item.ModifiedBy,
                                item.Description
                            ]
                        });
                    });
                }  
            }

            // Closing serialNumberTable
            html += closeTable({ imbricated: openedImbricatedTable });
            
            // Route Details
            routeResult = me.GetQAReportRouteDetails_CWC_EXT({
                SerialNumberUIDs: row.SerialNumberUID,
                IncludeStepDetailMetrics: true,
                OffsetTimezone: timeZone
            });
    
            if (TOOLBOX.IsInfotableEmpty({ Infotable: routeResult })) {
                logError('Cannot retrieveQAReportRouteDetails with specified SerialNumberUID.');
                return;
            }

            routeResult.rows.toArray().forEach(route => {
                // Create route table
                html += newTable({
                    tableClass: 'routeTable',
                    title: 'PTC.FSU.CWC.EXT.Global.Label.RouteDetails',
                    tdColspan: null,
                    columns: [
                        '#',
                        'PTC.FSU.COREUI.Label.Equipment',
                        'PTC.FSU.CWC.EXT.Global.RouteDetailsCell.WorkInstruction',
                        'PTC.FSU.CWC.EXT.Global.Label.StartTime',
                        'PTC.FSU.CWC.EXT.Global.Label.EndTime',
                        'PTC.FSU.CWC.EXT.Global.Label.Status'
                    ]
                });

                // Translate status
                // Commented, status is translated in manager
                /*
                route.StatusToken = TOOLBOX.GetTokenTranslation({
                    Token: route.StatusToken
                });
                */

                html += addRow({
                    columns: [
                        route.SequenceNumber,
                        route.StationWorkCell,
                        route.WorkInstruction,
                        dateToHTML(route.StartTime),
                        dateToHTML(route.EndTime),
                        route.StatusName
                    ]
                });
                if (route.WorkDefinitionRootUID) {
                    var stepResult = me.GetQAReportStepDetails_CWC_EXT({
                        WorkDefinitionRootUID: route.WorkDefinitionRootUID,
                        OffsetTimezone: timeZone
                    });
    
                    if (TOOLBOX.IsInfotableEmpty({ Infotable: stepResult })) {
                        logError('Cannot retrieve QAReportStepDetails for WorkDefinitionRootUID ' + route.WorkDefinitionRootUID + '.');
                        return;
                    }
                    // Step Details
                    stepResult.rows.toArray().forEach(step => {
                        // Create step table
                        html += newTable({
                            tableClass: 'stepTable',
                            title: 'PTC.FSU.CWC.EXT.Global.Label.StepDetails',
                            tdColspan: 11,
                            columns: [
                                '',
                                '',
                                '#',
                                'PTC.FSU.CWC.Grid.StepName',
                                'PTC.FSU.CWC.Label.StepType',
                                'PTC.FSU.CWC.EXT.Label.User',
                                '',
                                'PTC.FSU.CWC.EXT.Global.Label.StartTime',
                                'PTC.FSU.CWC.EXT.Global.Label.EndTime',
                                'PTC.FSU.CWC.EXT.QAReport.Grid.Exceptions',
                                'PTC.FSU.CWC.EXT.QAReport.Grid.Deviations',
                                '',
                                'PTC.FSU.CWC.EXT.Label.Certificate',
                                '',
                                'PTC.FSU.CWC.EXT.Label.SignOffUser',
                                '',
                                '' // Notes and Attachments
                            ]
                        });

                        html += addRow({
                            columns: [
                                boolToHTML(!step.AttributeValue, NO_ENTRIES),
                                boolToHTML(step.HasExecutionEvent, EXECUTION_EVENT),
                                step.SequenceNumber,
                                step.StepName,
                                step.StepType,
                                step.Username,
                                boolToHTML(step.SelfSignOffESig, E_SIG),
                                dateToHTML(step.StartTime),
                                dateToHTML(step.EndTime),
                                step.ExceptionCount,
                                step.DeviationCount,
                                boolToHTML(step.CertificateRequired, CERTIFICATE),
                                step.CertificateName,
                                boolToHTML(step.CertificateBypassESig, E_SIG),
                                step.SignOffUser,
                                boolToHTML(step.SignOffESig, E_SIG),
                                boolToHTML(step.HasAttachments, ATTACHMENTS) + boolToHTML(step.HasNotes, NOTES)
                            ]
                        },
                        // Define cells with fixed widths and their index
                        {
                            3: 380
                        });
   
                        // Variable used to keep track of the tables opened under the step table to ensure they're displayed in a same row
                        openedImbricatedTable = false;

                        // Check if this should be treated as EmptyLogSheetSignOff
                        var effectiveStepType = step.StepType;
                        if (step.IsEmptyLogSheetSignOff === 1 && step.VariableCount === 0) {
                            effectiveStepType = 'EmptyLogSheetSignOff';
                        }
    
                        if (step.WorkDefinitionUID) {
                            if (effectiveStepType === 'EmptyLogSheetSignOff') {
                                var stepLogSheetSignOffs = me.GetStepLogSheetSignOffs_CWC_EXT({
                                    WorkDefinitionUID: step.WorkDefinitionUID,
                                    OffsetTimezone: timeZone
                                });

                                if (!TOOLBOX.IsInfotableEmpty({ Infotable: stepLogSheetSignOffs })) {
                                    var stepColumns = VARIABLE_FORMATS[effectiveStepType];
                                    var headers = [];
                                    if (stepColumns) {
                                        stepColumns.forEach(column => {
                                            headers.push(VARIABLE_COLUMNS[column].header);
                                        });
                                    
                                        // Empty Log Sheet Sign Off Details
                                        html += newTable({
                                            tableClass: 'variableTable',
                                            title: 'PTC.FSU.CWC.EXT.Global.Label.LogSheetSignOffs',
                                            tdColspan: 16,
                                            columns: headers
                                        });

                                        openedImbricatedTable = true;

                                        stepLogSheetSignOffs.rows.toArray().forEach(signOff => {
                                            var values = [];

                                            stepColumns.forEach(column => {
                                                values.push(VARIABLE_COLUMNS[column].getValue(signOff));
                                            });

                                            html += addRow({
                                                columns: values
                                            });
                                        });
                                    }
                                }
                            }
                            else if (step.StepType === 'Verification' && IncludeEndpointLoggingDetails) {
                                var stepEndpointDataResult = ENDPOINT_MANAGER.GetQAReportEndpointStepVariableDetails_CWC_EXT({
                                    InputJSON: JSON.stringify({
                                        WorkDefinitionUID: step.WorkDefinitionUID,
                                        Limit: 2147483647,
                                        Offset: 0,
                                        OrderAscending: false,
                                        OffsetTimezone: timeZone
                                    })
                                });
                                
                                if (!TOOLBOX.IsInfotableEmpty({ Infotable: stepEndpointDataResult })) {
                                    // Notes
                                    html += newTable({
                                        tableClass: 'notesTable',
                                        title: 'PTC.FSU.CWC.EXT.QAReport.Label.EndpointLoggingDetails',
                                        tdColspan: openedImbricatedTable ? null : 11,
                                        columns: [
                                            'PTC.FSU.CWC.EXT.QAReport.Label.EndpointName',
                                            'PTC.FSU.CWC.EXT.QAReport.Label.EndpointTypeName',
                                            'PTC.FSU.CWC.EXT.QAReport.Label.Timestamp',
                                            'PTC.FSU.CWC.EXT.QAReport.Label.URL',
                                            //'PTC.FSU.CWC.EXT.QAReport.Label.Body',
                                            'PTC.FSU.CWC.EXT.QAReport.Label.Response',
                                            'PTC.FSU.CWC.EXT.QAReport.Label.IsManualCall',
                                            'PTC.FSU.CWC.EXT.QAReport.Label.StatusCode',
                                            'PTC.FSU.CWC.EXT.QAReport.Label.IsManualOverride'
                                        ]
                                    });
    
                                    openedImbricatedTable = true;
                                    stepEndpointDataResult.rows.toArray().forEach(note => {
                                        html += addRow({
                                            columns: [
                                                note.EndpointTokenName,
                                                note.EndpointTypeTokenName,
                                                dateToHTML(note.Timestamp),
                                                note.URL,
                                                //JSON.stringify(JSON.parse(!!note.Body ? note.Body : '{}'), null, 2),
                                                note.Response ? note.Response : '{}',
                                                note.IsManualCall,
                                                note.StatusCode,
                                                note.IsManualOverride
                                            ]
                                        });
                                    });
                                }
                            }
                            else if (effectiveStepType !== 'Verification') {
                                var stepVarResult = QA_REPORT_MANAGER.GetQAReportStepVariableDetails_CWC_EXT({
                                    WorkDefinitionUIDs: step.WorkDefinitionUID,
                                    OffsetTimezone: BrowserTimeZone
                                });
    
                                if (!TOOLBOX.IsInfotableEmpty({ Infotable: stepVarResult })) {
                                    var varResult = stepVarResult.rows[0].Table0;
    
                                    var stepColumns = VARIABLE_FORMATS[effectiveStepType];
                                    var headers = [];
                                    if (stepColumns) {
                                        stepColumns.forEach(column => {
                                            headers.push(VARIABLE_COLUMNS[column].header);
                                        });
                                    
                                        // Variable Details
                                        html += newTable({
                                            tableClass: 'variableTable',
                                            title: 'PTC.FSU.CWC.EXT.Global.Label.VariableDetails',
                                            tdColspan: 16,
                                            columns: headers
                                        });

                                        openedImbricatedTable = true;

                                        varResult.rows.toArray().forEach(variable=> {
                                            var values = [];

                                            stepColumns.forEach(column => {
                                                if (column === 'AttributeValueTimestampString') {
                                                    variable.AttributeValueTimestampString = variable.AttributeValueTimestamp;
                                                }

                                                values.push(VARIABLE_COLUMNS[column].getValue(variable));
                                            });

                                            html += addRow({
                                                columns: values
                                            });
                                        });
                                	}
                                }
                            }
                        }

                        // Certificate Bypass Details
                        if (step.CertificateBypassESig) {
                            html += newTable({
                                tableClass: 'certBypassTable',
                                title: 'PTC.FSU.CWC.EXT.Label.CertificateBypassDetails',
                                tdColspan: openedImbricatedTable ? null : 13,
                                columns: [
                                    'PTC.FSU.CWC.EXT.Label.Certificate',
                                    '',
                                    'PTC.FSU.CWC.EXT.QAReport.CertificateBypassUser',
                                    'PTC.FSU.CWC.EXT.QAReport.BypassTimestamp'
                                ]
                            });

                            var certBypassDetails = me.GetCertificateDetailsFromWorkDefinitionUID_CWC_EXT({
                                WorkDefinitionUID: step.WorkDefinitionUID,
                                OffsetTimezone: timeZone
                            });

                            certBypassDetails.rows.toArray().forEach(cert => {

                                if (cert.ESig === true) {
                                    html += addRow({
                                        columns: [
                                            cert.CertificateName,
                                            boolToHTML(cert.ESig, E_SIG),
                                            cert.CertificateBypassUser,
                                            dateToHTML(cert.BypassTimestampConverted)
                                        ]
                                    });
                                }
                            });
                        }
    
                        // Notes Details
                        if (step.HasNotes) {
                            if (openedImbricatedTable) {
                                // Closing previous table without changing row
                                html += closeTable({ imbricated: false });
                            }
    
                            var notesResult = me.GetQAReportNotes_CWC_EXT({
                                WorkDefinitionUID: step.WorkDefinitionUID,
                                OffsetTimezone: timeZone
                            });
    
                            if (TOOLBOX.IsInfotableEmpty({ Infotable: notesResult })) {
                                logError('Cannot retrieve GetQAReportNotes for WorkDefinitionUID ' + step.WorkDefinitionUID + '.');
                                return;
                            }
    
                            html += newTable({
                                tableClass: 'notesTable',
                                title: 'PTC.FSU.COREUI.Grid.Notes',
                                tdColspan: openedImbricatedTable ? null : 13,
                                columns: [
                                    'PTC.FSU.COREUI.Grid.CreatedTime',
                                    'PTC.FSU.COREUI.Label.Username',
                                    'PTC.FSU.COREUI.Grid.Subject',
                                    'PTC.FSU.COREUI.Grid.Body',
                                    'PTC.FSU.CWC.EXT.Global.LogSheetTimestamp',
                                    'PTC.FSU.COREUI.Label.Context'
                                ]
                            });
    
                            openedImbricatedTable = true;
    
                            notesResult.rows.toArray().forEach(idx => {
                                html += addRow({
                                    columns: [
                                        dateToHTML(idx.Created),
                                        idx.Username,
                                        idx.Subject,
                                        idx.Body,
                                        dateToHTML(idx.LogSheetTimestamp),
                                        idx.Context
                                    ]
                                });
                            });
                        }
    
                        if (IsDeviationEnabled && step.DeviationCount > 0) {
                            if (openedImbricatedTable) {
                                // Closing previous table without changing row
                                html += closeTable({ imbricated: false });
                            }
    
                            var workDefDeviations = me.GetDeviations_CWC_EXT({
                                WorkDefinitionUID: step.WorkDefinitionUID,
                                OffsetTimezone: timeZone
                            });
    
                            if (TOOLBOX.IsInfotableEmpty({ Infotable: workDefDeviations })) {
                                logError('Cannot retrieve Deviations for WorkDefinitionUID ' + step.WorkDefinitionUID + '.');
                                return;
                            }
                            
                            // Deviations
                            html += newTable({
                                tableClass: 'notesTable',
                                title: 'PTC.FSU.CWC.EXT.QAReport.Grid.Deviations',
                                tdColspan: openedImbricatedTable ? null : 13,
                                columns: [
                                    'PTC.FSU.CWC.EXT.QAReport.Grid.Timestamp',
                                    'PTC.FSU.COREUI.Label.Username',
                                    'PTC.FSU.COREUI.Grid.Body'
                                ]
                            });
    
                            openedImbricatedTable = true;
                            workDefDeviations.rows.toArray().forEach(dev => {
                                html += addRow({
                                    columns: [
                                        dateToHTML(new Date(dev.Modified)),
                                        dev.ModifiedBy,
                                        dev.Description
                                    ]
                                });
                            });
                        }
    
                        if (IsManualExceptionEnabled && step.HasManualException > 0) {
                            if (openedImbricatedTable) {
                                // Closing previous table without changing row
                                html += closeTable({ imbricated: false });
                            }
    
                            var manualExceptions = me.GetManualExceptions_CWC_EXT({
                                WorkDefinitionUID: step.WorkDefinitionUID,
                                OffsetTimezone: timeZone
                            });
    
                            if (!TOOLBOX.IsInfotableEmpty({ Infotable: manualExceptions })) {
                                html += newTable({
                                    tableClass: 'notesTable',
                                    title: 'Manual Exceptions',
                                    tdColspan: openedImbricatedTable ? null : 13,
                                    columns: [
                                        'PTC.FSU.CWC.EXT.QAReport.Grid.Timestamp',
                                        'PTC.FSU.COREUI.Label.Username',
                                        'PTC.FSU.COREUI.Grid.Body'
                                    ]
                                });
        
                                openedImbricatedTable = true;
                                manualExceptions.rows.toArray().forEach(exc => {
                                    html += addRow({
                                        columns: [
                                            dateToHTML(new Date(exc.Modified)),
                                            exc.ModifiedBy,
                                            exc.Description
                                        ]
                                    });
                                });
                            }
                        }
    
                        if (step.HasAttachments) {
                            if (openedImbricatedTable) {
                                // Closing previous table without changing row
                                html += closeTable({ imbricated: false });
                            }
    
                            var attachmentResult = ATTACHMENT_MANAGER.GetAttachments_CWC_EXT({
                                ApplicationUID: step.WorkDefinitionUID,
                                ApplicationContext: 'STEP',
                                OffsetTimezone: timeZone
                            });
    
                            if (TOOLBOX.IsInfotableEmpty({ Infotable: attachmentResult })) {
                                logError('Cannot retrieve GetAttachments for WorkDefinitionUID ' + step.WorkDefinitionUID + '.');
                                return;
                            }
                            // Attachments
                            html += newTable({
                                tableClass: 'attachmentsTable',
                                title: 'PTC.FSU.CWC.EXT.Global.Label.Attachments',
                                tdColspan: openedImbricatedTable ? null : 13,
                                columns: [
                                    'PTC.FSU.COREUI.Grid.CreatedTime',
                                    'PTC.FSU.COREUI.Label.Username',
                                    'PTC.FSU.CORE.Label.Description',
                                    'PTC.FSU.CWC.Label.Filename',
                                    'PTC.FSU.CWC.EXT.Global.LogSheetTimestamp',
                                    'PTC.FSU.COREUI.Label.Context'
                                ]
                            });
    
                            openedImbricatedTable = true;
    
                            attachmentResult.rows.toArray().forEach(att => {
                                html += addRow({
                                    columns: [
                                        dateToHTML(att.CreatedOn),
                                        att.CreatedBy,
                                        att.Description,
                                        createLink({ url: getHost() + att.DownloadLink, text: att.DisplayName }),
                                        dateToHTML(att.LogSheetTimestamp),
                                        att.DocumentContextName

                                    ]
                                });
                            });
                        }
                            if (openedImbricatedTable) {
                                // Closing previous table and close row
                                html += closeTable({ imbricated: true });
                            }
                        // Closing stepTable
                        html += closeTable({ imbricated: false });
                    });
                }
                // Closing routeTable
                html += closeTable({ imbricated: false }); 
            });
            // Closing serial number Table
            html += closeTable({ imbricated: false }); 
        });
        html += closeHTML();

        result = html;
    } catch (e) {
        logError(e);
    }
})();