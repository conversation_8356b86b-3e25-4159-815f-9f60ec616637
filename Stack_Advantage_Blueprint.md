# **App Name**: Stack Advantage

## Purpose:
Stack Advantage is a **card counting trainer** that uses the rules of Blackjack to teach and reinforce **Hi-Lo card counting** techniques. The game is designed for both new learners and experienced counters who want to **hone their skills**, track performance, and improve decision-making under pressure.

## Core Features:

- **Blackjack Core Game**: Simulate a Blackjack game against a dealer with built-in Hi-Lo tracking.
- **Card Counting System – Hi-Lo Integration**:
    - Cards 2–6 are counted as **+1**.
    - Cards 7–9 are **neutral (0)**.
    - Cards 10, face cards, and Aces are **–1**.
    - Game tracks both **running count** and **true count** (based on remaining decks).
    - Count updates in the background (player view is optional or locked in challenge mode).
- **Counting Accuracy Feedback**:
    - After each hand or round, display a stat report comparing player actions against expected moves based on the count.
    - Scoring system to rate accuracy, consistency, and edge advantage exploitation.
    - Accuracy tracking includes **basic strategy deviations based on Hi-Lo count**, such as standing on 16 vs 10 at high counts, doubling 10 vs Ace, etc.
- **Training Modes**:
    - **Free Practice Mode**: No stakes, count displayed, focused on learning.
    - **Challenge Mode**: Hidden count, test decision-making under real pressure.
- **Play Stats**:
    - Track win/loss ratio, hand history, and count accuracy.
    - Record streaks, mistake patterns, and missed doubling/splitting opportunities.
- **Deck Penetration Simulation**:
    - Let users adjust how deep into the shoe the game runs before reshuffling.
    - Useful for training under different casino-like conditions.
- **Drills Mode**:
    - Flashcards or rapid-fire hands to test running count and true count.
    - Timed challenges to test recognition speed and accuracy.
- **Mistake Review Log**:
    - Replay hands where the user deviated from optimal counting decisions.
    - Show “correct play” with explanation and decision rationale.

## Blackjack Rules:
- **Decks**: Two standard decks.
- **Dealer**: Stands on all 17s (including soft 17).
- **Player Actions**: Hit, Stand, Double Down, Split.
- **Doubling Down**: Allowed on any two cards, including after splits.
- **Splitting**: Allowed for pairs. Double down after split is supported.
- Optional restriction on re-splitting initially.

## AI Strategy Hint:
- Optional AI hints for correct basic strategy (Hit/Stand/Double/Split) based on count.
- Hints are disabled in Challenge Mode for realism.

## Style Guidelines:

- Modern dark theme with black background (#000000) for the main surface.
- Dark gray (#1F2937) rounded buttons with good contrast against the black background.
- Card styling with centered suit/value, slight overlap between cards in hand for a professional look.
- Player stats tracking (correct vs. total decisions) to encourage learning optimal strategy.
- Accent color: Blue (#3B82F6) for interactive elements and Gold (#FFC107) for hints/special elements.
- Clean and intuitive layout optimized for both mobile and desktop screens.
- Use simple, clear icons for actions like 'Hit', 'Stand', 'Double Down', and 'Split'.
- Subtle animations for card dealing and win/loss outcomes.

## Leaderboard Integration (Planned Feature):

- **Global Leaderboard**:
    - Rank players by total profit, accuracy rating, and average true count decisions.
    - Filter by mode (Free Practice vs. Challenge).
- **Weekly Challenges**:
    - Rotate preset deck setups or game constraints for players to compete under the same conditions.
- **Friends & Local Ranking**:
    - Allow players to add friends and view performance among peers.
    - Possible integration with Game Center or Google Play Games for authentication.
- **Data Display**:
    - Show streaks, hands played, % of optimal plays, and final score.
    - Shareable results for social bragging and competitive motivation.

## Original User Request (for context):
Create me a blackjack game for iPhone where you try to build the biggest stack possible.

## Updated Goal:
Create a mobile card counting training tool that combines core Blackjack gameplay with Hi-Lo system education and performance analytics to maximize skill growth and strategic edge.
