(()=>{var nt=Object.create,Ee=Object.defineProperty,it=Object.getOwnPropertyDescriptor,Ne=Object.getOwnPropertyNames,ot=Object.getPrototypeOf,at=Object.prototype.hasOwnProperty,st=(n,t)=>function(){return t||(0,n[Ne(n)[0]])((t={exports:{}}).exports,t),t.exports},ut=(n,t,o,e)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Ne(t))!at.call(n,r)&&r!==o&&Ee(n,r,{get:()=>t[r],enumerable:!(e=it(t,r))||e.enumerable});return n},lt=(n,t,o)=>(o=n!=null?nt(ot(n)):{},ut(t||!n||!n.__esModule?Ee(o,"default",{value:n,enumerable:!0}):o,n)),ct=st({"lib/fingerprint2.min.js"(n,t){(function(o,e,r){"use strict";typeof t!="undefined"&&t.exports?t.exports=r():typeof define=="function"&&define.amd?define(r):e[o]=r()})("Fingerprint2",n,function(){"use strict";Array.prototype.indexOf||(Array.prototype.indexOf=function(e,r){var i;if(this==null)throw new TypeError("'this' is null or undefined");var a=Object(this),c=a.length>>>0;if(c===0)return-1;var s=+r||0;if(Math.abs(s)===1/0&&(s=0),s>=c)return-1;for(i=Math.max(s>=0?s:c-Math.abs(s),0);c>i;){if(i in a&&a[i]===e)return i;i++}return-1});var o=function(e){var r={swfContainerId:"fingerprintjs2",swfPath:"flash/compiled/FontList.swf",detectScreenOrientation:!0,sortPluginsFor:[/palemoon/i]};this.options=this.extend(e,r),this.nativeForEach=Array.prototype.forEach,this.nativeMap=Array.prototype.map};return o.prototype={extend:function(e,r){if(e==null)return r;for(var i in e)e[i]!=null&&r[i]!==e[i]&&(r[i]=e[i]);return r},log:function(e){window.console&&console.log(e)},get:function(e){var r=[];r=this.userAgentKey(r),r=this.languageKey(r),r=this.colorDepthKey(r),r=this.screenResolutionKey(r),r=this.availableScreenResolutionKey(r),r=this.timezoneOffsetKey(r),r=this.sessionStorageKey(r),r=this.localStorageKey(r),r=this.indexedDbKey(r),r=this.addBehaviorKey(r),r=this.openDatabaseKey(r),r=this.cpuClassKey(r),r=this.platformKey(r),r=this.doNotTrackKey(r),r=this.pluginsKey(r),r=this.canvasKey(r),r=this.webglKey(r),r=this.adBlockKey(r),r=this.hasLiedLanguagesKey(r),r=this.hasLiedResolutionKey(r),r=this.hasLiedOsKey(r),r=this.hasLiedBrowserKey(r),r=this.touchSupportKey(r);var i=this;this.fontsKey(r,function(a){var c=[];i.each(a,function(h){var g=h.value;typeof h.value.join!="undefined"&&(g=h.value.join(";")),c.push(g)});var s=i.x64hash128(c.join("~~~"),31);return e(s,a)})},userAgentKey:function(e){return this.options.excludeUserAgent||e.push({key:"user_agent",value:this.getUserAgent()}),e},getUserAgent:function(){return navigator.userAgent},languageKey:function(e){return this.options.excludeLanguage||e.push({key:"language",value:navigator.language||navigator.userLanguage||navigator.browserLanguage||navigator.systemLanguage}),e},colorDepthKey:function(e){return this.options.excludeColorDepth||e.push({key:"color_depth",value:screen.colorDepth}),e},screenResolutionKey:function(e){return this.options.excludeScreenResolution?e:this.getScreenResolution(e)},getScreenResolution:function(e){var r;return r=this.options.detectScreenOrientation&&screen.height>screen.width?[screen.height,screen.width]:[screen.width,screen.height],typeof r!="undefined"&&e.push({key:"resolution",value:r}),e},availableScreenResolutionKey:function(e){return this.options.excludeAvailableScreenResolution?e:this.getAvailableScreenResolution(e)},getAvailableScreenResolution:function(e){var r;return screen.availWidth&&screen.availHeight&&(r=this.options.detectScreenOrientation?screen.availHeight>screen.availWidth?[screen.availHeight,screen.availWidth]:[screen.availWidth,screen.availHeight]:[screen.availHeight,screen.availWidth]),typeof r!="undefined"&&e.push({key:"available_resolution",value:r}),e},timezoneOffsetKey:function(e){return this.options.excludeTimezoneOffset||e.push({key:"timezone_offset",value:new Date().getTimezoneOffset()}),e},sessionStorageKey:function(e){return!this.options.excludeSessionStorage&&this.hasSessionStorage()&&e.push({key:"session_storage",value:1}),e},localStorageKey:function(e){return!this.options.excludeSessionStorage&&this.hasLocalStorage()&&e.push({key:"local_storage",value:1}),e},indexedDbKey:function(e){return!this.options.excludeIndexedDB&&this.hasIndexedDB()&&e.push({key:"indexed_db",value:1}),e},addBehaviorKey:function(e){return document.body&&!this.options.excludeAddBehavior&&document.body.addBehavior&&e.push({key:"add_behavior",value:1}),e},openDatabaseKey:function(e){return!this.options.excludeOpenDatabase&&window.openDatabase&&e.push({key:"open_database",value:1}),e},cpuClassKey:function(e){return this.options.excludeCpuClass||e.push({key:"cpu_class",value:this.getNavigatorCpuClass()}),e},platformKey:function(e){return this.options.excludePlatform||e.push({key:"navigator_platform",value:this.getNavigatorPlatform()}),e},doNotTrackKey:function(e){return this.options.excludeDoNotTrack||e.push({key:"do_not_track",value:this.getDoNotTrack()}),e},canvasKey:function(e){return!this.options.excludeCanvas&&this.isCanvasSupported()&&e.push({key:"canvas",value:this.getCanvasFp()}),e},webglKey:function(e){return this.options.excludeWebGL||this.isWebGlSupported()&&e.push({key:"webgl",value:this.getWebglFp()}),e},adBlockKey:function(e){return this.options.excludeAdBlock||e.push({key:"adblock",value:this.getAdBlock()}),e},hasLiedLanguagesKey:function(e){return this.options.excludeHasLiedLanguages||e.push({key:"has_lied_languages",value:this.getHasLiedLanguages()}),e},hasLiedResolutionKey:function(e){return this.options.excludeHasLiedResolution||e.push({key:"has_lied_resolution",value:this.getHasLiedResolution()}),e},hasLiedOsKey:function(e){return this.options.excludeHasLiedOs||e.push({key:"has_lied_os",value:this.getHasLiedOs()}),e},hasLiedBrowserKey:function(e){return this.options.excludeHasLiedBrowser||e.push({key:"has_lied_browser",value:this.getHasLiedBrowser()}),e},fontsKey:function(e,r){return this.options.excludeJsFonts?this.flashFontsKey(e,r):this.jsFontsKey(e,r)},flashFontsKey:function(e,r){return this.options.excludeFlashFonts?r(e):this.hasSwfObjectLoaded()&&this.hasMinFlashInstalled()?typeof this.options.swfPath=="undefined"?r(e):void this.loadSwfAndDetectFonts(function(i){e.push({key:"swf_fonts",value:i.join(";")}),r(e)}):r(e)},jsFontsKey:function(e,r){var i=this;return setTimeout(function(){var a=["monospace","sans-serif","serif"],c="mmmmmmmmmmlli",s="72px",h=document.getElementsByTagName("body")[0],g=document.createElement("span");g.style.fontSize=s,g.innerHTML=c;var x={},C={};for(var d in a)g.style.fontFamily=a[d],h.appendChild(g),x[a[d]]=g.offsetWidth,C[a[d]]=g.offsetHeight,h.removeChild(g);var y=function(f){var v=!1;for(var b in a){g.style.fontFamily=f+","+a[b],h.appendChild(g);var k=g.offsetWidth!==x[a[b]]||g.offsetHeight!==C[a[b]];h.removeChild(g),v=v||k}return v},S=["Andale Mono","Arial","Arial Black","Arial Hebrew","Arial MT","Arial Narrow","Arial Rounded MT Bold","Arial Unicode MS","Bitstream Vera Sans Mono","Book Antiqua","Bookman Old Style","Calibri","Cambria","Cambria Math","Century","Century Gothic","Century Schoolbook","Comic Sans","Comic Sans MS","Consolas","Courier","Courier New","Garamond","Geneva","Georgia","Helvetica","Helvetica Neue","Impact","Lucida Bright","Lucida Calligraphy","Lucida Console","Lucida Fax","LUCIDA GRANDE","Lucida Handwriting","Lucida Sans","Lucida Sans Typewriter","Lucida Sans Unicode","Microsoft Sans Serif","Monaco","Monotype Corsiva","MS Gothic","MS Outlook","MS PGothic","MS Reference Sans Serif","MS Sans Serif","MS Serif","MYRIAD","MYRIAD PRO","Palatino","Palatino Linotype","Segoe Print","Segoe Script","Segoe UI","Segoe UI Light","Segoe UI Semibold","Segoe UI Symbol","Tahoma","Times","Times New Roman","Times New Roman PS","Trebuchet MS","Verdana","Wingdings","Wingdings 2","Wingdings 3"],p=["Abadi MT Condensed Light","Academy Engraved LET","ADOBE CASLON PRO","Adobe Garamond","ADOBE GARAMOND PRO","Agency FB","Aharoni","Albertus Extra Bold","Albertus Medium","Algerian","Amazone BT","American Typewriter","American Typewriter Condensed","AmerType Md BT","Andalus","Angsana New","AngsanaUPC","Antique Olive","Aparajita","Apple Chancery","Apple Color Emoji","Apple SD Gothic Neo","Arabic Typesetting","ARCHER","ARNO PRO","Arrus BT","Aurora Cn BT","AvantGarde Bk BT","AvantGarde Md BT","AVENIR","Ayuthaya","Bandy","Bangla Sangam MN","Bank Gothic","BankGothic Md BT","Baskerville","Baskerville Old Face","Batang","BatangChe","Bauer Bodoni","Bauhaus 93","Bazooka","Bell MT","Bembo","Benguiat Bk BT","Berlin Sans FB","Berlin Sans FB Demi","Bernard MT Condensed","BernhardFashion BT","BernhardMod BT","Big Caslon","BinnerD","Blackadder ITC","BlairMdITC TT","Bodoni 72","Bodoni 72 Oldstyle","Bodoni 72 Smallcaps","Bodoni MT","Bodoni MT Black","Bodoni MT Condensed","Bodoni MT Poster Compressed","Bookshelf Symbol 7","Boulder","Bradley Hand","Bradley Hand ITC","Bremen Bd BT","Britannic Bold","Broadway","Browallia New","BrowalliaUPC","Brush Script MT","Californian FB","Calisto MT","Calligrapher","Candara","CaslonOpnface BT","Castellar","Centaur","Cezanne","CG Omega","CG Times","Chalkboard","Chalkboard SE","Chalkduster","Charlesworth","Charter Bd BT","Charter BT","Chaucer","ChelthmITC Bk BT","Chiller","Clarendon","Clarendon Condensed","CloisterBlack BT","Cochin","Colonna MT","Constantia","Cooper Black","Copperplate","Copperplate Gothic","Copperplate Gothic Bold","Copperplate Gothic Light","CopperplGoth Bd BT","Corbel","Cordia New","CordiaUPC","Cornerstone","Coronet","Cuckoo","Curlz MT","DaunPenh","Dauphin","David","DB LCD Temp","DELICIOUS","Denmark","DFKai-SB","Didot","DilleniaUPC","DIN","DokChampa","Dotum","DotumChe","Ebrima","Edwardian Script ITC","Elephant","English 111 Vivace BT","Engravers MT","EngraversGothic BT","Eras Bold ITC","Eras Demi ITC","Eras Light ITC","Eras Medium ITC","EucrosiaUPC","Euphemia","Euphemia UCAS","EUROSTILE","Exotc350 Bd BT","FangSong","Felix Titling","Fixedsys","FONTIN","Footlight MT Light","Forte","FrankRuehl","Fransiscan","Freefrm721 Blk BT","FreesiaUPC","Freestyle Script","French Script MT","FrnkGothITC Bk BT","Fruitger","FRUTIGER","Futura","Futura Bk BT","Futura Lt BT","Futura Md BT","Futura ZBlk BT","FuturaBlack BT","Gabriola","Galliard BT","Gautami","Geeza Pro","Geometr231 BT","Geometr231 Hv BT","Geometr231 Lt BT","GeoSlab 703 Lt BT","GeoSlab 703 XBd BT","Gigi","Gill Sans","Gill Sans MT","Gill Sans MT Condensed","Gill Sans MT Ext Condensed Bold","Gill Sans Ultra Bold","Gill Sans Ultra Bold Condensed","Gisha","Gloucester MT Extra Condensed","GOTHAM","GOTHAM BOLD","Goudy Old Style","Goudy Stout","GoudyHandtooled BT","GoudyOLSt BT","Gujarati Sangam MN","Gulim","GulimChe","Gungsuh","GungsuhChe","Gurmukhi MN","Haettenschweiler","Harlow Solid Italic","Harrington","Heather","Heiti SC","Heiti TC","HELV","Herald","High Tower Text","Hiragino Kaku Gothic ProN","Hiragino Mincho ProN","Hoefler Text","Humanst 521 Cn BT","Humanst521 BT","Humanst521 Lt BT","Imprint MT Shadow","Incised901 Bd BT","Incised901 BT","Incised901 Lt BT","INCONSOLATA","Informal Roman","Informal011 BT","INTERSTATE","IrisUPC","Iskoola Pota","JasmineUPC","Jazz LET","Jenson","Jester","Jokerman","Juice ITC","Kabel Bk BT","Kabel Ult BT","Kailasa","KaiTi","Kalinga","Kannada Sangam MN","Kartika","Kaufmann Bd BT","Kaufmann BT","Khmer UI","KodchiangUPC","Kokila","Korinna BT","Kristen ITC","Krungthep","Kunstler Script","Lao UI","Latha","Leelawadee","Letter Gothic","Levenim MT","LilyUPC","Lithograph","Lithograph Light","Long Island","Lydian BT","Magneto","Maiandra GD","Malayalam Sangam MN","Malgun Gothic","Mangal","Marigold","Marion","Marker Felt","Market","Marlett","Matisse ITC","Matura MT Script Capitals","Meiryo","Meiryo UI","Microsoft Himalaya","Microsoft JhengHei","Microsoft New Tai Lue","Microsoft PhagsPa","Microsoft Tai Le","Microsoft Uighur","Microsoft YaHei","Microsoft Yi Baiti","MingLiU","MingLiU_HKSCS","MingLiU_HKSCS-ExtB","MingLiU-ExtB","Minion","Minion Pro","Miriam","Miriam Fixed","Mistral","Modern","Modern No. 20","Mona Lisa Solid ITC TT","Mongolian Baiti","MONO","MoolBoran","Mrs Eaves","MS LineDraw","MS Mincho","MS PMincho","MS Reference Specialty","MS UI Gothic","MT Extra","MUSEO","MV Boli","Nadeem","Narkisim","NEVIS","News Gothic","News GothicMT","NewsGoth BT","Niagara Engraved","Niagara Solid","Noteworthy","NSimSun","Nyala","OCR A Extended","Old Century","Old English Text MT","Onyx","Onyx BT","OPTIMA","Oriya Sangam MN","OSAKA","OzHandicraft BT","Palace Script MT","Papyrus","Parchment","Party LET","Pegasus","Perpetua","Perpetua Titling MT","PetitaBold","Pickwick","Plantagenet Cherokee","Playbill","PMingLiU","PMingLiU-ExtB","Poor Richard","Poster","PosterBodoni BT","PRINCETOWN LET","Pristina","PTBarnum BT","Pythagoras","Raavi","Rage Italic","Ravie","Ribbon131 Bd BT","Rockwell","Rockwell Condensed","Rockwell Extra Bold","Rod","Roman","Sakkal Majalla","Santa Fe LET","Savoye LET","Sceptre","Script","Script MT Bold","SCRIPTINA","Serifa","Serifa BT","Serifa Th BT","ShelleyVolante BT","Sherwood","Shonar Bangla","Showcard Gothic","Shruti","Signboard","SILKSCREEN","SimHei","Simplified Arabic","Simplified Arabic Fixed","SimSun","SimSun-ExtB","Sinhala Sangam MN","Sketch Rockwell","Skia","Small Fonts","Snap ITC","Snell Roundhand","Socket","Souvenir Lt BT","Staccato222 BT","Steamer","Stencil","Storybook","Styllo","Subway","Swis721 BlkEx BT","Swiss911 XCm BT","Sylfaen","Synchro LET","System","Tamil Sangam MN","Technical","Teletype","Telugu Sangam MN","Tempus Sans ITC","Terminal","Thonburi","Traditional Arabic","Trajan","TRAJAN PRO","Tristan","Tubular","Tunga","Tw Cen MT","Tw Cen MT Condensed","Tw Cen MT Condensed Extra Bold","TypoUpright BT","Unicorn","Univers","Univers CE 55 Medium","Univers Condensed","Utsaah","Vagabond","Vani","Vijaya","Viner Hand ITC","VisualUI","Vivaldi","Vladimir Script","Vrinda","Westminster","WHITNEY","Wide Latin","ZapfEllipt BT","ZapfHumnst BT","ZapfHumnst Dm BT","Zapfino","Zurich BlkEx BT","Zurich Ex BT","ZWAdobeF"];i.options.extendedJsFonts&&(S=S.concat(p));for(var u=[],l=0,m=S.length;m>l;l++)y(S[l])&&u.push(S[l]);e.push({key:"js_fonts",value:u}),r(e)},1)},pluginsKey:function(e){return this.options.excludePlugins||e.push(this.isIE()?{key:"ie_plugins",value:this.getIEPlugins()}:{key:"regular_plugins",value:this.getRegularPlugins()}),e},getRegularPlugins:function(){for(var e=[],r=0,i=navigator.plugins.length;i>r;r++)e.push(navigator.plugins[r]);return this.pluginsShouldBeSorted()&&(e=e.sort(function(a,c){return a.name>c.name?1:a.name<c.name?-1:0})),this.map(e,function(a){var c=this.map(a,function(s){return[s.type,s.suffixes].join("~")}).join(",");return[a.name,a.description,c].join("::")},this)},getIEPlugins:function(){if(window.ActiveXObject){var e=["AcroPDF.PDF","Adodb.Stream","AgControl.AgControl","DevalVRXCtrl.DevalVRXCtrl.1","MacromediaFlashPaper.MacromediaFlashPaper","Msxml2.DOMDocument","Msxml2.XMLHTTP","PDF.PdfCtrl","QuickTime.QuickTime","QuickTimeCheckObject.QuickTimeCheck.1","RealPlayer","RealPlayer.RealPlayer(tm) ActiveX Control (32-bit)","RealVideo.RealVideo(tm) ActiveX Control (32-bit)","Scripting.Dictionary","SWCtl.SWCtl","Shell.UIHelper","ShockwaveFlash.ShockwaveFlash","Skype.Detection","TDCCtl.TDCCtl","WMPlayer.OCX","rmocx.RealPlayer G2 Control","rmocx.RealPlayer G2 Control.1"];return this.map(e,function(r){try{return new ActiveXObject(r),r}catch(i){return null}})}return[]},pluginsShouldBeSorted:function(){for(var e=!1,r=0,i=this.options.sortPluginsFor.length;i>r;r++){var a=this.options.sortPluginsFor[r];if(navigator.userAgent.match(a)){e=!0;break}}return e},touchSupportKey:function(e){return this.options.excludeTouchSupport||e.push({key:"touch_support",value:this.getTouchSupport()}),e},hasSessionStorage:function(){try{return!!window.sessionStorage}catch(e){return!0}},hasLocalStorage:function(){try{return!!window.localStorage}catch(e){return!0}},hasIndexedDB:function(){try{return!!window.indexedDB}catch(e){return!0}},getNavigatorCpuClass:function(){return navigator.cpuClass?navigator.cpuClass:"unknown"},getNavigatorPlatform:function(){return navigator.platform?navigator.platform:"unknown"},getDoNotTrack:function(){return navigator.doNotTrack?navigator.doNotTrack:"unknown"},getTouchSupport:function(){var e=0,r=!1;typeof navigator.maxTouchPoints!="undefined"?e=navigator.maxTouchPoints:typeof navigator.msMaxTouchPoints!="undefined"&&(e=navigator.msMaxTouchPoints);try{document.createEvent("TouchEvent"),r=!0}catch(a){}var i="ontouchstart"in window;return[e,r,i]},getCanvasFp:function(){var e=[],r=document.createElement("canvas");r.width=2e3,r.height=200,r.style.display="inline";var i=r.getContext("2d");return i.rect(0,0,10,10),i.rect(2,2,6,6),e.push("canvas winding:"+(i.isPointInPath(5,5,"evenodd")===!1?"yes":"no")),i.textBaseline="alphabetic",i.fillStyle="#f60",i.fillRect(125,1,62,20),i.fillStyle="#069",i.font=this.options.dontUseFakeFontInCanvas?"11pt Arial":"11pt no-real-font-123",i.fillText("Cwm fjordbank glyphs vext quiz, \u{1F603}",2,15),i.fillStyle="rgba(102, 204, 0, 0.7)",i.font="18pt Arial",i.fillText("Cwm fjordbank glyphs vext quiz, \u{1F603}",4,45),i.globalCompositeOperation="multiply",i.fillStyle="rgb(255,0,255)",i.beginPath(),i.arc(50,50,50,0,2*Math.PI,!0),i.closePath(),i.fill(),i.fillStyle="rgb(0,255,255)",i.beginPath(),i.arc(100,50,50,0,2*Math.PI,!0),i.closePath(),i.fill(),i.fillStyle="rgb(255,255,0)",i.beginPath(),i.arc(75,100,50,0,2*Math.PI,!0),i.closePath(),i.fill(),i.fillStyle="rgb(255,0,255)",i.arc(75,75,75,0,2*Math.PI,!0),i.arc(75,75,25,0,2*Math.PI,!0),i.fill("evenodd"),e.push("canvas fp:"+r.toDataURL()),e.join("~")},getWebglFp:function(){var e,r=function(y){return e.clearColor(0,0,0,1),e.enable(e.DEPTH_TEST),e.depthFunc(e.LEQUAL),e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT),"["+y[0]+", "+y[1]+"]"},i=function(y){var S,p=y.getExtension("EXT_texture_filter_anisotropic")||y.getExtension("WEBKIT_EXT_texture_filter_anisotropic")||y.getExtension("MOZ_EXT_texture_filter_anisotropic");return p?(S=y.getParameter(p.MAX_TEXTURE_MAX_ANISOTROPY_EXT),S===0&&(S=2),S):null};if(e=this.getWebglCanvas(),!e)return null;var a=[],c="attribute vec2 attrVertex;varying vec2 varyinTexCoordinate;uniform vec2 uniformOffset;void main(){varyinTexCoordinate=attrVertex+uniformOffset;gl_Position=vec4(attrVertex,0,1);}",s="precision mediump float;varying vec2 varyinTexCoordinate;void main() {gl_FragColor=vec4(varyinTexCoordinate,0,1);}",h=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,h);var g=new Float32Array([-.2,-.9,0,.4,-.26,0,0,.732134444,0]);e.bufferData(e.ARRAY_BUFFER,g,e.STATIC_DRAW),h.itemSize=3,h.numItems=3;var x=e.createProgram(),C=e.createShader(e.VERTEX_SHADER);e.shaderSource(C,c),e.compileShader(C);var d=e.createShader(e.FRAGMENT_SHADER);return e.shaderSource(d,s),e.compileShader(d),e.attachShader(x,C),e.attachShader(x,d),e.linkProgram(x),e.useProgram(x),x.vertexPosAttrib=e.getAttribLocation(x,"attrVertex"),x.offsetUniform=e.getUniformLocation(x,"uniformOffset"),e.enableVertexAttribArray(x.vertexPosArray),e.vertexAttribPointer(x.vertexPosAttrib,h.itemSize,e.FLOAT,!1,0,0),e.uniform2f(x.offsetUniform,1,1),e.drawArrays(e.TRIANGLE_STRIP,0,h.numItems),e.canvas!=null&&a.push(e.canvas.toDataURL()),a.push("extensions:"+e.getSupportedExtensions().join(";")),a.push("webgl aliased line width range:"+r(e.getParameter(e.ALIASED_LINE_WIDTH_RANGE))),a.push("webgl aliased point size range:"+r(e.getParameter(e.ALIASED_POINT_SIZE_RANGE))),a.push("webgl alpha bits:"+e.getParameter(e.ALPHA_BITS)),a.push("webgl antialiasing:"+(e.getContextAttributes().antialias?"yes":"no")),a.push("webgl blue bits:"+e.getParameter(e.BLUE_BITS)),a.push("webgl depth bits:"+e.getParameter(e.DEPTH_BITS)),a.push("webgl green bits:"+e.getParameter(e.GREEN_BITS)),a.push("webgl max anisotropy:"+i(e)),a.push("webgl max combined texture image units:"+e.getParameter(e.MAX_COMBINED_TEXTURE_IMAGE_UNITS)),a.push("webgl max cube map texture size:"+e.getParameter(e.MAX_CUBE_MAP_TEXTURE_SIZE)),a.push("webgl max fragment uniform vectors:"+e.getParameter(e.MAX_FRAGMENT_UNIFORM_VECTORS)),a.push("webgl max render buffer size:"+e.getParameter(e.MAX_RENDERBUFFER_SIZE)),a.push("webgl max texture image units:"+e.getParameter(e.MAX_TEXTURE_IMAGE_UNITS)),a.push("webgl max texture size:"+e.getParameter(e.MAX_TEXTURE_SIZE)),a.push("webgl max varying vectors:"+e.getParameter(e.MAX_VARYING_VECTORS)),a.push("webgl max vertex attribs:"+e.getParameter(e.MAX_VERTEX_ATTRIBS)),a.push("webgl max vertex texture image units:"+e.getParameter(e.MAX_VERTEX_TEXTURE_IMAGE_UNITS)),a.push("webgl max vertex uniform vectors:"+e.getParameter(e.MAX_VERTEX_UNIFORM_VECTORS)),a.push("webgl max viewport dims:"+r(e.getParameter(e.MAX_VIEWPORT_DIMS))),a.push("webgl red bits:"+e.getParameter(e.RED_BITS)),a.push("webgl renderer:"+e.getParameter(e.RENDERER)),a.push("webgl shading language version:"+e.getParameter(e.SHADING_LANGUAGE_VERSION)),a.push("webgl stencil bits:"+e.getParameter(e.STENCIL_BITS)),a.push("webgl vendor:"+e.getParameter(e.VENDOR)),a.push("webgl version:"+e.getParameter(e.VERSION)),e.getShaderPrecisionFormat&&(a.push("webgl vertex shader high float precision:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.HIGH_FLOAT).precision),a.push("webgl vertex shader high float precision rangeMin:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.HIGH_FLOAT).rangeMin),a.push("webgl vertex shader high float precision rangeMax:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.HIGH_FLOAT).rangeMax),a.push("webgl vertex shader medium float precision:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.MEDIUM_FLOAT).precision),a.push("webgl vertex shader medium float precision rangeMin:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.MEDIUM_FLOAT).rangeMin),a.push("webgl vertex shader medium float precision rangeMax:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.MEDIUM_FLOAT).rangeMax),a.push("webgl vertex shader low float precision:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.LOW_FLOAT).precision),a.push("webgl vertex shader low float precision rangeMin:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.LOW_FLOAT).rangeMin),a.push("webgl vertex shader low float precision rangeMax:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.LOW_FLOAT).rangeMax),a.push("webgl fragment shader high float precision:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.HIGH_FLOAT).precision),a.push("webgl fragment shader high float precision rangeMin:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.HIGH_FLOAT).rangeMin),a.push("webgl fragment shader high float precision rangeMax:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.HIGH_FLOAT).rangeMax),a.push("webgl fragment shader medium float precision:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.MEDIUM_FLOAT).precision),a.push("webgl fragment shader medium float precision rangeMin:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.MEDIUM_FLOAT).rangeMin),a.push("webgl fragment shader medium float precision rangeMax:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.MEDIUM_FLOAT).rangeMax),a.push("webgl fragment shader low float precision:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.LOW_FLOAT).precision),a.push("webgl fragment shader low float precision rangeMin:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.LOW_FLOAT).rangeMin),a.push("webgl fragment shader low float precision rangeMax:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.LOW_FLOAT).rangeMax),a.push("webgl vertex shader high int precision:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.HIGH_INT).precision),a.push("webgl vertex shader high int precision rangeMin:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.HIGH_INT).rangeMin),a.push("webgl vertex shader high int precision rangeMax:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.HIGH_INT).rangeMax),a.push("webgl vertex shader medium int precision:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.MEDIUM_INT).precision),a.push("webgl vertex shader medium int precision rangeMin:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.MEDIUM_INT).rangeMin),a.push("webgl vertex shader medium int precision rangeMax:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.MEDIUM_INT).rangeMax),a.push("webgl vertex shader low int precision:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.LOW_INT).precision),a.push("webgl vertex shader low int precision rangeMin:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.LOW_INT).rangeMin),a.push("webgl vertex shader low int precision rangeMax:"+e.getShaderPrecisionFormat(e.VERTEX_SHADER,e.LOW_INT).rangeMax),a.push("webgl fragment shader high int precision:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.HIGH_INT).precision),a.push("webgl fragment shader high int precision rangeMin:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.HIGH_INT).rangeMin),a.push("webgl fragment shader high int precision rangeMax:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.HIGH_INT).rangeMax),a.push("webgl fragment shader medium int precision:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.MEDIUM_INT).precision),a.push("webgl fragment shader medium int precision rangeMin:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.MEDIUM_INT).rangeMin),a.push("webgl fragment shader medium int precision rangeMax:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.MEDIUM_INT).rangeMax),a.push("webgl fragment shader low int precision:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.LOW_INT).precision),a.push("webgl fragment shader low int precision rangeMin:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.LOW_INT).rangeMin),a.push("webgl fragment shader low int precision rangeMax:"+e.getShaderPrecisionFormat(e.FRAGMENT_SHADER,e.LOW_INT).rangeMax)),a.join("~")},getAdBlock:function(){var e=document.createElement("div");e.setAttribute("id","ads");try{return document.body.appendChild(e),!document.getElementById("ads")}catch(r){return!1}},getHasLiedLanguages:function(){if(typeof navigator.languages!="undefined")try{var e=navigator.languages[0].substr(0,2);if(e!==navigator.language.substr(0,2))return!0}catch(r){return!0}return!1},getHasLiedResolution:function(){return screen.width<screen.availWidth?!0:screen.height<screen.availHeight},getHasLiedOs:function(){var e,r=navigator.userAgent.toLowerCase(),i=navigator.oscpu,a=navigator.platform.toLowerCase();e=r.indexOf("windows phone")>=0?"Windows Phone":r.indexOf("win")>=0?"Windows":r.indexOf("android")>=0?"Android":r.indexOf("linux")>=0?"Linux":r.indexOf("iphone")>=0||r.indexOf("ipad")>=0?"iOS":r.indexOf("mac")>=0?"Mac":"Other";var c;return c="ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0,c&&e!=="Windows Phone"&&e!=="Android"&&e!=="iOS"&&e!=="Other"||typeof i!="undefined"&&(i=i.toLowerCase(),i.indexOf("win")>=0&&e!=="Windows"&&e!=="Windows Phone"||i.indexOf("linux")>=0&&e!=="Linux"&&e!=="Android"||i.indexOf("mac")>=0&&e!=="Mac"&&e!=="iOS"||i.indexOf("win")===0&&i.indexOf("linux")===0&&i.indexOf("mac")>=0&&e!=="other")||a.indexOf("win")>=0&&e!=="Windows"&&e!=="Windows Phone"||(a.indexOf("linux")>=0||a.indexOf("android")>=0||a.indexOf("pike")>=0)&&e!=="Linux"&&e!=="Android"||(a.indexOf("mac")>=0||a.indexOf("ipad")>=0||a.indexOf("ipod")>=0||a.indexOf("iphone")>=0)&&e!=="Mac"&&e!=="iOS"||a.indexOf("win")===0&&a.indexOf("linux")===0&&a.indexOf("mac")>=0&&e!=="other"?!0:typeof navigator.plugins=="undefined"&&e!=="Windows"&&e!=="Windows Phone"},getHasLiedBrowser:function(){var e,r=navigator.userAgent.toLowerCase(),i=navigator.productSub;if(e=r.indexOf("firefox")>=0?"Firefox":r.indexOf("opera")>=0||r.indexOf("opr")>=0?"Opera":r.indexOf("chrome")>=0?"Chrome":r.indexOf("safari")>=0?"Safari":r.indexOf("trident")>=0?"Internet Explorer":"Other",(e==="Chrome"||e==="Safari"||e==="Opera")&&i!=="20030107")return!0;var a=eval.toString().length;if(a===37&&e!=="Safari"&&e!=="Firefox"&&e!=="Other"||a===39&&e!=="Internet Explorer"&&e!=="Other"||a===33&&e!=="Chrome"&&e!=="Opera"&&e!=="Other")return!0;var c;try{throw"a"}catch(s){try{s.toSource(),c=!0}catch(h){c=!1}}return!!(c&&e!=="Firefox"&&e!=="Other")},isCanvasSupported:function(){var e=document.createElement("canvas");return!(!e.getContext||!e.getContext("2d"))},isWebGlSupported:function(){if(!this.isCanvasSupported())return!1;var e,r=document.createElement("canvas");try{e=r.getContext&&(r.getContext("webgl")||r.getContext("experimental-webgl"))}catch(i){e=!1}return!!window.WebGLRenderingContext&&!!e},isIE:function(){return navigator.appName==="Microsoft Internet Explorer"?!0:!!(navigator.appName==="Netscape"&&/Trident/.test(navigator.userAgent))},hasSwfObjectLoaded:function(){return typeof window.swfobject!="undefined"},hasMinFlashInstalled:function(){return swfobject.hasFlashPlayerVersion("9.0.0")},addFlashDivNode:function(){var e=document.createElement("div");e.setAttribute("id",this.options.swfContainerId),document.body.appendChild(e)},loadSwfAndDetectFonts:function(e){var r="___fp_swf_loaded";window[r]=function(s){e(s)};var i=this.options.swfContainerId;this.addFlashDivNode();var a={onReady:r},c={allowScriptAccess:"always",menu:"false"};swfobject.embedSWF(this.options.swfPath,i,"1","1","9.0.0",!1,a,c,{})},getWebglCanvas:function(){var e=document.createElement("canvas"),r=null;try{r=e.getContext("webgl")||e.getContext("experimental-webgl")}catch(i){}return r||(r=null),r},each:function(e,r,i){if(e!==null){if(this.nativeForEach&&e.forEach===this.nativeForEach)e.forEach(r,i);else if(e.length===+e.length){for(var a=0,c=e.length;c>a;a++)if(r.call(i,e[a],a,e)==={})return}else for(var s in e)if(e.hasOwnProperty(s)&&r.call(i,e[s],s,e)==={})return}},map:function(e,r,i){var a=[];return e==null?a:this.nativeMap&&e.map===this.nativeMap?e.map(r,i):(this.each(e,function(c,s,h){a[a.length]=r.call(i,c,s,h)}),a)},x64Add:function(e,r){e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]],r=[r[0]>>>16,65535&r[0],r[1]>>>16,65535&r[1]];var i=[0,0,0,0];return i[3]+=e[3]+r[3],i[2]+=i[3]>>>16,i[3]&=65535,i[2]+=e[2]+r[2],i[1]+=i[2]>>>16,i[2]&=65535,i[1]+=e[1]+r[1],i[0]+=i[1]>>>16,i[1]&=65535,i[0]+=e[0]+r[0],i[0]&=65535,[i[0]<<16|i[1],i[2]<<16|i[3]]},x64Multiply:function(e,r){e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]],r=[r[0]>>>16,65535&r[0],r[1]>>>16,65535&r[1]];var i=[0,0,0,0];return i[3]+=e[3]*r[3],i[2]+=i[3]>>>16,i[3]&=65535,i[2]+=e[2]*r[3],i[1]+=i[2]>>>16,i[2]&=65535,i[2]+=e[3]*r[2],i[1]+=i[2]>>>16,i[2]&=65535,i[1]+=e[1]*r[3],i[0]+=i[1]>>>16,i[1]&=65535,i[1]+=e[2]*r[2],i[0]+=i[1]>>>16,i[1]&=65535,i[1]+=e[3]*r[1],i[0]+=i[1]>>>16,i[1]&=65535,i[0]+=e[0]*r[3]+e[1]*r[2]+e[2]*r[1]+e[3]*r[0],i[0]&=65535,[i[0]<<16|i[1],i[2]<<16|i[3]]},x64Rotl:function(e,r){return r%=64,r===32?[e[1],e[0]]:32>r?[e[0]<<r|e[1]>>>32-r,e[1]<<r|e[0]>>>32-r]:(r-=32,[e[1]<<r|e[0]>>>32-r,e[0]<<r|e[1]>>>32-r])},x64LeftShift:function(e,r){return r%=64,r===0?e:32>r?[e[0]<<r|e[1]>>>32-r,e[1]<<r]:[e[1]<<r-32,0]},x64Xor:function(e,r){return[e[0]^r[0],e[1]^r[1]]},x64Fmix:function(e){return e=this.x64Xor(e,[0,e[0]>>>1]),e=this.x64Multiply(e,[4283543511,3981806797]),e=this.x64Xor(e,[0,e[0]>>>1]),e=this.x64Multiply(e,[3301882366,444984403]),e=this.x64Xor(e,[0,e[0]>>>1])},x64hash128:function(e,r){e=e||"",r=r||0;for(var i=e.length%16,a=e.length-i,c=[0,r],s=[0,r],h=[0,0],g=[0,0],x=[2277735313,289559509],C=[1291169091,658871167],d=0;a>d;d+=16)h=[255&e.charCodeAt(d+4)|(255&e.charCodeAt(d+5))<<8|(255&e.charCodeAt(d+6))<<16|(255&e.charCodeAt(d+7))<<24,255&e.charCodeAt(d)|(255&e.charCodeAt(d+1))<<8|(255&e.charCodeAt(d+2))<<16|(255&e.charCodeAt(d+3))<<24],g=[255&e.charCodeAt(d+12)|(255&e.charCodeAt(d+13))<<8|(255&e.charCodeAt(d+14))<<16|(255&e.charCodeAt(d+15))<<24,255&e.charCodeAt(d+8)|(255&e.charCodeAt(d+9))<<8|(255&e.charCodeAt(d+10))<<16|(255&e.charCodeAt(d+11))<<24],h=this.x64Multiply(h,x),h=this.x64Rotl(h,31),h=this.x64Multiply(h,C),c=this.x64Xor(c,h),c=this.x64Rotl(c,27),c=this.x64Add(c,s),c=this.x64Add(this.x64Multiply(c,[0,5]),[0,1390208809]),g=this.x64Multiply(g,C),g=this.x64Rotl(g,33),g=this.x64Multiply(g,x),s=this.x64Xor(s,g),s=this.x64Rotl(s,31),s=this.x64Add(s,c),s=this.x64Add(this.x64Multiply(s,[0,5]),[0,944331445]);switch(h=[0,0],g=[0,0],i){case 15:g=this.x64Xor(g,this.x64LeftShift([0,e.charCodeAt(d+14)],48));case 14:g=this.x64Xor(g,this.x64LeftShift([0,e.charCodeAt(d+13)],40));case 13:g=this.x64Xor(g,this.x64LeftShift([0,e.charCodeAt(d+12)],32));case 12:g=this.x64Xor(g,this.x64LeftShift([0,e.charCodeAt(d+11)],24));case 11:g=this.x64Xor(g,this.x64LeftShift([0,e.charCodeAt(d+10)],16));case 10:g=this.x64Xor(g,this.x64LeftShift([0,e.charCodeAt(d+9)],8));case 9:g=this.x64Xor(g,[0,e.charCodeAt(d+8)]),g=this.x64Multiply(g,C),g=this.x64Rotl(g,33),g=this.x64Multiply(g,x),s=this.x64Xor(s,g);case 8:h=this.x64Xor(h,this.x64LeftShift([0,e.charCodeAt(d+7)],56));case 7:h=this.x64Xor(h,this.x64LeftShift([0,e.charCodeAt(d+6)],48));case 6:h=this.x64Xor(h,this.x64LeftShift([0,e.charCodeAt(d+5)],40));case 5:h=this.x64Xor(h,this.x64LeftShift([0,e.charCodeAt(d+4)],32));case 4:h=this.x64Xor(h,this.x64LeftShift([0,e.charCodeAt(d+3)],24));case 3:h=this.x64Xor(h,this.x64LeftShift([0,e.charCodeAt(d+2)],16));case 2:h=this.x64Xor(h,this.x64LeftShift([0,e.charCodeAt(d+1)],8));case 1:h=this.x64Xor(h,[0,e.charCodeAt(d)]),h=this.x64Multiply(h,x),h=this.x64Rotl(h,31),h=this.x64Multiply(h,C),c=this.x64Xor(c,h)}return c=this.x64Xor(c,[0,e.length]),s=this.x64Xor(s,[0,e.length]),c=this.x64Add(c,s),s=this.x64Add(s,c),c=this.x64Fmix(c),s=this.x64Fmix(s),c=this.x64Add(c,s),s=this.x64Add(s,c),("00000000"+(c[0]>>>0).toString(16)).slice(-8)+("00000000"+(c[1]>>>0).toString(16)).slice(-8)+("00000000"+(s[0]>>>0).toString(16)).slice(-8)+("00000000"+(s[1]>>>0).toString(16)).slice(-8)}},o.VERSION="1.1.0",o})}}),pt,z=pt=window.$,dt=I.FilterPickers=function(){n.prototype.label_padding=10;function n(t,o){var e,r;this.opts=o!=null?o:{},this.el=$(t),e=function(i){return function(){return i.el.find(".filter_picker_widget")}}(this),this.opts.label_padding&&(this.label_padding=this.opts.label_padding),r=function(i){return function(){return e().removeClass("open popup_visible").find(".filter_options").css({marginTop:""})}}(this),$(window).on("click",function(i){return function(a){if(!$(a.target).closest(".filter_picker_widget").length)return r()}}(this)),this.el.on("i:close_filter_pickers",r),this.el.on("click",".filter_picker_widget .filter_value",function(i){return function(a){var c,s,h;return a.stopPropagation(),a.preventDefault(),s=$(a.currentTarget).closest(".filter_picker_widget"),e().not(s).removeClass("open popup_visible"),s.toggleClass("open"),s.is(".open")&&(s.trigger("i:track_link"),c=s.find(".filter_value").height(),s.find(".filter_options").css({marginTop:c+i.label_padding*2+"px",minWidth:s.width()+30+"px"}),h=$(window).width()-s.position().left+s.width(),s.toggleClass("popup_left",h<200)),_.defer(function(){return s.toggleClass("popup_visible",s.is(".open"))})}}(this))}return n}();window.init_CommunityCategoryModerators=function(n,t){let o=z(n);o.remote_link(function(e,r){e.return_to&&(window.location=e.return_to)}),new dt(o)};var D=window.I,T=window.React,Ie=window.ReactDOM,A=window.ReactDOMFactories,ht=window.ReactTransitionGroup,N=window.PropTypes,j=window.classNames,L=window.R;T||D.libs.react.done(function(){return T=window.React,Ie=window.ReactDOM,A=window.ReactDOMFactories,ht=window.ReactTransitionGroup,N=window.PropTypes,j=window.classNames,L=window.R});var U=function(n){return D.libs.react.done(n)},Re=null;U(function(){var n,t;return n=A,t=n.img,Re=L.component("LazyImage",{pure:!0,propTypes:{src:N.string,src_set:N.string,width:N.number,height:N.number},getInitialState:function(){return{visible:!1}},componentDidMount:function(){var o;return o=this.image_ref.current,this.unbind_lazy_images=$(o).lazy_images({elements:[o],show_images:function(e){return function(){var r;return e.setState({visible:!0}),typeof(r=e.props).on_reveal=="function"?r.on_reveal():void 0}}(this)})},componentWillUnmount:function(){return typeof this.unbind_lazy_images=="function"?this.unbind_lazy_images():void 0},render:function(){var o,e;return t({ref:this.image_ref||(this.image_ref=T.createRef()),className:j(this.props.class,this.props.className,{lazy_loaded:this.state.loaded,lazy_visible:this.state.visible}),alt:this.props.alt,width:this.state.loaded?this.props.width:(o=this.props.initial_width)!=null?o:this.props.width,height:this.state.loaded?this.props.height:(e=this.props.initial_height)!=null?e:this.props.height,src:this.state.visible?this.props.src:void 0,srcSet:this.state.visible?this.props.src_set:void 0,onLoad:this.state.visible&&!this.state.loaded?function(r){return function(){var i;return r.setState({loaded:!0}),typeof(i=r.props).on_load=="function"?i.on_load():void 0}}(this):void 0})}})});var pe=null;U(function(){var n,t;return n=A,t=n.div,pe=L.component("SlideDown",{getInitialState:function(){return{}},getDefaultProps:function(){return{duration:200,delay:1}},componentDidMount:function(){return this.timer=window.setTimeout(function(o){return function(){var e;return e=o.wrapper_ref.current,o.setState({height:e.scrollHeight||!1}),o.timer=window.setTimeout(function(){return o.setState({animated:!0})},o.props.duration+50),e.scrollTop=0}}(this),this.props.delay)},componentWillUnmount:function(){if(this.timer)return window.clearTimeout(this.timer),delete this.timer},render:function(){var o;return o=this.state.height===!1?null:this.state.height?this.state.animated?null:{height:this.state.height+"px",overflow:"hidden",transition:"height "+this.props.duration/1e3+"s ease"}:{height:0,overflow:"scroll",transition:"height 0.2s ease"},t({style:o,className:this.props.className,ref:this.wrapper_ref||(this.wrapper_ref=T.createRef())},this.props.children)}})});var ye=null;U(function(){return ye=L("LoadOnScroll",{componentWillUnmount:function(){return typeof this.unbind_visibility=="function"?this.unbind_visibility():void 0},componentDidMount:function(){var n;return n=Ie.findDOMNode(this),this.unbind_visibility=$(n).lazy_images({elements:[n],show_images:function(t){return function(){var o;return(o=t.props)!=null?o.on_seen():void 0}}(this)})},render:function(){return this.props.children}})});var de=window._,he=null;U(function(){var n,t;return n=A,t=n.img,he=function(o){var e,r,i,a,c;return o==null&&(o={}),c=(r=o.width)!=null?r:24,e=(i=o.height)!=null?i:c,T.createElement("svg",{className:"svgicon icon_close",strokeLinecap:"round",stroke:"currentColor",role:"img",version:"1.1",viewBox:"0 0 24 24",strokeWidth:(a=o.stroke_width)!=null?a:"2",width:c,height:e,strokeLinejoin:"round","aria-hidden":!0,fill:"none"},T.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),T.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))},he=T.createElement.bind(null,T.memo(he))});var Q=null,ie=null,Y=null;U(function(){var n,t,o,e,r,i,a,c;return n=A,t=n.a,r=n.div,o=n.button,a=n.label,i=n.h2,c=n.p,e=null,Y=function(s){return s.promise?(I.Lightbox.open_loading(),s.then(function(h){return function(g){return I.Lightbox.open(g)}}(this),function(h){return function(g){return I.Lightbox.open(ie({errors:g.errors}))}}(this))):I.Lightbox.open(s)},Q=L.component("Lightbox",{is_modal_dialog:function(){var s;return s=document.getElementById("lightbox_container"),s!=null?s.contains(this.container_ref.current):void 0},getInitialState:function(){return{}},componentDidMount:function(){if(this.is_modal_dialog())return e?console.warn("A dialog already has the focus trap"):(this.detect_focus=function(s){return function(h){var g;if(g=s.container_ref.current,g&&"contains"in g&&!(g===h.target||g.contains(h.target)))return g.focus()}}(this),$(document.body).on("focusin",this.detect_focus),e=this),this.setState({previously_focused:document.activeElement,is_modal_dialog:!0},function(){return _.defer(function(s){return function(){var h;return(h=s.container_ref.current)!=null?h.focus():void 0}}(this))})},componentWillUnmount:function(){var s;return this.detect_focus&&($(document.body).off("focusin",this.detect_focus),e=null,delete this.detect_focus),(s=this.state.previously_focused)!=null?s.focus():void 0},close:function(){return I.Lightbox.close()},render:function(){return r({className:classNames("lightbox",this.props.className),style:this.props.style,role:this.state.is_modal_dialog?"dialog":void 0,"aria-modal":this.state.is_modal_dialog?"true":void 0,tabIndex:this.state.is_modal_dialog?-1:void 0,ref:this.container_ref||(this.container_ref=React.createRef())},this.props.close!==!1?o({className:"close_button",type:"button","aria-label":"Close Dialog"},he({width:18})):void 0,this.props.children)}}),ie=L.component("ErrorLightbox",{propTypes:{title:N.string,errors:N.array.isRequired},render:function(){return Q({className:classNames(this.enclosing_class_name(),"compact")},i({},this.props.title||this.tt("misc.lightboxes.error_title")),L.Forms.FormErrors({title:!1,errors:this.props.errors}),c({className:"buttons"},o({className:"button",type:"button",onClick:function(s){return function(){return I.Lightbox.close()}}(this)},"Close")))}})}),D.Lightbox||console.warn("I.Lightbox is being depended on but is missing from the page");var oe=D.Lightbox,ft=function(n,t){for(var o in t)mt.call(t,o)&&(n[o]=t[o]);function e(){this.constructor=n}return e.prototype=t.prototype,n.prototype=new e,n.__super__=t.prototype,n},mt={}.hasOwnProperty,Pe=function(n){ft(t,n);function t(){return t.__super__.constructor.apply(this,arguments)}return t.prototype.init=function(o){return this.el.dispatch("click",{tab_btn:function(e){return function(r){var i;return i=r.data("tab"),e.el.find(".tab_content").hide().filter("[data-tab="+i+"]").show(),e.el.find(".tab_btn").removeClass("selected").filter(r).addClass("selected")}}(this),pick_image_btn:function(e){return function(r){return typeof o=="function"?o(r.data("url")):void 0}}(this),upload_image_btn:function(e){return function(r){return D.upload_image({url:D.root_url("dashboard/upload-image"),thumb_size:"original"}).progress(function(){if(!r.prop("disabled"))return r.prop("disabled",!0).addClass("disabled"),r.data("original_text",r.text()),r.text("Uploading...")}).fail(function(){return r.prop("disabled",!1).removeClass("disabled"),r.text(r.data("original_text"))}).done(function(i){var a,c;return r.prop("disabled",!1).removeClass("disabled"),r.text(r.data("original_text")),i.success?typeof o=="function"?o(i.upload.thumb_url):void 0:(a=((c=i.errors)!=null?c[0]:void 0)||"Image upload failed",D.flash(a,"error"))})}}(this)})},t}(oe),fe,Ae,ee=function(n,t){return function(){return n.apply(t,arguments)}},Le=function(n,t){for(var o in t)_t.call(t,o)&&(n[o]=t[o]);function e(){this.constructor=n}return e.prototype=t.prototype,n.prototype=new e,n.__super__=t.prototype,n},_t={}.hasOwnProperty,De=function(n){var t,o;return o=function(e){return e.errors?$.Deferred().reject(e):e},t=function(e){return $.Deferred().reject({errors:["Server error ("+e.status+")","Please contact support if the error persists"]})},n.then(o,t)},gt=function(n){return n.then(null,function(t){return function(o){var e;return e=typeof o=="string"?{errors:[o]}:Array.isArray(o)&&o.every(item(function(){return typeof item=="string"}))?{errors:o}:"readyState"in o&&"status"in o?Object.assign({errors:["Server error ("+o.status+")","Please contact support if the error persists"]},o.responseJSON):o,$.Deferred().reject(e)}}(this))};I.prepare_upload=function(n,t){return $.when(t).then(function(o){return De($.ajax({url:n+"/upload/prepare",type:"post",data:I.with_csrf(o),dataType:"json"}))})},Ae=function(n){var t,o,e,r,i;return t=new DataView(n,0,5),e=t.getUint8(0,!0),r=t.getUint8(1,!0),o=e.toString(16)+r.toString(16),i={8950:"image/png",4749:"image/gif","424d":"image/bmp",ffd8:"image/jpeg"},i[o]},fe="You selected an image type we don't recognize. It's possible it has the wrong file extension for the format it is saved as. Please use an image editing program to convert it to a PNG, JPEG, or GIF.",I.test_image_format=function(n){return $.Deferred(function(t){return function(o){var e;return window.FileReader?(e=new FileReader,e.readAsArrayBuffer(n),e.onerror=function(){return o.reject("Failed to read image from disk")},e.onload=function(){var r;return r=Ae(e.result),r==="image/bmp"?o.reject("You selected a BMP file that has a wrong extension. Please use an image editing program to convert it to a PNG, JPEG, or GIF."):r?o.resolve():o.reject(fe)}):o.resolve()}}(this))},I.image_dimensions=function(n){return $.Deferred(function(t){var o,e;return e=typeof URL!="undefined"&&URL!==null&&typeof URL.createObjectURL=="function"?URL.createObjectURL(n):void 0,e?(o=new Image,o.src=e,o.onload=function(){return t.resolve([o.width,o.height])},o.onerror=function(){return t.reject(fe)}):t.reject(fe)})},I.video_dimensions=function(n){return $.Deferred(function(t){var o,e;return e=document.createElement("video"),o=typeof URL!="undefined"&&URL!==null&&typeof URL.createObjectURL=="function"?URL.createObjectURL(n):void 0,o?(e.src=o,e.onloadedmetadata=function(){return t.resolve([e.videoWidth,e.videoHeight])},e.onerror=function(){return t.reject("Invalid video file")}):t.reject("Invalid video file")})},I.test_image_dimensions=function(n,t,o){return t==null&&(t=3840),o==null&&(o=2160),I.image_dimensions(n).then(function(e){return function(r){var i,a;return a=r[0],i=r[1],$.Deferred(function(c){return a>t||i>o?c.reject("Image is greater than the maximum dimensions of "+t+"x"+o+" (you selected a "+a+"x"+i+" image)"):c.resolve()})}}(this))};var ir=I.Upload||(I.Upload=function(){n.prototype.kind="upload";function n(t,o){this.file=t,this.opts=o!=null?o:{},this.save_upload=ee(this.save_upload,this),this.decrement=ee(this.decrement,this),this.increment=ee(this.increment,this)}return n.prototype.upload_params=function(){return{kind:this.kind,filename:this.file.name}},n.prototype.progress=function(t,o){},n.prototype.save_params=function(){return{}},n.prototype.save_url=function(){return this.upload_data.success_url},n.prototype.start_upload=function(){if(this.upload_data){console.warn("Attempt to start_upload when upload has already been started");return}return this.prepare_upload().then(function(t){return function(){return t.start_xhr_upload()}}(this)).then(function(t){return function(){return t.save_upload()}}(this))},n.prototype.increment=function(){var t,o;if((t=this.constructor).active_uploads||(t.active_uploads=0),this.constructor.active_uploads++,this.constructor.active_uploads===1)return typeof(o=this.opts).start_uploading=="function"?o.start_uploading():void 0},n.prototype.decrement=function(){var t;if(this.constructor.active_uploads--,this.constructor.active_uploads===0)return typeof(t=this.opts).stop_uploading=="function"?t.stop_uploading():void 0},n.prototype.prepare_upload=function(){return $.when(this.opts.upload_prefix||this.upload_prefix).then(function(t){return function(o){return I.prepare_upload(o,t.upload_params()).done(function(e){t.upload_data=e})}}(this))},n.prototype.start_xhr_upload=function(){var t,o,e,r,i,a;if(!this.upload_data)throw"missing upload data";this.increment(),t=$.Deferred().always(function(c){return function(){return c.decrement()}}(this)),o=new FormData,r=this.upload_data.post_params;for(e in r)i=r[e],o.append(e,i);return o.append("file",this.file),a=new XMLHttpRequest,a.upload.addEventListener("progress",function(c){return function(s){if(s.lengthComputable)return c.progress(s.loaded,s.total)}}(this)),a.upload.addEventListener("error",function(c){return function(s){return I.event("upload","xhr error",c.kind)}}(this)),a.upload.addEventListener("abort",function(c){return function(s){return I.event("upload","xhr abort",c.kind)}}(this)),a.addEventListener("readystatechange",function(c){return function(s){var h;if(a.readyState===4){if(Math.floor(a.status/100)===2)return I.event("upload","save",c.kind),t.resolve();if(h="Failed upload.",a.responseXML)try{h=a.responseXML.querySelector("Error Message").innerHTML}catch(g){s=g}else h=a.responseText;return I.event("upload_error","server error "+a.status+": "+h,c.kind),t.reject({errors:[h]})}}}(this)),a.open("POST",this.upload_data.action),a.send(o),t},n.prototype.save_upload=function(){if(!this.upload_data){console.warn("attempted to call save_upload without upload_data");return}return De($.ajax({url:this.save_url(),data:I.with_csrf(this.save_params()),dataType:"json",type:"post"}))},n}()),or=I.ImageUpload=function(n){Le(t,n);function t(){return t.__super__.constructor.apply(this,arguments)}return t.prototype.kind="image",t.prototype.upload_params=function(){return $.when(t.__super__.upload_params.call(this)).then(function(o){return function(e){return I.image_dimensions(o.file).then(function(r){var i,a;return a=r[0],i=r[1],Object.assign({width:a,height:i},e)},function(){return e})}}(this))},t}(I.Upload),ar=function(n){Le(t,n),t.prototype.stage_delay=500,t.prototype.mock_prepare_result={id:123},t.prototype.mock_save_result={success:!0};function t(){this.save_upload=ee(this.save_upload,this),this.start_xhr_upload=ee(this.start_xhr_upload,this),this.prepare_upload=ee(this.prepare_upload,this),t.__super__.constructor.apply(this,arguments),console.warn("MockUpload:new "+this.file.name)}return t.prototype.prepare_upload=function(){return console.warn("MockUpload: prepare_upload"),$.when(this.opts.upload_prefix()).then(function(o){return function(){return $.Deferred(function(e){return setTimeout(function(){return o.upload_data=o.mock_prepare_result,e.resolve(o.upload_data)},o.stage_delay)})}}(this))},t.prototype.start_xhr_upload=function(){var o,e,r,i,a,c;return console.warn("MockUpload: start_xhr_upload"),o=20,r=(i=this.file.size)!=null?i:1234,e=0,c=0,a=this.stage_delay/o*5,$.Deferred(function(s){return function(h){var g;return s.progress(0,r),g=function(){return setTimeout(function(){return c+=1,s.progress(Math.floor(c/o*r),r),c===o?h.resolve():g()},a)},g()}}(this))},t.prototype.save_upload=function(){return console.warn("MockUpload: save_upload"),$.Deferred(function(o){return function(e){return setTimeout(function(){return e.resolve(o.mock_save_result)},o.stage_delay)}}(this))},t}(I.Upload);I.pick_files=function(n){var t;return n==null&&(n={}),t=n.input?$(n.input):($("input.pick_files_input").remove(),$("<input type='file' class='pick_files_input' />").hide().insertAfter("body")),$.Deferred(function(o){return function(e){return n.multiple&&t.attr("multiple",!0),n.accept&&t.attr("accept",n.accept),t.on("change",function(r){var i,a,c,s,h,g,x;for(h=function(){var C,d,y,S;for(y=r.target.files,S=[],C=0,d=y.length;C<d;C++)c=y[C],S.push(c);return S}(),a=[],typeof n.on_pick_files=="function"&&n.on_pick_files(h),g=0,x=h.length;g<x;g++)s=h[g],n.max_size&&s.size>n.max_size&&a.push(["Image is greater than the max file size "+I.format_bytes(n.max_size)+" (you selected a "+I.format_bytes(s.size)+" file)",s]);if(a.length){e.reject(a,h);return}return n.test_file?(i=h.map(function(C){return $.Deferred(function(d){return n.test_file(C,d),setTimeout(function(){if(e.state()==="pending")return d.reject("Timed out checking file, are you sure it was an image file?")},1e3)}).catch(function(d){throw[d,C]})}),$.when.apply($,i).done(function(C){return e.resolve(h)}).fail(function(C){return e.reject([C])})):e.resolve(h)}),t.click()}}(this))},I.xhr_upload=function(n,t){return $.Deferred(function(o){return function(e){var r,i,a,c,s;r=new FormData,a=t.post_params;for(i in a)c=a[i],r.append(i,c);return r.append("file",n),s=new XMLHttpRequest,s.upload.addEventListener("progress",function(h){if(h.lengthComputable)return e.notify("progress",h.loaded,h.total)}),s.upload.addEventListener("error",function(h){return e.reject("xhr error")}),s.upload.addEventListener("abort",function(h){return e.reject("xhr aborted")}),s.addEventListener("readystatechange",function(h){var g;if(s.readyState===4){if(Math.floor(s.status/100)===2)return e.resolve();if(g="Failed upload.",s.responseXML)try{g=s.responseXML.querySelector("Error Message").innerHTML}catch(x){h=x}else g=s.responseText;return e.reject(g)}}),s.open("POST",t.action),s.send(r)}}(this))},I.upload_image=function(n){return n==null&&(n={}),n.accept||(n.accept="image/png,image/jpeg,image/gif"),n.file_params=function(t){return t.type==="video/mp4"?I.video_dimensions(t).then(function(o){return function(e){var r,i;return i=e[0],r=e[1],{width:i,height:r}}}(this)):I.image_dimensions(t).then(function(o){return function(e){var r,i;return i=e[0],r=e[1],{width:i,height:r}}}(this))},I.upload_file(n)};var Fe=function(n,t){var o,e,r;return t==null&&(t={}),e=t.on_start_upload,r=t.test_file,o=t.file_params?t.file_params(n):{},$.when(o).then(function(i){return function(a){return $.ajax({url:t.url,type:"post",xhrFields:{withCredentials:!0},data:I.with_csrf(Object.assign({filename:n.name,thumb_size:t.thumb_size,action:"prepare"},a))}).then(function(c){var s;return s=I.xhr_upload(n,c).then(function(){return $.ajax({url:c.success_url,type:"post",xhrFields:{withCredentials:!0},data:I.with_csrf()})}),typeof e=="function"&&e(n,c,s),s})}}(this))};I.upload_file=function(n){var t,o,e,r,i;if(n==null&&(n={}),!n.url)throw new Error("missing url for upload image");return t=n.accept,o=n.max_size,e=n.multiple,r=n.on_pick_files,i=n.test_file,I.pick_files({accept:t,max_size:o,multiple:e,test_file:i,on_pick_files:r}).then(function(a){return function(c){var s,h;if(!e&&c.length>1)throw"Got multiple files for single upload";return h=function(){var g,x,C,d;for(C=c.slice(0,6),d=[],g=0,x=C.length;g<x;g++)s=C[g],d.push(Fe(s,n));return d}(),e?$.when.apply($,h):h[0]}}(this))},I.make_upload_button=function(n,t){var o,e;return o=null,e=n.data("max_size"),n.on("click",function(r){return function(i){var a;return o&&o.remove(),o=$("<input type='file' multiple />").hide().insertAfter(n),(a=n.data("accept"))&&o.attr("accept",a),o.on("change",function(){var c,s,h,g,x;for(g=o[0].files,x=[],s=0,h=g.length;s<h;s++){if(c=g[s],e!=null&&c.size>e){I.flash(c.name+" is greater than max size of "+I.format_bytes(e),"error");continue}x.push(typeof t=="function"?t(c):void 0)}return x}),o.insertAfter(n),o.click()}}(this))};var me=[].slice,vt={}.hasOwnProperty,be=null;U(function(){var n,t,o,e,r,i,a,c,s,h,g,x,C,d,y,S,p,u,l,m,f,v,b,k,F,O,P,V,q,re,nr,Ye,Ze,Me,Qe,et,tt;return s=A,f=s.input,h=s.a,re=s.select,C=s.code,tt=s.ul,b=s.li,x=s.button,y=s.div,Qe=s.textarea,m=s.img,l=s.iframe,u=s.h2,p=s.form,v=s.label,V=s.pre,O=s.p,g=s.br,nr=s.span,S=s.em,a=L.package("Forms"),Ye=A.table,Ze=A.tbody,et=A.tr,Me=A.td,be=a("MarkdownInput",{pure:!0,propTypes:{name:N.string,value:N.string},getDefaultProps:function(){return{max_default_height:400,enable_image_pasting:!0}},getInitialState:function(){return{}},componentWillUnmount:function(){return this.unmounted=!0},componentDidMount:function(){var w,M,E;if(E=this.input_ref.current.scrollHeight,w=this.input_ref.current.clientHeight,E>w&&this.setState({default_height:Math.min(E,this.props.max_default_height)}),this.props.autofocus&&de.defer(function(B){return function(){var H;return(H=B.input_ref.current)!=null?H.focus():void 0}}(this)),this.props.enable_image_pasting)return(M=this.input_ref.current)!=null?M.addEventListener("paste",this.on_paste):void 0},on_paste:function(w){var M,E,B,H,X,K,ve,ne;if(!this.state.loading){for(M=w.clipboardData||window.clipboardData,K=M.items,ve=[],B=0,X=K.length;B<X;B++)if(H=K[B],H.kind==="file"&&(H.type==="image/png"||H.type==="image/jpeg")&&H.getAsFile!=null){E=H.getAsFile(),w.preventDefault(),ne=gt(Fe(E,{url:D.root_url("dashboard/upload-image"),file_params:function(G){return $.when(D.test_image_format(G),D.test_image_dimensions(G)).then(function(J){return function(){return D.image_dimensions(G).then(function(le){var ce,rt;return rt=le[0],ce=le[1],{width:rt,height:ce,source:"clipboard"}})}}(this))}})),this.setState({loading:!0,loading_reason:"Uploading image..."}),ne.always(function(G){return function(){if(!G.unmounted)return G.setState({loading:!1})}}(this)),ne.then(function(G){return function(J){var le,ce;if(!G.unmounted&&(le=(ce=J.upload)!=null?ce.thumb_url:void 0)&&G.input_ref.current!=null)return G.insert_line("!["+E.name+"]("+le+")")}}(this)),ne.fail(function(G){return function(J){if(!G.unmounted)return Y(ie(Object.assign({title:"Unable to upload image"},J)))}}(this));break}return ve}},get_value:function(){var w;return(w=this.input_ref.current)!=null?w.value:void 0},set_value:function(w){return this.input_ref.current.value=w},click_bold_text:function(w){return this.wrap_selection("**","**")},click_italic_text:function(w){return this.wrap_selection("*","*")},click_insert_link:function(w){var M,E,B;if(B=this.get_selected_text(),M=this.input_ref.current,B.match(/^https?:\/\/[^\s]+$/))return this.wrap_selection("[](",")"),M.selectionStart=M.selectionStart-2,M.selectionEnd=M.selectionStart;if(this.wrap_selection("[","](url)"),B.length>0)return E=M.selectionEnd,M.selectionStart=E+2,M.selectionEnd=E+5},click_insert_video:function(w){return Y(a.MarkdownVideoEmbedLightbox({on_submit:function(M){return function(E){return M.insert_line(E)}}(this)}))},click_insert_image:function(w){return D.Lightbox.open_remote(D.root_url("dashboard/upload-image"),Pe,function(M){return function(E){var B;if(D.Lightbox.close(),!M.unmounted&&(B=M.input_ref.current))return M.insert_line("![]("+E+")")}}(this))},click_show_help:function(w){return Y(a.MarkdownHelpLightbox({}))},get_selected_text:function(){var w;return(w=this.input_ref.current)?w.value.substring(w.selectionStart,w.selectionEnd):""},wrap_selection:function(w,M){var E,B,H,X,K,ve,ne,G,J;return E=this.input_ref.current,K=E.selectionStart,X=E.selectionEnd,J=E.value,ne=J.substring(0,E.selectionStart),ve=J.substring(E.selectionEnd,J.length),G=J.substring(E.selectionStart,E.selectionEnd),E.focus(),H=""+w+(G||"")+M,B=document.execCommand("insertText",!1,H),B||typeof E.setRangeText=="function"&&E.setRangeText(H),E.selectionStart=K+w.length,E.selectionEnd=X+w.length},insert_line:function(w){var M,E;if(M=this.input_ref.current,M.focus(),E=document.execCommand("insertText",!1,w),!E)return typeof M.setRangeText=="function"&&M.setRangeText(w),M.selectionStart=M.selectionStart+w.length,M.selectionEnd=M.selectionEnd},on_hotkey_keydown:function(w){var M;if(w.metaKey||w.ctrlKey){switch(w.key){case"b":this.click_bold_text(w);break;case"i":this.click_italic_text(w);break;case"k":this.click_insert_link(w);break;case"Enter":typeof(M=this.props).on_submit_hotkey=="function"&&M.on_submit_hotkey();break;default:return}return w.preventDefault()}},render:function(){var w;return this.enclose({className:j({loading:this.state.loading})},tt({className:"markdown_toolbar"},b({},x({tabIndex:-1,type:"button",onClick:this.click_show_help,title:"Markdown Help"},i({}))),b({},x({tabIndex:-1,type:"button",onClick:this.click_bold_text,title:"Bold"},n({}))),b({},x({tabIndex:-1,type:"button",onClick:this.click_italic_text,title:"Italic"},t({}))),b({},x({tabIndex:-1,type:"button",title:"Insert link",onClick:this.click_insert_link},e({}))),b({},x({tabIndex:-1,type:"button",title:"Insert image",onClick:this.click_insert_image},o({}))),b({},x({tabIndex:-1,type:"button",title:"Insert video",onClick:this.click_insert_video},r({})))),this.state.loading&&this.state.loading_reason?y({className:"loading_reason"},this.state.loading_reason):void 0,Qe({name:this.props.name,ref:this.input_ref||(this.input_ref=T.createRef()),defaultValue:this.get_default_value(),className:"markdown_textarea",onKeyDown:this.on_hotkey_keydown,placeholder:this.props.placeholder,required:this.props.required,disabled:this.state.loading||this.props.disabled,style:(w=this.state)!=null&&w.default_height?{height:this.state.default_height+"px"}:void 0,onChange:function(M){return function(E){var B,H;if(H=E.target.value,typeof(B=M.props).on_change=="function"&&B.on_change(H),M.props.remember_key)return M.set_memory||(M.set_memory=de.throttle(function(X){return D.store_memory(this.remember_key(),X)},500)),M.set_memory(H)}}(this)}))},get_default_value:function(){var w;if((w=this.props.value||this.props.defaultValue)||this.props.remember_key&&(w=typeof localStorage!="undefined"&&localStorage!==null?localStorage.getItem(this.remember_key()):void 0))return w},remember_key:function(){return"inputmemory:"+this.props.remember_key},clear_memory:function(){return D.clear_memory(this.remember_key())}}),P=A.path,k=A.line,q=A.rect,F=function(w){return T.createElement.bind(null,T.memo(w))},i=function(){return T.createElement("svg",{className:j("svgicon markdown_icon"),role:"img",version:"1.1",viewBox:"0 0 208 128",fill:"currentColor"},P({d:"M193 128H15a15 15 0 0 1-15-15V15A15 15 0 0 1 15 0h178a15 15 0 0 1 15 15v98a15 15 0 0 1-15 15zM50 98V59l20 25 20-25v39h20V30H90L70 55 50 30H30v68zm134-34h-20V30h-20v34h-20l30 35z"}))},i=F(i),d=function(){var w,M,E;return M=arguments[0],w=2<=arguments.length?me.call(arguments,1):[],T.createElement.apply(T,["svg",{className:j("svgicon",M.className),role:"img",version:"1.1",viewBox:"0 0 24 24",width:"24",height:"24",fill:(E=M!=null?M.fill:void 0)!=null?E:"none",stroke:"currentColor",strokeWidth:"3",strokeLinejoin:"round","aria-hidden":!0}].concat(me.call(w)))},n=function(){return d({className:"icon_format_bold"},P({d:"M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"}),P({d:"M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"}))},n=F(n),t=function(){return d({className:"icon_format_italic"},k({x1:"19",y1:"4",x2:"10",y2:"4"}),k({x1:"14",y1:"20",x2:"5",y2:"20"}),k({x1:"15",y1:"4",x2:"9",y2:"20"}))},t=F(t),e=function(){return d({className:"icon_insert_link"},P({d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"}),P({d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"}))},e=F(e),o=function(){return d({className:"icon_insert_image"},q({x:"3",y:"3",width:"18",height:"18",rx:"2",ry:"2"}),T.createElement("circle",{cx:"8.5",cy:"8.5",r:"1.5"}),T.createElement("polyline",{points:"21 15 16 10 5 21"}))},o=F(o),r=function(){return d({className:"icon_insert_video"},P({d:"M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"}),T.createElement("polygon",{points:"9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"}))},r=F(r),c={youtube:/https?:\/\/(?:[0-9A-Z-]+\.)?(?:youtu\.be\/|youtube(?:\-nocookie)?\.com\S*[^\w\-\s])([\w\-]{11})(?=[^\w\-]|$)(?![?=&+%\w.\-]*(?:['"][^<>]*>|<\/a>))[?=&+%\w.-]*/ig,vimeo:/https?:\/\/(www\.)?vimeo.com\/(\d+)($|\/)/,sketchfab_old:/https?:\/\/(?:www\.)?sketchfab\.com\/models\/([\w]+)($|\/)/,sketchfab:/https?:\/\/(?:www\.)?sketchfab\.com\/3d-models\/.*?-([^-]+)$/},a("MarkdownVideoEmbedLightbox",{pure:!0,getInitialState:function(){return{is_valid:!1}},componentDidMount:function(){return de.defer(function(w){return function(){var M;return(M=w.input_ref.current)!=null?M.focus():void 0}}(this))},generate_youtube_embed_code:function(w){return'<iframe width="560" height="315" src="https://www.youtube.com/embed/'+w+'" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>'},generate_vimeo_embed_code:function(w){return'<iframe src="https://player.vimeo.com/video/'+w+'" width="560" height="315" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>'},generate_sketchfab_embed_code:function(w){return'<iframe width="560" height="315" src="https://sketchfab.com/models/'+w+'/embed" frameborder="0" allow="autoplay; fullscreen; vr" mozallowfullscreen="true" webkitallowfullscreen="true"></iframe>'},parse_input_value:function(){var w,M,E,B,H,X,K;if(X=$.trim(((E=this.input_ref.current)!=null?E.value:void 0)||""),X.match(/<iframe/i))return X;for(H in c)if(vt.call(c,H)&&(M=c[H],w=X.match(M),!!w))switch(H){case"youtube":if(K=(B=w[0].match(/v=([^>"'&]+)/))||(B=w[0].match(/embed\/([^>"'&\/]+)/))?B[1]:void 0,K)return this.generate_youtube_embed_code(K);break;case"vimeo":if(K=w[2])return this.generate_vimeo_embed_code(K);break;case"sketchfab":case"sketchfab_old":if(K=w[1])return this.generate_sketchfab_embed_code(K);break;default:console.log(H,w)}},render:function(){return Q({className:j(this.enclosing_class_name(),"compact"),ref:this.lightbox_ref||(this.lightbox_ref=T.createRef())},u({},"Embed Video"),p({className:"form",onSubmit:this.on_submit||(this.on_submit=function(w){return function(M){var E,B;if(M.preventDefault(),!!(w.state.is_valid&&w.state.embed_code))return typeof(E=w.props).on_submit=="function"&&E.on_submit(w.state.embed_code),(B=w.lightbox_ref.current)!=null?B.close():void 0}}(this))},y({className:"label"},"Youtube/Vimeo/Sketchfab URL or ",C({},"<iframe>")," embed code"),y({className:"input_split"},f({type:"text",required:"required",placeholder:"Please paste a valid URL or embed code",ref:this.input_ref||(this.input_ref=T.createRef()),onChange:this.on_change||function(w){return function(M){var E;return E=w.parse_input_value(),w.setState({embed_code:E,is_valid:!!E})}}(this)})," ",x({disabled:!this.state.is_valid,className:j("button",{disabled:!this.state.is_valid})},"Insert"))))}}),a("MarkdownHelpLightbox",{pure:!0,render:function(){var w;return w=function(){var M,E;return M=arguments[0],E=2<=arguments.length?me.call(arguments,1):[],et({},Me({className:"format_type"},M),Me.apply(null,[{className:"format_example"}].concat(me.call(E))))},Q({className:j(this.enclosing_class_name(),"compact")},u({},"Quick Markdown Guide"),O({},"Markdown is a writing format that converts into HTML."),y({className:"table_wrapper"},Ye({className:"nice_table"},Ze({},w("Bold","**Bolded text here**"),w("Italic","*Emphasized text here*"),w("Bullet list","* First item",g({}),"* Second item"),w("Link","[Linked text](http://example.com)"),w("Blockquote","> Quoted text here",g({}),"> Can span multiple lines"),w("Code","```",g({}),'print("Hello world")',g({}),"```"),w("Video embeds",S({},"Paste embed code directly"))))),O({},h({href:"https://commonmark.org/help/",target:"_blank"},"Extended tutorial \u2197"),". You can also use a subset of HTML directly for advanced formatting."))}})});var we,Be=null,yt={plugins:["source","table","alignment","video","addimage"],toolbarFixed:!1,buttons:["format","bold","italic","deleted","lists","link"],minHeight:250,linkSize:80},bt=function(n){return D.libs.redactor.done(n)};we=!1;var wt=function(n){return we||(we=!0,DOMPurify.addHook("uponSanitizeElement",function(t,o,e){if(t.tagName==="IFRAME"&&t.innerHTML)return t.setAttribute("data-tmp-html",t.innerHTML),t.innerHTML=""}),DOMPurify.addHook("afterSanitizeElements",function(t,o,e){if(t.tagName==="IFRAME"&&t.getAttribute("data-tmp-html"))return t.innerHTML=t.getAttribute("data-tmp-html"),t.removeAttribute("data-tmp-html")})),DOMPurify.sanitize(n,{ADD_TAGS:["iframe"],ADD_ATTR:["width","height","frameborder","allowfullscreen","title","allow","scrolling","target"]})},Z=function(n,t){var o,e,r,i;if(t==null&&(t={}),!window.location.href.match(/\bredactor=0\b/)&&!D.in_test){if(!z.fn.redactor){console.warn("tried to create redactor text element without redactor on page",n[0]);return}t=z.extend({},yt,t),t.source===!1&&(delete t.source,t.plugins=function(){var a,c,s,h;for(s=t.plugins,h=[],a=0,c=s.length;a<c;a++)r=s[a],r!=="source"&&h.push(r);return h}()),n.closest(".lightbox_widget").exists()&&t.plugins&&(t.plugins=function(){var a,c,s,h;if(r!=="addimage"){for(s=t.plugins,h=[],a=0,c=s.length;a<c;a++)r=s[a],h.push(r);return h}}()),window.DOMPurify&&(o=n.val(),i=wt(o),i!==o&&n.val(i));try{return n.redactor(t)}catch(a){return e=a,D.event("error","redactor","invalid_content"),n.parent().replaceWith(n).end().val("").redactor(t)}}};z.Redactor?Be=z.Redactor:D.libs.redactor.done(function(){return Be=z.Redactor,z.Redactor.prototype.addimage=function(){return{langs:{en:{}},init:function(){var n;return n=this.button.addAfter("image","image","Add image"),this.button.setIcon(n,'<i class="re-icon-image"></i>'),this.button.addCallback(n,this.addimage.show)},show:function(){return oe.open_remote(D.root_url("dashboard/upload-image"),Pe,function(n){return function(t){var o;return D.Lightbox.close(),o=z("<img>").attr("src",t)[0].outerHTML,n.placeholder.hide(),n.buffer.set(),n.insert.html(o)}}(this))}}}});var Oe=null;U(function(){var n,t,o,e;return t=A,o=t.input,e=t.textarea,n=L.package("Forms"),Oe=n("RedactorInput",{pure:!0,componentDidMount:function(){var r;return r=z.extend({minHeight:80,source:!1},this.props.redactor_opts),r.callbacks||(r.callbacks={}),r.callbacks.change=function(i){return function(a){var c;if(typeof(c=i.props).on_change=="function"&&c.on_change(a),i.props.remember_key)return i.set_memory||(i.set_memory=de.throttle(function(s){return D.store_memory(this.remember_key(),s)},500)),i.set_memory(a)}}(this),Z(z(this.input_ref.current),r)},remember_key:function(){return"inputmemory:"+this.props.remember_key},clear_memory:function(){return D.clear_memory(this.remember_key())},get_default_value:function(){var r;if((r=this.props.defaultValue)||this.props.remember_key&&(r=typeof localStorage!="undefined"&&localStorage!==null?localStorage.getItem(this.remember_key()):void 0))return r},render:function(){return e({ref:this.input_ref||(this.input_ref=T.createRef()),name:this.props.name,value:this.props.value,defaultValue:this.get_default_value(),placeholder:this.props.placeholder,required:this.props.required,autoFocus:this.props.autofocus})}})});var xt=[].indexOf||function(n){for(var t=0,o=this.length;t<o;t++)if(t in this&&this[t]===n)return t;return-1},_e=null,He=null;U(function(){var n,t,o,e,r,i,a,c,s,h;return t=A,c=t.input,i=t.form,h=t.pre,e=t.button,r=t.div,s=t.label,a=t.h3,o=t.a,n=L.package("Community"),_e=n("PostForm",{getInitialState:function(){return{open:this.props.open||!1}},componentDidCatch:function(g,x){return I.event("error","react","Community.PostForm")},render:function(){return this.enclose({},this.state.open?this.render_form():this.render_pre_form())},render_pre_form:function(){return i({className:classNames("form post_form",this.props.className)},c({className:"click_input",type:"text",placeholder:this.t("game.comments.write_your_comment"),onFocus:function(g){return function(){return g.setState({open:!0,focus:!0})}}(this)}))},render_form:function(){var g,x,C;return i({action:this.props.submit_url,ref:this.form_ref||(this.form_ref=React.createRef()),className:classNames("form post_form",this.props.className),onSubmit:function(d){return function(y){var S;if(!((S=d.state)!=null&&S.loading))return y.preventDefault(),d.setState({loading:!0}),I.remote_submit($(y.target),[{name:"format",value:"props"}]).done(function(p){var u,l,m;if(p.redirect_to){(l=d.body_input_ref.current)!=null&&l.clear_memory(),window.location=p.redirect_to;return}if(p.errors){d.setState({needs_recaptcha:xt.call(p.errors,"recaptcha")>=0,errors:p.errors,loading:!1});return}if((m=d.body_input_ref.current)!=null&&m.clear_memory(),p.redirect_to){window.location=p.redirect_to;return}if(p.flash&&I.flash(p.flash),d.setState({loading:!1,needs_recaptcha:!1,errors:null}),p.post)return typeof(u=d.props).on_create_post=="function"?u.on_create_post(p.post):void 0})}}(this)},(g=this.state)!=null&&g.errors?L.Forms.FormErrors({errors:this.state.errors,animated:!0,scroll_into_view:!0}):void 0,L.CSRF({}),this.props.body_format?c({type:"hidden",name:"post[body_format]",value:this.props.body_format}):void 0,function(){switch(this.props.body_format||"html"){case"html":return Oe({placeholder:"Required",ref:this.body_input_ref||(this.body_input_ref=React.createRef()),remember_key:this.props.remember_key,name:"post[body]",required:!0,defaultValue:this.props.defaultValue,redactor_opts:$.extend({minHeight:50,focus:this.props.autofocus||this.state.focus},this.props.redactor_opts)});case"markdown":return be({ref:this.body_input_ref||(this.body_input_ref=React.createRef()),remember_key:this.props.remember_key,placeholder:"Required",defaultValue:this.props.defaultValue,name:"post[body]",autofocus:this.props.autofocus||this.state.focus,required:!0,on_submit_hotkey:this.on_submit_hotkey_callback||(this.on_submit_hotkey_callback=function(d){return function(){var y,S;return(y=d.submit_button_ref)!=null&&(S=y.current)!=null?S.click():void 0}}(this))});default:return L.Forms.FormErrors({animated:!0,scroll_into_view:!0,errors:["Don't know how to edit post with format "+this.state.body_format]})}}.call(this),(x=this.state)!=null&&x.needs_recaptcha?L.Forms.RecaptchaInput({sitekey:this.props.recaptcha_sitekey}):void 0,r({className:"buttons"},e({className:"button",disabled:(C=this.state)!=null?C.loading:void 0,ref:this.submit_button_ref||(this.submit_button_ref=React.createRef())},this.props.post_label||this.tt("community.post_form.post")),this.props.more_buttons))}}),He=n("PostEditForm",{propTypes:{edit_url:N.string.isRequired},componentDidCatch:function(g,x){return I.event("error","react","Community.PostEditForm"),this.setState({critical_error:!0})},getInitialState:function(){return{loading:!0}},componentDidMount:function(){return $.ajax({type:"GET",dataType:"json",url:this.props.edit_url,xhrFields:{withCredentials:!0}}).done(function(g){return function(x){return g.setState({loading:!1,errors:x.errors,body_format:x.body_format,body:x.body})}}(this)).fail(function(g){return function(x){return g.setState({critical_error:!0,loading:!1})}}(this))},render:function(){var g;return(g=this.state)!=null&&g.critical_error?a({},"There was an error editing this post, please ",o({href:"https://itch.io/support"},"contact support")," with a link to this page."):this.state.body?_e($.extend({},this.props.reply_form_params,{submit_url:this.props.edit_url,autofocus:!0,open:!0,className:"inline_edit",defaultValue:this.state.body,body_format:this.state.body_format,post_label:this.tt("misc.forms.save"),more_buttons:this.props.more_buttons,on_create_post:this.props.on_create_post})):r({},"")}})});var Ue=[].slice,W=null,ae=null,xe=null,St=null,kt=null,Tt=null,Ct=null,Mt=null,Et=null,Nt=null,It=null;U(function(){var n,t,o;return n=A,t=n.a,o=n.img,W=function(){var e,r,i;return i=arguments[0],r=2<=arguments.length?Ue.call(arguments,1):[],i==null&&(i={}),T.createElement.bind(null,T.memo(e=function(a){var c,s,h,g,x;return T.createElement.apply(T,["svg",{className:j("svgicon",a.className,i.className),role:"img",version:"1.1",viewBox:"0 0 24 24",width:(c=a.width)!=null?c:"24",height:(s=a.height)!=null?s:"24",fill:(h=(g=a.fill)!=null?g:i.fill)!=null?h:"none",stroke:"currentColor",strokeWidth:(x=a.strokeWidth)!=null?x:"2",strokeLinejoin:"round",strokeLinecap:"round","aria-hidden":!0}].concat(Ue.call(r)))}))},ae=W({className:"icon_tri_up",fill:"currentColor"},T.createElement("polygon",{points:"2 18 12 6 22 18"})),xe=W({className:"icon_tri_down",fill:"currentColor"},T.createElement("polygon",{points:"2 6 12 18 22 6"})),St=W({className:"icon_filter"},T.createElement("polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"})),kt=W({className:"icon_edit"},T.createElement("path",{d:"M20 14.66V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h5.34"}),T.createElement("polygon",{points:"18 2 22 6 12 16 8 16 8 12 18 2"})),Tt=W({className:"icon_external_link"},T.createElement("path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"}),T.createElement("polyline",{points:"15 3 21 3 21 9"}),T.createElement("line",{x1:"10",y1:"14",x2:"21",y2:"3"})),Ct=W({className:"icon_help"},T.createElement("circle",{cx:"12",cy:"12",r:"10"}),T.createElement("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),T.createElement("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})),Mt=W({className:"icon_search"},T.createElement("circle",{cx:"11",cy:"11",r:"8"}),T.createElement("line",{x1:"21",y1:"21",x2:"16.65",y2:"16.65"})),Et=W({className:"icon_tag"},T.createElement("path",{d:"M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"}),T.createElement("line",{x1:"7",y1:"7",x2:"7",y2:"7"})),It=W({className:"icon_browse_category"},T.createElement("path",{d:"M4 4h16v2H4zM4 10h10v2H4zM4 16h14v2H4z"})),Nt=W({className:"icon_verified"},T.createElement("title",{},"Verified Account"),T.createElement("path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14"}),T.createElement("polyline",{points:"22 4 12 14.01 9 11.01"}))});var te,Se=te=window.dayjs;window._dayjs_setup||(te.extend(window.dayjs_plugin_duration),te.extend(window.dayjs_plugin_calendar),te.extend(window.dayjs_plugin_advancedFormat),te.extend(window.dayjs_plugin_relativeTime),te.extend(window.dayjs_plugin_utc),window._dayjs_setup=!0);var je,Rt=[].slice;je=function(n){var t;return(t=typeof n=="string"&&n.match(/(^\d{4}\-\d{1,2}\-\d{1,2}) (\d{1,2}:\d{1,2}:\d{1,2})$/))?t[1]+"T"+t[2]+"Z":n};var ke=function(){var n,t,o,e;switch(e=arguments[0],t=arguments[1],n=3<=arguments.length?Rt.call(arguments,2):[],t==null&&(t="fromNow"),e=je(e),t){case"fromNow":return Se(e).fromNow();case"calendar":return Se(e).calendar(null,{sameElse:"MMMM Do YYYY"});case"format":return(o=Se(e)).format.apply(o,n);default:throw new Error("unknown method for format_timestamp: "+t)}},Pt={}.hasOwnProperty,Ge=null,Te=null,Ve=null,$e=null,se=null;U(function(){var n,t,o,e,r,i,a,c,s,h,g,x,C,d,y,S;return t=A,c=t.form,o=t.a,e=t.button,x=t.img,r=t.code,s=t.fragment,i=t.div,a=t.em,y=t.span,S=t.strong,C=t.label,g=t.h3,h=t.h2,d=t.p,s=React.createElement.bind(null,React.Fragment),s.type=React.fragment,n=L.package("Community"),Ge=n("PostLiker",{pure:!0,getDefaultProps:function(){return{animation_duration:500}},componentDidCatch:function(p,u){return I.event("error","react","Community.PostLiker")},componentWillUnmount:function(){if(this.animation_timer)return window.clearTimeout(this.animation_timer),this.animation_timer=null},componentDidUpdate:function(p,u){var l;if((l=this.state)!=null&&l.animation&&(u!=null?u.animation:void 0)!==this.state.animation)return this.animation_timer=window.setTimeout(function(m){return function(){return m.setState({animation:null})}}(this),this.props.animation_duration)},like:function(){var p,u,l,m,f;return l={direction:"up"},(m=this.props.vote)!=null&&m.positive&&(l.action="remove"),p=l.action==="remove"?"animate_drop_down":"animate_bounce",(f=this.state)!=null&&f.animation&&(u=p,_.defer(function(v){return function(){return v.setState({animation:u})}}(this)),p=null),this.setState({loading:!0,pending_vote:l.action!=="remove",animation:p}),$.ajax({type:"POST",dataType:"json",url:this.props.urls.vote_url({id:this.props.post_id}),data:I.with_csrf(l),xhrFields:{withCredentials:!0}}).done(function(v){return function(b){var k;return v.setState({loading:!1,pending_vote:void 0}),typeof(k=v.props).on_vote=="function"?k.on_vote(b):void 0}}(this)).fail(function(v){return function(b){var k;if(v.setState({loading:!1}),k=function(){try{return JSON.parse(b.responseText)}catch(F){}}(),k!=null&&k.errors&&v.setState({pending_vote:null}),k!=null&&k.login_url)return window.location=k.login_url}}(this))},render:function(){var p,u,l,m,f;return this.props.current_user?this.enclose({component:"button",type:"button",className:classNames("post_action vote_btn",(p=this.state)!=null?p.animation:void 0,this.props.classNames,{loading:(u=this.state)!=null?u.loading:void 0,voted:(l=(m=this.state)!=null?m.pending_vote:void 0)!=null?l:(f=this.props.vote)!=null?f.positive:void 0}),onClick:this.on_click||(this.on_click=function(v){return function(b){return b.preventDefault(),v.like()}}(this))},ae({})," ",this.tt("community.post_list.like")):(this.props.login_url||console.warn("Rendering a logged out PostLiker but we don't have the login_url"),this.enclose({component:"a",className:classNames("post_action vote_btn",this.props.classNames),href:this.props.login_url,target:"_blank"},ae({})," ",this.tt("community.post_list.like")))}}),Ve=n("PostVoter",{pure:!0,propTypes:{urls:N.object,post_id:N.number,vote:N.object},componentDidCatch:function(p,u){return I.event("error","react","Community.PostVoter")},vote:function(p){var u,l,m;if(!((l=this.state)!=null&&l.loading))return u={direction:p},p==="up"&&((m=this.props.vote)!=null&&m.positive)&&(u.action="remove"),p==="down"&&this.props.vote&&!this.props.vote.positive&&(u.action="remove"),this.setState({loading:!0}),$.ajax({type:"POST",url:this.props.urls.vote_url({id:this.props.post_id}),data:I.with_csrf(u),dataType:"json",xhrFields:{withCredentials:!0}}).done(function(f){return function(v){var b;return f.setState({loading:!1}),typeof(b=f.props).on_vote=="function"?b.on_vote(v):void 0}}(this)).fail(function(f){return function(v){var b;if(f.setState({loading:!1}),b=function(){try{return JSON.parse(v.responseText)}catch(k){}}(),b!=null&&b.login_url)return window.location=b.login_url}}(this))},render:function(){var p;return!this.props.current_user&&!this.props.login_url&&console.warn("Rendering a logged out PostVoter but we don't have the login_url"),this.enclose({className:classNames("post_votes",{loading:(p=this.state)!=null?p.loading:void 0})},this.props.current_user?e({type:"button",disabled:this.props.disabled,className:classNames("vote_up_btn vote_btn",{voted:this.props.vote&&this.props.vote.positive}),onClick:this.on_vote_up||(this.on_vote_up=function(u){return function(){return u.vote("up")}}(this))},ae({})):o({className:"vote_up_btn vote_btn",target:"_blank",rel:"nofollow",href:this.props.login_url},ae({})),this.props.current_user?e({type:"button",disabled:this.props.disabled,className:classNames("vote_down_btn vote_btn",{voted:this.props.vote&&!this.props.vote.positive}),onClick:this.on_vote_down||(this.on_vote_down=function(u){return function(){return u.vote("down")}}(this))},xe({})):o({className:"vote_down_btn vote_btn",target:"_blank",rel:"nofollow",href:this.props.login_url},xe({})))}}),$e=n("PostBody",{pure:!0,getDefaultProps:function(){return{max_height:300}},componentDidMount:function(){return _.defer(function(p){return function(){var u,l;return l=$(p.wrapper_ref.current).find("img").on("load",function(){return p.refresh_sizes()}),u=l.length?void 0:50,p.refresh_sizes(u)}}(this))},reveal:function(){return this.setState({open:!0,has_more:!1})},on_click:function(p){var u,l,m;if((l=this.state)!=null&&l.has_more&&this.reveal(),u=$(p.target).closest(".embed_preload"),u.length&&(r=u.data("embed_code"),u.replaceWith(r),!((m=this.state)!=null&&m.open)))return this.setState({open:!0})},refresh_sizes:function(p){var u;if(this.wrapper_ref.current&&(u=this.wrapper_ref.current.scrollHeight,u>this.props.max_height&&this.setState({has_more:!0}),p&&u<this.props.max_height+p))return this.setState({open:!0,has_more:!1})},render:function(){var p,u,l;return l=(p=this.state)!=null&&p.open?void 0:{overflowY:"hidden",maxHeight:this.props.max_height+"px"},s({},i({className:"post_body user_formatted",ref:this.wrapper_ref||(this.wrapper_ref=React.createRef()),style:l,onClick:this.on_click,dangerouslySetInnerHTML:{__html:this.props.body_html}}),(u=this.state)!=null&&u.has_more?e({type:"button",onClick:this.reveal,className:"reveal_full_post_btn"},"View rest \u2193"):void 0)}}),se=n("Post",{pure:!0,propTypes:{post:N.object,urls:N.object,current_user:N.object,reply_form_params:N.object,len_posts:N.number,idx:N.number,readonly:N.bool,display_post_footer:N.bool},edit_post:function(p){return this.props.edit_post(this.props.post,p)},get_visible_vote_types:function(){return this.props.readonly?null:this.props.post.vote_types},is_redacted:function(){var p,u,l,m,f;return p=this.props.post,f=this.props.post.user,p.deleted||(u=p.user)!=null&&u.deleted?!0:(l=p.user)!=null&&l.suspended||p.blocked?!((m=this.state)!=null&&m.show_blocked):!1},render:function(){var p,u;return p=this.props.post,s({},this.render_current_post(),(u=p.replies)!=null&&u.length?this.render_children():p.view_replies_url?i({className:"view_more_replies"},o({href:p.view_replies_url,className:"button outline forward_link"},this.tt("community.post_list.view_more_in_thread"))):void 0)},render_children:function(){var p;return p=this.props.post,i({className:classNames("community_post_replies",{top_level_replies:p.depth===1})},p.replies.map(function(u){return function(l,m){return se({key:l.id,readonly:u.props.readonly,idx:m,post:l,current_user:u.props.current_user,edit_post:u.edit_child_post||(u.edit_child_post=function(f,v){var b;return u.edit_post({replies:function(){var k,F,O,P;for(O=this.props.post.replies,P=[],k=0,F=O.length;k<F;k++)if(b=O[k],b===f){if(v==="remove")continue;P.push($.extend({},f,v))}else P.push(b);return P}.call(u)})}),urls:u.props.urls,reply_form_params:u.props.reply_form_params})}}(this)))},update_vote:function(p){var u,l,m;return l=p.score_adjustment||0,m=Math.max(l,0),u=Math.min(l,0),this.edit_post({up_votes:p.up_score+m,down_votes:p.down_score-u,vote:p.score_adjustment?{positive:p.score_adjustment>0}:null})},delete_post:function(p){var u,l,m,f,v;if(p==null&&(p=null),f=this.props.post,l=p==="hard"?"Purging post will delete it permanently, along with any replies. Continue?":"Are you sure you want to delete this post?",!!confirm(l)&&!((v=this.state)!=null&&v.deleting))return this.setState({deleting:!0}),u=this.props.urls.delete_url({id:f.id}),m={},p==="hard"&&(m.hard="1"),$.ajax({type:"POST",url:u,data:I.with_csrf(m),dataType:"json",xhrFields:{withCredentials:!0}}).done(function(b){return function(k){return b.edit_post({deleted:!0,hard_deleted:k.type==="hard"})}}(this)).always(function(b){return function(){return b.setState({deleting:!1})}}(this))},render_current_post_moderation_event:function(){var p,u,l;return u=this.props.post,l=this.props.post.user,p=u.moderation_event,u.deleted?i({className:"post_content"},i({className:"post_body"},a({className:"deleted_message"},this.tt("community.post_list.deleted_post")))):s({},y({className:"post_author"},l.url&&l.name?o({href:l.url},l.name):y({className:"name_placeholder"},"Unknown account")),y({className:"moderation_action"},S({},p.action),p.target_name?s({}," ",o({href:p.target_url},p.target_name)):void 0," ",y({className:"post_date",title:u.created_at+" UTC"},o({href:u.url},ke(u.created_at))),u.can_delete&&!u.deleted&&!this.props.readonly?e({className:"textlike delete_post_btn post_action",type:"button",onClick:function(m){return function(f){return f.preventDefault(),m.delete_post()}}(this)},this.tt("community.post_list.delete")):void 0))},render_current_post_contents:function(){var p,u,l,m,f,v,b,k,F,O,P;return l=this.props.post,P=this.props.post.user,p=Re({className:"post_avatar",width:25,height:25,src:P.avatar_url,src_set:P.avatar_url2x?P.avatar_url+" 1x, "+P.avatar_url2x+" 2x":void 0}),(l.just_added?pe:i)({className:"post_grid"},this.get_visible_vote_types()==="ud"?Ve({post_id:l.id,vote:l.vote,disabled:this.is_redacted(),urls:this.props.urls,on_vote:this.update_vote,current_user:this.props.current_user,login_url:typeof(u=this.props.urls).login_url=="function"?u.login_url():void 0}):void 0,P.url?o({href:P.url,className:"avatar_container"},p):i({className:"avatar_container"},p),i({className:"post_header"},y({className:"post_author"},l.deleted?y({className:"name_placeholder"},"Deleted post"):P.deleted?y({className:"name_placeholder"},this.tt("community.post_list.deleted_account")):l.blocked&&!((m=this.state)!=null&&m.show_blocked)?y({className:"name_placeholder"},"Blocked account"):P.suspended?y({className:"name_placeholder"},this.tt("community.post_list.suspended_account")):P.url&&P.name?o({href:P.url},P.name):void 0),y({className:"post_date",title:l.created_at+" UTC"},o({href:l.url},ke(l.created_at))),!this.is_redacted()&&(l.edits_count||0)>0?s({}," ",y({className:"edit_message",title:l.edited_at+" ("+l.edits_count+")"},this.tt("community.post_list.edited"))):void 0,!this.is_redacted()&&l.vote_types&&(l.up_votes||l.down_votes)?y({className:"vote_counts"},l.up_votes?y({className:"upvotes"},"(+"+l.up_votes+")"):void 0,l.down_votes?y({className:"downvotes"},"(-"+l.down_votes+")"):void 0):void 0),i({className:"post_content"},l.deleted||(f=l.user)!=null&&f.deleted?i({className:"post_body"},a({className:"deleted_message"},this.tt("community.post_list.deleted_post")),(l.can_moderate||l.admin_url)&&!l.hard_deleted?s({}," (",e({className:"textlike",onClick:this.purge_post||(this.purge_post=function(V){return function(q){return q.preventDefault(),V.delete_post("hard").done(function(){return V.edit_post("remove")})}}(this))},"Purge"),")"):void 0):(v=l.user)!=null&&v.suspended&&!((b=this.state)!=null&&b.show_blocked)?i({className:"post_body"},a({},this.tt("community.post_list.post_from_suspended_account"))," (",e({className:"textlike",onClick:this.show_blocked_post||(this.show_blocked_post=function(V){return function(){return V.setState({show_blocked:!0})}}(this))},"Show post"),")"):l.blocked&&!((k=this.state)!=null&&k.show_blocked)?i({className:"post_body"},a({},this.tt("community.post_list.post_from_blocked_account"))," (",e({className:"textlike",onClick:this.show_blocked_post||(this.show_blocked_post=function(V){return function(){return V.setState({show_blocked:!0})}}(this))},"Show post"),", ",o({href:this.props.urls.blocks_url(),target:"_blank"},this.tt("community.post_list.edit_blocks")),")"):(F=this.state)!=null&&F.editing?this.render_edit_form():$e({key:l.body_html,body_html:l.body_html}),this.render_post_footer(),(O=this.state)!=null&&O.replying_to?this.render_reply_form():void 0))},render_post_footer:function(){var p,u,l,m;if(this.props.display_post_footer!==!1)return l=this.props.post,m=this.props.post.user,i({className:"post_footer"},!this.is_redacted()&&this.get_visible_vote_types()==="u"?Ge({post_id:l.id,vote:l.vote,urls:this.props.urls,on_vote:this.update_vote,current_user:this.props.current_user,login_url:typeof(p=this.props.urls).login_url=="function"?p.login_url():void 0}):void 0,!this.is_redacted()&&!this.props.readonly?l.can_reply?o({href:this.props.urls.reply_url({id:l.id}),className:"post_action reply_btn",onClick:this.props.reply_form_params?this.on_click_reply||(this.on_click_reply=function(f){return function(v){return v.preventDefault(),f.setState(function(b){return b==null&&(b={}),{replying_to:!b.replying_to}})}}(this)):void 0},this.tt("community.post_list.reply")):!this.props.current_user&&this.props.urls.login_url?o({href:typeof(u=this.props.urls).login_url=="function"?u.login_url():void 0,className:"post_action reply_btn",rel:"nofollow",target:"_blank"},this.tt("community.post_list.reply")):void 0:void 0,l.can_edit&&!this.props.readonly?o({href:this.props.urls.edit_url({id:l.id}),className:"post_action edit_btn",onClick:this.props.reply_form_params?this.on_click_edit||(this.on_click_edit=function(f){return function(v){return v.preventDefault(),f.setState(function(b){return b==null&&(b={}),{editing:!b.editing,replying_to:!1}})}}(this)):void 0},this.tt("community.post_list.edit")):void 0,l.can_delete&&!l.deleted&&!this.props.readonly?o({href:"#",className:"post_action delete_post_btn",onClick:function(f){return function(v){return v.preventDefault(),f.delete_post()}}(this)},l.is_topic?this.tt("community.post_list.delete_topic"):this.tt("community.post_list.delete")):void 0,!this.is_redacted()&&l.can_report&&!this.props.readonly?o({href:"#",className:"post_action report_btn",onClick:function(f){return function(v){var b;return v.preventDefault(),b=f.props.urls.report_url({id:l.id}),I.Lightbox.open_remote(b,I.CommunityReportPostLightbox)}}(this)},this.tt("community.post_list.report")):void 0,m.id&&l.ban_target&&!this.props.readonly?o({href:"#",className:"post_action ban_user_btn",onClick:function(f){return function(v){var b;return v.preventDefault(),b=f.props.urls.ban_url(l.ban_target),b+="?banned_user_id="+m.id,I.Lightbox.open_remote(b,I.CommunityBanLightbox)}}(this)},S({},this.tt("community.post_list.ban"))):void 0,l.admin_url?o({href:l.admin_url},"Admin"):void 0)},render_edit_form:function(){var p;return p=this.props.post,this.edit_form_cancel||(this.edit_form_cancel=s({}," ",e({type:"button",className:"textlike",onClick:function(u){return function(){return u.setState({editing:!1})}}(this)},"Cancel"))),He({edit_url:this.props.urls.edit_url({id:p.id}),more_buttons:this.edit_form_cancel,reply_form_params:this.props.reply_form_params,on_create_post:function(u){return function(l){return u.edit_post(l),u.setState({editing:!1})}}(this)})},render_reply_form:function(){var p;return pe({},_e($.extend({submit_url:this.props.urls.reply_url({id:this.props.post.id}),autofocus:!0,open:!0,className:"inline_reply",key:"post-"+(((p=this.state)!=null?p.post_counter:void 0)||0),post_label:this.tt("community.post_form.post_reply"),remember_key:"reply:"+this.props.post.id,on_create_post:this.on_add_reply||(this.on_add_reply=function(u){return function(l){return l.just_added=!0,u.setState(function(m){return{replying_to:!1,post_counter:(m.post_counter||0)+1}}),u.edit_post({replies:[l].concat(u.props.post.replies||[])})}}(this))},this.props.reply_form_params)))},render_current_post:function(){var p,u,l,m;return p=this.props.post,this.enclose({className:classNames("community_post",{disabled:p.deleted||((u=p.user)!=null?u.suspended:void 0)||((l=p.user)!=null?l.deleted:void 0)||p.blocked,is_deleted:p.deleted,is_reply:p.depth>1,is_blocked:p.blocked,is_suspended:(m=p.user)!=null?m.suspended:void 0,has_replies:p.replies&&p.replies.length,has_vote_column:this.get_visible_vote_types()==="ud",moderation_event:p.moderation_event}),id:"post-"+p.id},this.props.len_posts&&this.props.idx===0?i({className:"post_anchor",id:"first-post"}):void 0,this.props.len_posts&&this.props.idx===this.props.len_posts-1?i({className:"post_anchor",id:"last-post"}):void 0,p.moderation_event?this.render_current_post_moderation_event():this.render_current_post_contents())}}),Te=n("PostList",{pure:!0,propTypes:{children:N.array,urls:N.object},componentDidCatch:function(p,u){return I.event("error","react","Community.PostList"),this.setState({critical_error:!0})},render:function(){var p;return(p=this.state)!=null&&p.critical_error?g({},"There was an error rendering the comments, please ",o({href:"https://itch.io/support"},"contact support")," with a link to this page."):this.props.posts?(this.url_templates||(this.url_templates=function(u){return function(){var l,m,f,v;m={},f=u.props.urls;for(l in f)Pt.call(f,l)&&(v=f[l],m[l]=_.template(v));return m}}(this)()),this.enclose({},this.props.posts.map(function(u){return function(l,m){var f;return f=m===0?u.first_post_ref||(u.first_post_ref=React.createRef()):void 0,se({ref:f,key:l.id,readonly:u.props.readonly,idx:m,edit_post:u.props.edit_post,len_posts:u.props.posts.length,post:l,current_user:u.props.current_user,urls:u.url_templates,reply_form_params:u.props.reply_form_params})}}(this)))):null}}),L.package("Game")("Comments",{pure:!0,getDefaultProps:function(){return{autoload_count:1}},getInitialState:function(){return{posts:this.props.posts,pagination:this.props.pagination,autoload_count:this.props.autoload_count}},create_scroll_restore:function(p){var u,l,m;return p=$(p).find(".post_grid"),p.length?(l=p.offsetParent(),m=window.document.documentElement.scrollTop,u=p.position().top-m,function(){return window.document.documentElement.scrollTop=p.position().top-u}):function(){}},load_page:function(p){var u;if(p==null&&(p="next_page"),!!((u=this.state.pagination)!=null&&u[p]))return this.setState({loading:!0}),$.getJSON(this.props.topic.url,this.state.pagination[p]).done(function(l){return function(m){var f,v,b,k,F,O,P,V,q;return m.posts?(b=Object.assign({},l.props.pagination),b[p]=(O=m.pagination)!=null?O[p]:void 0,F=function(){switch(p){case"prev_page":return m.posts.concat(this.state.posts);case"next_page":return this.state.posts.concat(m.posts)}}.call(l),q=p==="prev_page"?(k=(P=l.post_list_ref)!=null?P.current:void 0,(v=k!=null&&(V=k.first_post_ref)!=null?V.current:void 0)?(f=ReactDOM.findDOMNode(v),l.create_scroll_restore(f)):void 0):void 0,l.setState({loading:!1,posts:F,pagination:b},q)):l.setState({loading:!1,pagination:null})}}(this))},render:function(){var p,u,l,m,f;return this.enclose({},h({},this.tt("game.comments.comments_header")),_e({key:"post-"+(((u=this.state)!=null?u.post_counter:void 0)||0),submit_url:this.props.post_form.submit_url,post_label:this.tt("game.comments.post_comment"),recaptcha_sitekey:this.props.recaptcha_sitekey,remember_key:this.props.remember_key||"topic:"+this.props.topic.id,redactor_opts:this.props.post_form.redactor_opts,body_format:this.props.body_format,on_create_post:this.on_create_post||(this.on_create_post=function(v){return function(b){return v.setState(function(k){return b.just_added=!0,{post_counter:(k.post_counter||0)+1,posts:[b].concat(k.posts||[])}})}}(this))}),this.state.pagination&&this.state.pagination.prev_page?d({className:"pagination_buttons"},o({href:this.props.topic.url+"?"+$.param(this.state.pagination.prev_page),className:"load_posts_before",disabled:(l=this.state)!=null?l.loading:void 0,onClick:this.load_prev_page||(this.load_prev_page=function(v){return function(b){return b.preventDefault(),v.load_page("prev_page")}}(this))},"Show previous posts \u2191")):void 0,Te({ref:this.post_list_ref||(this.post_list_ref=React.createRef()),posts:this.state.posts,urls:this.props.urls,current_user:this.props.current_user||I.current_user,reply_form_params:this.reply_form_params||(this.reply_form_params={recaptcha_sitekey:this.props.recaptcha_sitekey,redactor_opts:this.props.post_form.redactor_opts,body_format:this.props.body_format}),edit_post:this.edit_post||(this.edit_post=function(v){return function(b,k){return v.setState(function(F){var O;return{posts:function(){var P,V,q,re;for(q=F.posts,re=[],P=0,V=q.length;P<V;P++)if(O=q[P],O===b){if(k==="remove")continue;re.push($.extend({},b,k))}else re.push(O);return re}()}})}}(this))}),this.state.pagination&&this.state.pagination.next_page?(p=i({className:"pagination_buttons"},o({href:this.props.topic.url+"?"+$.param(this.state.pagination.next_page),className:classNames("load_posts_after",{loading:(m=this.state)!=null?m.loading:void 0}),onClick:this.load_next_page||(this.load_next_page=function(v){return function(b){var k;if(b.preventDefault(),!((k=v.state)!=null&&k.loading))return v.load_page("next_page")}}(this))},(f=this.state)!=null&&f.loading?"Loading...":"Load more")),this.state.autoload_count>0?ye({key:JSON.stringify(this.state.pagination),on_seen:function(v){return function(){return v.setState(function(b){return{autoload_count:b.autoload_count-1}}),v.load_page("next_page")}}(this)},p):p):void 0)}})});var Ke=null;U(function(){var n,t,o,e;return t=A,e=t.h2,o=t.div,n=L.package("Community"),Ke=n("PostLightbox",{render:function(){return Q({className:j(this.enclosing_class_name(),"compact")},e({},"Preview post"),o({className:"community_post_list_widget"},se({readonly:!0,urls:this.props.urls,post:this.props.post,current_user:I.current_user})))}})});var ge=null;U(function(){var n,t;return n=A,t=n.img,ge=function(o){return T.createElement("svg",{className:"svgicon icon_down_tick2",strokeLinecap:"round",stroke:"currentColor",role:"img",version:"1.1",viewBox:"0 0 24 24",strokeWidth:"2",width:"22",height:"22",strokeLinejoin:"round","aria-hidden":!0,fill:"none"},T.createElement("polyline",{points:"6 9 12 15 18 9"}))},ge=T.createElement.bind(null,T.memo(ge))});var At=[].slice,Xe=null,ue=null;U(function(){var n,t,o,e,r,i,a,c,s,h,g,x,C,d,y,S,p;return t=A,i=t.input,e=t.div,a=t.label,d=t.span,S=t.textarea,g=t.p,h=t.option,s=t.optgroup,C=t.select,p=t.ul,c=t.li,x=t.section,r=t.form,y=t.strong,o=function(u){return function(){return L.component.apply(L,arguments)}}(this),o("CSRF",{pure:!0,render:function(){return i({type:"hidden",name:"csrf_token",value:$("meta[name=csrf_token]").attr("value")})}}),n=L.package("Forms"),n("InputRow",{pure:!0,propTypes:{title:N.string,sub:N.any},render:function(){var u;return u=T.createElement(T.Fragment,{},e({className:"label",key:"label"},this.props.title,this.props.sub?d({className:"sub"}," \u2014 ",this.props.sub):void 0),this.props.children),this.props.multi||(u=a({children:u})),e({className:j("input_row",this.props.className),id:this.props.id},u)}}),n("TextInputRow",{pure:!0,propTypes:{title:N.string,sub:N.any,name:N.string.isRequired,value:N.any},focus:function(){return this.input_ref.current.focus()},get_input_el:function(){return this.input_ref.current},get_value:function(){return this.input_ref.current.value},componentDidMount:function(){var u;if(this.props.money_input){if(!this.props.currency)throw new Error("missing currency");I.money_input(this.input_ref.current,{currency:this.props.currency})}if(this.props.slug_input&&I.slug_input($(this.input_ref.current)),this.props.integer_input)return u=/[0-9]/g,$(this.input_ref.current).on("keypress",function(l){return function(m){var f;if(m.keyCode>=32&&(f=String.fromCharCode(m.keyCode),!f.match(u)))return!1}}(this))},render:function(){var u,l;return u=function(){switch(this.props.type){case"textarea":return function(m){return function(f){return S(Object.assign({rows:m.props.rows,cols:m.props.cols},f))}}(this);default:return function(m){return function(f){return i(Object.assign({type:m.props.type||"text"},f))}}(this)}}.call(this),l=[e({className:"label",key:"label"},this.props.title,this.props.sub?d({className:"sub"}," \u2014 ",this.props.sub):void 0),u({key:"input",name:this.props.name,ref:this.input_ref||(this.input_ref=T.createRef()),value:this.props.value,defaultValue:this.props.defaultValue,onChange:this.props.onChange,onKeyUp:this.props.onKeyUp,onFocus:this.props.onFocus,onBlur:this.props.onBlur,onClick:this.props.onClick,readOnly:this.props.readonly,className:j({has_error:this.props.has_error}),required:this.props.required,placeholder:this.props.placeholder,disabled:this.props.disabled,pattern:this.props.pattern,dir:this.props.dir}),this.props.error_message?g({key:"error",className:"input_error"},this.props.error_message):void 0],this.props.multi||(l=a({children:l})),e({className:j("input_row",this.props.className)},l)}}),n("SimpleSelect",{pure:!0,propTypes:{options:N.array.isRequired,name:N.string},getInitialState:function(){return{value:this.props.defaultValue?this.props.defaultValue:void 0}},render:function(){var u,l,m,f,v;return l=this.current_option(),v=this.props.render_current_option,v||(v=function(b){return function(k){return k.short_name||k.name}}(this)),u=[],m=null,f=function(){if(m)return u.push(s.apply(null,[{label:m.label,key:"group-"+u.length}].concat(At.call(m.children)))),m=null},this.props.options.map(function(b){return function(k,F){var O;return O=h({key:""+F,value:k.value},k.name),k.group?((m!=null?m.label:void 0)!==k.group&&f(),m||(m={label:k.group,children:[]}),m.children.push(O)):(f(),u.push(O))}}(this)),f(),this.enclose({className:j({focused:this.state.focused,disabled:this.props.disabled,has_value:l!==this.props.options[0]})},e({className:"selected_option"},d({className:"selected_option_name"},v(l)),ge({})),C({ref:this.select_ref||(this.select_ref=T.createRef()),disabled:this.props.disabled,value:l.value,name:this.props.name,onFocus:function(b){return function(k){return b.setState({focused:!0})}}(this),onBlur:function(b){return function(k){return b.setState({focused:!1})}}(this),onChange:this.on_change,children:u}))},on_change:function(u){var l;return l=u.target.value,this.props.onChange?l===this.props.value?void 0:this.props.onChange(l):l===this.state.value?void 0:this.setState({value:l})},find_option:function(u){var l,m,f,v;for(v=this.props.options,l=0,m=v.length;l<m;l++)if(f=v[l],f.value===u)return f},current_option:function(){var u,l;return l=this.props.value||this.state.value,u=l!=null?this.find_option(l):void 0,u||this.props.options[0]}}),n("Select",{propTypes:{name:N.string.isRequired,value:N.any,options:N.array.isRequired},componentDidMount:function(){var u,l;if(this.props.selectize&&(l=this.props.selectize===!0?{}:this.props.selectize,u=$(this.input_ref.current),u.selectize(l),this.props.onChange))return u.on("change",function(m){return function(f){return m.props.onChange(f)}}(this))},render:function(){return C({ref:this.input_ref||(this.input_ref=T.createRef()),name:this.props.name,onChange:this.props.onChange,value:this.props.value,defaultValue:this.props.defaultValue,children:this.props.options.map(function(u){return function(l){var m,f;return f=l[0],m=l[1],h({key:f,value:f},m||f)}}(this))})}}),n("RadioButtons",{propTypes:{name:N.string.isRequired,value:N.any,options:N.array.isRequired},getValue:function(){var u,l,m,f,v,b;for(f=this.container().find("input").serializeArray(),u=0,l=f.length;u<l;u++)return v=f[u],m=v.name,b=v.value,b},render:function(){return p({className:"radio_list",children:this.props.options.map(function(u){return function(l,m){var f,v,b,k;return k=l[0],f=l[1],b=l[2],v=l[3],c({key:k!=null?k:m,className:j({disabled:v!=null?v.disabled:void 0})},a({},i(Object.assign({type:"radio",required:u.props.required,name:u.props.name,value:k,defaultChecked:u.props.defaultValue?k===u.props.defaultValue:void 0,checked:u.props.value!=null?k===u.props.value:void 0,onChange:u.props.onChange},v)),d({className:"radio_label"},f),b?d({className:"sub"}," \u2014 ",b):void 0))}}(this))})}}),Xe=n("FormErrors",{pure:!0,getDefaultProps:function(){return{animated:!1,scroll_into_view:!1}},propTypes:{title:N.string,errors:N.array.isRequired},render:function(){var u;return u=this.props.animated?pe:x,u({className:j(this.enclosing_class_name(),"form_errors"),role:"alert"},e({ref:this.props.scroll_into_view?function(l){if(l)return typeof l.scrollIntoView=="function"?l.scrollIntoView():void 0}:void 0},this.props.title!==!1?g({},y({},this.props.title||this.tt("misc.forms.form_errors"))):void 0,p({},this.props.errors.map(function(l){return function(m){var f;return f=m==="recaptcha"?"Please complete the CAPTCHA to continue":m,c({key:m},f)}}(this))),this.props.children))}}),ue=function(u){var l;return l=T.createRef(),T.useEffect(function(){I.with_recaptcha(function(m){return function(){var f;if(f=l.current)return grecaptcha.render(f,{sitekey:u.sitekey})}}(this))},[]),e({ref:l,className:"g-recaptcha"})},ue.propTypes={sitekey:N.string.isRequired},ue=T.createElement.bind(null,T.memo(ue)),n.RecaptchaInput=ue});var Ce=null;U(function(){var n,t,o,e,r,i,a,c,s,h,g,x,C,d,y,S,p,u,l;return e=A,S=e.select,g=e.h2,s=e.form,x=e.input,a=e.div,C=e.label,l=e.ul,d=e.li,p=e.span,i=e.button,h=e.fragment,r=e.a,u=e.strong,c=e.em,y=e.p,h=React.createElement.bind(null,React.Fragment),h.type=React.fragment,t=L.package("Community"),n=t("DismissPendingPostLightbox",{on_submit:function(m){return m.preventDefault(),this.setState({loading:!0}),I.remote_submit($(m.target)).then(function(f){return function(v){var b,k,F;if(v.errors){f.setState({errors:v.errors,loading:!1});return}return typeof(b=f.props).on_dismiss=="function"&&b.on_dismiss(v),(k=f.lightbox_ref)!=null&&(F=k.current)!=null?F.close():void 0}}(this))},getInitialState:function(){return{mark_as_spam:!1}},render:function(){var m;return m=this.props.pending_post,Q({className:classNames(this.enclosing_class_name(),"compact"),ref:this.lightbox_ref||(this.lightbox_ref=React.createRef())},g({},"Dismiss Post"),this.state.errors?Xe({errors:this.state.errors}):void 0,s({action:this.props.update_url,method:"post",className:"form",onSubmit:this.on_submit},L.CSRF({}),x({type:"hidden",name:"pending_post_id",value:m.id}),x({type:"hidden",name:"action",value:"set_status"}),x({type:"hidden",name:"status",value:this.state.mark_as_spam?"spam":"ignored"}),a({className:"input_row"},a({className:"label"},"Options"),l({className:"check_list"},d({},C({},x({type:"checkbox",checked:this.state.mark_as_spam,onChange:function(f){return function(){return f.setState(function(v){return{mark_as_spam:!v.mark_as_spam}})}}(this)}),"Report as Spam",p({className:"sub"}," \u2014 ","Notify itch.io staff about this post. It may be used to train the spam detector"))),d({},C({},x({type:"checkbox",name:"ban_user"}),"Ban account",p({className:"sub"}," \u2014 ","Account will no longer be allowed to make posts in this category"))))),i({className:classNames("button",{disabled:this.state.loading}),disabled:this.state.loading},"Dismiss")))}}),o=t("PendingPost",{pure:!0,getInitialState:function(){return{}},remote_submit:function(m){return this.setState({loading:!0}),I.remote_submit($(m)).then(function(f){return function(v){return f.setState({loading:!1}),v.errors?(Y(ie({errors:v.errors})),$.Deferred().reject(v)):v}}(this))},approve_post:function(m){return m.preventDefault(),this.remote_submit(m.target).then(function(f){return function(v){if(v.post)return f.setState({approved_post:v.post})}}(this))},render:function(){var m,f,v,b;return v=this.props.pending_post,this.enclose({className:classNames({loading:this.state.loading})},a({className:"post_location"},(m=v.category)?h({},r({href:m.url,target:"_blank"},m.title)," \xBB "):void 0,v.is_topic?h({},"New topic"," ",u({},v.topic_title)):(b=v.topic)?h({},r({href:b.url,target:"_blank"},b.title),b.deleted?h({}," ",c({className:"sub"},"(deleted)")):void 0," \xBB ",(f=v.parent_post)?h({},"Replying to ",r({href:f.url,onClick:function(k){return function(F){if(!event.altKey&&!event.ctrlKey&&!event.metaKey&&!event.shiftKey)return F.preventDefault(),Y($.getJSON(f.url).then(function(O){return Ke(O)}))}}(this)},"Post by "+f.user.name),f.deleted?h({}," ",c({className:"sub"},"(deleted)")):void 0):"New reply"):void 0),a({className:"community_post_list_widget"},se({readonly:!0,post:v,display_post_footer:!1})),this.render_post_tools())},render_post_tools:function(){var m,f;return f=this.props.pending_post,(m=this.state.approved_post)?a({className:"post_tools"},p({className:"approval_message"},u({},"Post Approved")," ",r({href:m.url,className:"forward_link",target:"_blank"},"View post"))):a({className:"post_tools"},f.can_promote?s({action:this.props.update_url,method:"post",onSubmit:this.approve_post},L.CSRF({}),x({type:"hidden",name:"pending_post_id",value:f.id}),x({type:"hidden",name:"action",value:"promote_post"}),i({className:classNames("button",{disabled:this.state.loading}),disabled:this.state.loading},"Approve Post")):i({className:"button disabled",disabled:!0,title:"The account or category can not accept new posts"},"Can't approve post"),f.admin_url?r({href:f.admin_url,target:"_blank"},"Admin"):void 0,f.mod_url?r({href:f.mod_url,target:"_blank"},"Mod"):void 0,f.can_promote?void 0:s({action:this.props.update_url,method:"post",onSubmit:function(v){return function(b){if(b.preventDefault(),!!confirm("A deleted pending post can not be recovered, are you sure?"))return v.remote_submit(b.target).then(function(k){return v.props.remove_pending_post(f)})}}(this)},L.CSRF({}),x({type:"hidden",name:"action",value:"set_status"}),x({type:"hidden",name:"status",value:"deleted"}),x({type:"hidden",name:"pending_post_id",value:f.id}),i({className:classNames("button outline",{disabled:this.state.loading}),disabled:this.state.loading},"Delete")),i({className:classNames("button outline",{disabled:this.state.loading}),type:"button",disabled:this.state.loading,onClick:function(v){return function(){return Y(n({update_url:v.props.update_url,pending_post:f,on_dismiss:function(b){if(b.status!==f.status)return v.props.remove_pending_post(f)}}))}}(this)},"Dismiss..."))}}),Ce=t("EditPendingPosts",{getInitialState:function(){return{pending_posts:this.props.pending_posts,next_page:this.props.next_page}},get_next_page:function(){if(this.state.next_page)return this.setState({loading:!0}),$.getJSON("",this.state.next_page).then(function(m){return function(f){if(m.setState({loading:!1}),f.errors){Y(ie({errors:f.errors}));return}return m.setState(function(v){return{next_page:f.next_page,pending_posts:v.pending_posts.concat(f.pending_posts)}})}}(this))},remove_pending_post:function(m){return this.setState(function(f){return{pending_posts:f.pending_posts.filter(function(v){return function(b){return b!==m}}(this))}})},render:function(){var m;return this.enclose({},a({className:"pending_post_list"},(m=this.state.pending_posts)!=null&&m.length?this.state.pending_posts.map(function(f){return function(v){return o({key:v.id,pending_post:v,update_url:f.props.update_url,remove_pending_post:f.remove_pending_post})}}(this)):y({className:"sub empty_message"},"There are no pending posts")),this.state.next_page?ye({key:JSON.stringify(this.state.next_page),on_seen:function(f){return function(){return f.get_next_page()}}(this)},a({className:"loading_container"},i({disabled:!0,className:"button outline disabled"},this.tt("misc.loading_more_ellip")))):void 0)}})}),window.init_CommunityCategoryPendingPosts=function(n,t){L.render(n+" .post_editor_drop",()=>Ce(t))},window.init_RedactorInput=function(n,t){bt(function(){Z($(n),t)})},window.init_CommunityEditCategory=function(n,t){new I.CommunityEditCategory(n)};var ze=[].slice,We=null;U(function(){var n,t,o,e,r,i,a,c,s,h,g,x,C;return t=A,a=t.form,c=t.h2,r=t.div,x=t.p,i=t.em,e=t.button,s=t.input,g=t.label,C=t.span,o=t.a,n=R.package("Community"),h=0,We=n("EditCategories",{propTypes:{category:N.object},getInitialState:function(){return{children:_.toArray(this.props.category.children)}},componentDidMount:function(){return this.dispatch("category",{create:function(d){return function(){return d.state.children.push({key:h+=1}),d.forceUpdate()}}(this),set_property:function(d){return function(y,S,p,u){return S[p]=u,d.forceUpdate()}}(this),create_child:function(d){return function(y,S){return S.children||(S.children=[]),S.children.push({key:h+=1}),d.forceUpdate()}}(this),remove_child:function(d){return function(y,S,p){var u;return u=S===d.props.category?d.state.children:S.children,u.splice(p,1),d.forceUpdate()}}(this),toggle:function(d){return function(y,S,p){return S[p]=!S[p],d.forceUpdate()}}(this),move_up:function(d){return function(y,S,p){var u,l,m,f;if(l=S===d.props.category?d.state.children:S.children,f=p-1,!!l[f])return u=l,m=[u[f],u[p]],u[p]=m[0],u[f]=m[1],d.forceUpdate()}}(this),move_down:function(d){return function(y,S,p){var u,l,m,f;if(l=S===d.props.category?d.state.children:S.children,f=p+1,!!l[f])return u=l,m=[u[f],u[p]],u[p]=m[0],u[f]=m[1],d.forceUpdate()}}(this)})},render:function(){return a({method:"POST",className:"edit_categories form"},R.CSRF({}),c({},"Subcategories"),this.state.children.length?r({className:"category_list",children:_.map(this.state.children,function(d){return function(y,S){return n.CategoryRow({is_first:S===0,is_last:S===d.state.children.length-1,key:y.key||"cat"+y.id,parent_category:d.props.category,category:y,position:S+1})}}(this))}):x({},i({className:"empty_message"},"There are no categories. You must have at least one non-directory category for your community to be usable.")),r({className:"button_row"},e({className:"button",onClick:function(d){return function(y){return y.preventDefault(),d.trigger("category:create")}}(this)},"New category")," ",e({className:"button"},"Save")))}}),n("CategoryRow",{propTypes:{is_first:N.bool,is_last:N.bool,position:N.number,category:N.shape({id:N.number,title:N.string}),input_prefix:N.string},input_name:function(d){var y;return y=this.props.input_prefix||"categories",y+"["+(this.props.position||"")+"]["+d+"]"},render:function(){var d,y;return r({className:"category_row"},this.props.category.id?s({type:"hidden",name:this.input_name("id"),value:this.props.category.id}):void 0,r.apply(null,[{className:"category_primary_inputs"}].concat(ze.call(this.render_title_inputs()))),r.apply(null,[{className:"category_secondary_inputs"}].concat(ze.call(this.render_button_inputs()))),r({className:"category_secondary_inputs"},g({},s({type:"checkbox",name:this.input_name("archived"),checked:this.props.category.archived||!1,onChange:function(S){return function(p){return S.trigger("category:toggle",S.props.category,"archived")}}(this)}),C({className:"label"},"Archive")),g({},s({type:"checkbox",name:this.input_name("hidden"),checked:this.props.category.hidden||!1,onChange:function(S){return function(p){return S.trigger("category:toggle",S.props.category,"hidden")}}(this)}),C({className:"label"},"Hidden")),g({},s({type:"checkbox",name:this.input_name("directory"),checked:this.props.category.directory||!1,onChange:function(S){return function(p){return S.trigger("category:toggle",S.props.category,"directory")}}(this)}),C({className:"label"},"Directory")),this.props.category.created_at?C({className:"created_at",title:this.props.category.created_at},ke(this.props.category.created_at,"calendar")):void 0,!this.props.category.directory&&this.props.category.topics_count!=null?(d=this.props.category.topics_count,C({className:"topics_count"},d+" topic"+(d===1&&""||"s"))):void 0,this.props.category.url?o({className:"category_link",href:this.props.category.url},"View"):void 0,this.props.category.edit_url?o({className:"category_link",href:this.props.category.edit_url},"Edit"):void 0),(y=this.props.category.children)!=null&&y.length?r({className:"child_categories",children:_.map(this.props.category.children,function(S){return function(p,u){return n.CategoryRow({is_first:u===0,is_last:u===S.props.category.children-1,key:p.key||"cat"+p.id,input_prefix:(S.props.input_prefix||"categories")+"["+S.props.position+"][children]",parent_category:S.props.category,category:p,position:u+1})}}(this))}):void 0)},render_title_inputs:function(){return[this.props.category.id?void 0:C({className:"new_flag"},"New"),s({type:"text",className:"title_input text_input",name:this.input_name("title"),required:"required",value:this.props.category.title||"",placeholder:"Title",onChange:function(d){return function(y){return d.trigger("category:set_property",d.props.category,"title",y.target.value)}}(this)}),this.props.category.directory?void 0:s({type:"text",className:"short_description_input text_input",name:this.input_name("short_description"),value:this.props.category.short_description||"",placeholder:"Short description",onChange:function(d){return function(y){return d.trigger("category:set_property",d.props.category,"short_description",y.target.value)}}(this)})]},render_button_inputs:function(){return[e({className:"button small",onClick:function(d){return function(y){return y.preventDefault(),d.trigger("category:create_child",d.props.category)}}(this)},"Add child"),this.props.is_first?void 0:o({href:"#",className:"move_btn move_up_btn",onClick:function(d){return function(y){return y.preventDefault(),d.trigger("category:move_up",d.props.parent_category,d.props.position-1)}}(this)},"Move up"),this.props.is_last?void 0:o({href:"#",className:"move_btn move_down_btn",onClick:function(d){return function(y){return y.preventDefault(),d.trigger("category:move_down",d.props.parent_category,d.props.position-1)}}(this)},"Move down"),o({href:"#",className:"remove_btn",onClick:function(d){return function(y){if(y.preventDefault(),confirm("Are you sure you want to remove?"))return d.trigger("category:remove_child",d.props.parent_category,d.props.position-1)}}(this)},"Remove")]}})}),window.init_CommunityEditCategoryChildren=function(n,t){L.render(n+" .react_drop",()=>We(t))},window.init_CommunityEditCategory=function(n,t){new I.CommunityEditCategory(n)},window.init_FormsMarkdownInput=function(n,t){L.render(n,()=>be(t))};var qe=lt(ct()),Lt=function(){function n(t,o){var e;this.el=$(t);try{this.el.find("input[name=offset]").val(new Date().getTimezoneOffset())}catch(r){}this.set_fingerprint(),e=this.el.find("form").remote_submit(function(r){return function(i){if(i.redirect_to){window.location=i.redirect_to;return}if(i.errors){if(I.add_recaptcha_if_necessary(e,i.errors))return;e.set_form_errors(i.errors)}}}(this)),e.on("submit",function(r){return function(){return e.set_form_errors([])}}(this))}return n.prototype.set_fingerprint=function(){return qe.default?(this.set_fingerprint=function(){},new qe.default().get(function(t){return function(o){if(o)return t.el.find("input[name=bfp]").val(o)}}(this))):!1},n}();window.init_CommunityNewTopic=function(n,t){new Lt(n)},window.init_DashboardPendingPosts=function(n,t){t&&L.render(n+" .post_editor_drop",()=>Ce(t))};var Dt=function(){function n(t,o){var e;o==null&&(o={}),this.el=$(t),e=this.el.find("form").remote_submit(function(r){return function(i){if(i.redirect_to){window.location=i.redirect_to;return}if(i.errors){if(I.add_recaptcha_if_necessary(e,i.errors))return;e.set_form_errors(i.errors);return}if(i.flash)return I.flash(i.flash)}}(this))}return n}();window.init_CommunityPostForm=function(n,t){Dt(n,t)};var Je=null;U(function(){var n,t,o,e,r,i,a;return t=A,r=t.div,a=t.strong,o=t.a,i=t.em,e=t.button,n=R.package("Community"),Je=n("PostPreview",{pure:!0,render:function(){var c,s;return c=this.props.posts[0],console.log(c),this.enclose({},c.parent_post?(s=c.parent_post.user,r({className:"replying_to"},a({},"Replying to ",s?o({target:"_blank",href:s.url},s.name):i({},"Unknown user"))," \xB7 ",o({href:c.parent_post.url,className:"show_parent_link",target:"_blank"},"\u2191 Show parent"))):void 0,Te(Object.assign({current_user:I.current_user},this.props)),c.has_children?r({className:"load_replies"},e({type:"button",className:"textlike",onClick:function(h){return function(g){return g.preventDefault(),h.load_replies()}}(this)},"\u2193 Show replies")):void 0)}})}),window.init_CommunityPostReports=function(n,t){$(n).find(".reported_post[data-props]").each(function(){const o=$(this).data("props");L.render(this,()=>Je(o))})};var Ft=function(n,t){return function(){return n.apply(t,arguments)}},Bt=function(n,t){for(var o in t)Ot.call(t,o)&&(n[o]=t[o]);function e(){this.constructor=n}return e.prototype=t.prototype,n.prototype=new e,n.__super__=t.prototype,n},Ot={}.hasOwnProperty;I.CommunityArchiveTopicLightbox=function(n){Bt(t,n);function t(){return this.init=Ft(this.init,this),t.__super__.constructor.apply(this,arguments)}return t.prototype.init=function(){var o;return this.with_redactor(function(e){return function(){return Z(e.el.find("textarea"),{minHeight:100,source:!1})}}(this)),o=this.el.find("form").remote_submit(function(e){return function(r){if(o.set_form_errors(r.errors),!r.errors)return window.location.reload()}}(this))},t}(I.Lightbox);var Ht=function(n,t){return function(){return n.apply(t,arguments)}},Ut=function(n,t){for(var o in t)jt.call(t,o)&&(n[o]=t[o]);function e(){this.constructor=n}return e.prototype=t.prototype,n.prototype=new e,n.__super__=t.prototype,n},jt={}.hasOwnProperty;I.CommunityBanLightbox=function(n){Ut(t,n);function t(){return this.init=Ht(this.init,this),t.__super__.constructor.apply(this,arguments)}return t.prototype.init=function(){var o;return this.with_redactor(function(e){return function(){return Z(e.el.find("textarea"),{minHeight:100,source:!1})}}(this)),o=this.el.find("form").remote_submit(function(e){return function(r){if(o.set_form_errors(r.errors),!r.errors)return e.el.addClass("after_ban")}}(this))},t}(I.Lightbox),I.CommunityCategoryBans=function(){function n(t){this.el=$(t),this.el.remote_link(function(o){return function(e){if(e.errors){alert(e.errors.join(`
`));return}return window.location.reload()}}(this))}return n}(),I.CommunityProfile=function(){function n(t,o){var e;this.el=$(t),new I.FilterPickers(this.el),this.carousels=function(){var r,i,a,c;for(a=this.el.find(".game_carousel_widget"),c=[],r=0,i=a.length;r<i;r++)e=a[r],c.push(new I.GameCarousel($(e)));return c}.call(this),new I.CommunityViewTopic(this.el.find(".recent_posts"),o)}return n}(),I.CommunityEditCategory=function(){function n(t){var o;this.el=$(t),o=this.el.find("form").remote_submit(function(e){return function(r){if(r.errors){o.set_form_errors(r.errors);return}if(r.redirect_to)return window.location=r.redirect_to}}(this))}return n}(),I.GameCommunityCategory=function(){function n(t){var o;this.el=$(t),o=this.el.find(".blog_post_grid_widget"),o.lazy_images({horizontal:!0,target:o})}return n}();var Gt=function(n,t){for(var o in t)Vt.call(t,o)&&(n[o]=t[o]);function e(){this.constructor=n}return e.prototype=t.prototype,n.prototype=new e,n.__super__=t.prototype,n},Vt={}.hasOwnProperty,$t=D.CollectionLightbox=function(n){Gt(t,n);function t(){return t.__super__.constructor.apply(this,arguments)}return t.prototype.init=function(){var o;return D.has_follow_button(this.el),this.el.find("input[type='radio']:first").prop("checked",!0),this.el.on("click change",".collection_option",function(e){return function(r){var i;return i=$(r.currentTarget),i.find("input[type='radio']").prop("checked",!0)}}(this)),o=this.el.find("form").remote_submit(function(e){return function(r){if(r.errors){if(D.add_recaptcha_if_necessary(o,r.errors))return;o.set_form_errors(r.errors);return}return e.el.addClass("is_complete"),e.el.find(".after_submit .collection_name").text(r.title).attr("href",r.url)}}(this)),this.with_redactor(function(e){return function(){return Z(e.el.find("textarea"),{minHeight:40,source:!1,buttons:["bold","italic","deleted","lists","link"]})}}(this)),this.with_selectize(function(e){return function(){return e.el.find("select.collection_input").selectize()}}(this))},t}(oe);I.GameCommunityHeader=function(){function n(t){this.el=z(t),this.el.dispatch("click",{add_to_collection_btn:function(o){return function(e){return I.current_user?oe.open_remote(e.attr("href"),$t):"continue"}}(this)})}return n}();var Kt=function(n,t){return function(){return n.apply(t,arguments)}},Xt=function(n,t){for(var o in t)zt.call(t,o)&&(n[o]=t[o]);function e(){this.constructor=n}return e.prototype=t.prototype,n.prototype=new e,n.__super__=t.prototype,n},zt={}.hasOwnProperty;I.CommunityLockTopicLightbox=function(n){Xt(t,n);function t(){return this.init=Kt(this.init,this),t.__super__.constructor.apply(this,arguments)}return t.prototype.init=function(){var o;return this.with_redactor(function(e){return function(){return Z(e.el.find("textarea"),{minHeight:100,source:!1})}}(this)),o=this.el.find("form").remote_submit(function(e){return function(r){if(o.set_form_errors(r.errors),!r.errors)return window.location.reload()}}(this))},t}(I.Lightbox);var Wt=function(n,t){return function(){return n.apply(t,arguments)}},qt=function(n,t){for(var o in t)Jt.call(t,o)&&(n[o]=t[o]);function e(){this.constructor=n}return e.prototype=t.prototype,n.prototype=new e,n.__super__=t.prototype,n},Jt={}.hasOwnProperty;I.CommunityReportPostLightbox=function(n){qt(t,n);function t(){return this.init=Wt(this.init,this),t.__super__.constructor.apply(this,arguments)}return t.prototype.init=function(){var o;return o=this.el.find("form").remote_submit(function(e){return function(r){if(r.errors){o.set_form_errors(r.errors);return}return e.el.addClass("submitted_report")}}(this))},t}(I.Lightbox);var Yt=function(n,t){return function(){return n.apply(t,arguments)}},Zt=function(n,t){for(var o in t)Qt.call(t,o)&&(n[o]=t[o]);function e(){this.constructor=n}return e.prototype=t.prototype,n.prototype=new e,n.__super__=t.prototype,n},Qt={}.hasOwnProperty;I.CommunityStickTopicLightbox=function(n){Zt(t,n);function t(){return this.init=Yt(this.init,this),t.__super__.constructor.apply(this,arguments)}return t.prototype.init=function(){var o;return this.with_redactor(function(e){return function(){return Z(e.el.find("textarea"),{minHeight:100,source:!1})}}(this)),o=this.el.find("form").remote_submit(function(e){return function(r){if(o.set_form_errors(r.errors),!r.errors)return window.location.reload()}}(this))},t}(oe);var er=function(n,t){return function(){return n.apply(t,arguments)}};I.CommunityTopicHeader=function(){function n(t,o){var e;this.opts=o,this.render_topic_voter=er(this.render_topic_voter,this),this.el=$(t),e=this.el.find(".moderation_tools"),e.length&&new I.CommunityTopicModerationTools(e,this.opts.moderation),this.render_topic_voter()}return n.prototype.render_topic_voter=function(){var t,o,e;if(typeof R!="undefined"&&R!==null&&(o=R.Community)!=null&&o.TopicVoter&&(e=this.el.find(".community_topic_voter_widget"),!!e.length))return t=e.data("p"),t.vote_url=this.opts.vote_url,ReactDOM.render(R.Community.TopicVoter(t),e[0])},n}(),I.CommunityTopicList=function(){function n(t,o){this.opts=o,this.el=$(t),new I.CommunityTopicModerationTools(t,this.opts),this.el.lazy_images({selector:"[data-background_image]"}),this.render_topic_voters(),this.el.has_tooltips(),new I.GamePopups(this.el)}return n.prototype.render_topic_voters=function(){var t,o,e,r,i,a,c;if(typeof R!="undefined"&&R!==null&&(r=R.Community)!=null&&r.TopicVoter){for(i=this.el.find(".community_topic_voter_widget"),a=[],t=0,o=i.length;t<o;t++)c=i[t],e=$(c).data("p"),e.vote_url=this.opts.vote_url,a.push(ReactDOM.render(R.Community.TopicVoter(e),c));return a}},n}();var tr=function(n,t){return function(){return n.apply(t,arguments)}};I.CommunityTopicModerationTools=function(){n.prototype.topic_url=function(t,o){return this.opts.urls[t].replace(/:topic_id\b/,o)};function n(t,o){this.opts=o,this.topic_url=tr(this.topic_url,this),this.el=$(t),new I.FilterPickers(t),this.el.on("click",".filter_option",function(e){return function(r){var i,a,c;switch(i=$(r.currentTarget).trigger("i:close_filter_pickers"),a=i.closest("[data-topic_id]").data("topic_id"),c=i!=null?i.data("value"):void 0,c){case"purge":if(r.preventDefault(),confirm("This will delete all the users posts and spam their account. No undo. Are you sure?"))return $.post($(r.currentTarget).attr("href"),I.with_csrf()).done(function(s){if(s.errors){alert(s.errors.join(","));return}if(s.redirect_to)return window.location=s.redirect_to});break;case"feature":case"unfeature":case"bump":return r.preventDefault(),$.post($(r.currentTarget).attr("href"),I.with_csrf()).done(function(s){if(s.errors){alert(s.errors.join(","));return}if(s.redirect_to)return window.location=s.redirect_to});case"archive":case"unarchive":return r.preventDefault(),I.Lightbox.open_remote(e.topic_url("archive_topic",a),I.CommunityArchiveTopicLightbox);case"lock":case"unlock":return r.preventDefault(),I.Lightbox.open_remote(e.topic_url("lock_topic",a),I.CommunityLockTopicLightbox);case"stick":case"unstick":return r.preventDefault(),I.Lightbox.open_remote(e.topic_url("stick_topic",a),I.CommunityStickTopicLightbox);case"delete":if(r.preventDefault(),confirm("Are you sure you want to delete this topic?"))return $.ajax({url:e.topic_url("delete_topic",a),data:I.with_csrf(),type:"post",xhrFields:{withCredentials:!0}}).done(function(s){return s.errors?alert(s.errors.join()):window.location.reload()})}}}(this))}return n}();var rr=function(n,t){return function(){return n.apply(t,arguments)}};I.CommunityViewTopic=function(){n.prototype.vote_counts_template=I.lazy_template(n,"vote_counts");function n(t,o){var e;this.opts=o,this.update_votes=rr(this.update_votes,this),this.el=$(t),this.el.find("code[class*=language-]").exists()&&(e=$("#lib_prism_src").data("inject"))&&$(document.body).append(e),this.el.remote_link(function(r){return function(i,a){if(a.is(".vote_btn")){if(i.errors){alert(i.errors.join(", "));return}r.update_votes(a,i);return}if(i.redirect_to)return window.location=i.redirect_to}}(this)),this.el.dispatch("click",{ban_user_btn:function(r){return function(i){var a,c;return c=i.closest(".community_post").data("post"),a=r.opts.ban_url+("?banned_user_id="+c.user_id),I.Lightbox.open_remote(a,I.CommunityBanLightbox)}}(this),stick_topic_btn:function(r){return function(i){return I.Lightbox.open_remote(i.data("href"),I.CommunityStickTopicLightbox)}}(this),lock_topic_btn:function(r){return function(i){return I.Lightbox.open_remote(i.data("href"),I.CommunityLockTopicLightbox)}}(this),report_post_btn:function(r){return function(i){var a,c;return a=i.closest(".community_post").data("post"),c=r.opts.report_url.replace(/:post_id/,a.id),I.Lightbox.open_remote(c,I.CommunityReportPostLightbox)}}(this),embed_preload:function(r){return function(i){var a;if(i[0].hasAttribute("itchio"))return a=i.data("embed_code"),i.replaceWith(a)}}(this)})}return n.prototype.update_votes=function(t,o){var e,r,i,a,c,s;for(a=t.closest(".community_post"),s=a.find(".vote_btn"),i=s.filter(".post_action"),i.removeClass("animate_bounce animate_drop_down"),setTimeout(function(h){return function(){return i.removeClass("animate_bounce animate_drop_down")}}(this),500),t.is(".voted")?(_.defer(function(h){return function(){return i.addClass("animate_drop_down")}}(this)),t.removeClass("voted")):(_.defer(function(h){return function(){return i.addClass("animate_bounce")}}(this)),s.removeClass("voted").filter(t).addClass("voted")),e=0,r=s.length;e<r;e++)c=s[e],c=$(c),c.is(".voted")?c.data("params").action="remove":delete c.data("params").action;return a.find(".vote_counts").html(this.vote_counts_template({up_score:o.up_score+Math.max(0,o.score_adjustment),down_score:o.down_score+Math.abs(Math.min(0,o.score_adjustment))}))},n}(),U(function(){var n,t,o,e,r,i,a,c,s,h,g,x,C;return t=A,i=t.label,r=t.input,a=t.span,e=t.button,n=R.package("Community"),C=PropTypes,c=A.table,g=A.thead,s=A.tbody,x=A.tr,h=A.td,o=A.abbr,n("CategoryEditTags",{propTypes:{tags:C.arrayOf(C.shape({id:C.number.isRequired,label:C.string.isRequired,color:C.string}))},getInitialState:function(){return{tags:_.toArray(this.props.tags)}},push_tag:function(d){if(d=$.trim(d),d!=="")return this.setState(function(y){return{tags:y.tags.concat({label:d})}})},remove_tag:function(d){return this.state.tags.splice(d,1),this.forceUpdate()},input_name:function(d,y){return"category_tags["+(y+1)+"]["+d+"]"},render:function(){return this.enclose({},this.state.tags.length?c({className:"tag_list"},s({},this.state.tags.map(function(d){return function(y,S){return x({className:"tag_row",key:y.id+"-"+y.label},h({},y.id!=null?r({type:"hidden",name:d.input_name("id",S),value:""+y.id}):void 0,y.color!=null?r({type:"hidden",name:d.input_name("color",S),value:y.color}):void 0,r({type:"hidden",name:d.input_name("label",S),value:y.label}),a({className:"tag_label"},y.label)),h({},r({type:"text",name:d.input_name("description",S),defaultValue:y.description,placeholder:"Description, optional"})),h({},e({className:"textlike remove_tag_btn",onClick:function(p){if(confirm("Are you sure you want to remove this tag?"))return p.preventDefault(),d.remove_tag(S)}},"remove")))}}(this)))):void 0,r({type:"text",placeholder:"Type tag name and press enter to add tag",maxLength:30,onKeyDown:function(d){return function(y){if(y.keyCode===13)return y.preventDefault(),d.push_tag(y.target.value),y.target.value=""}}(this)}))}})}),U(function(){var n,t,o,e,r,i;return t=A,o=t.button,e=t.div,r=t.label,n=R.package("Community"),i=PropTypes,n("TopicVoter",{propTypes:{post_id:i.number.isRequired,vote:i.string,vote_url:i.string.isRequired,score:i.number.isRequired,adjustment:i.number,dir:i.string},getInitialState:function(){return{vote:this.props.vote}},vote:function(a){var c,s,h;if(!I.current_user){window.location=this.login_url();return}return this.setState({loading:!0}),h=_.template(this.props.vote_url)({post_id:this.props.post_id}),s=!1,c=a===this.state.vote?(s=!0,{action:"remove"}):{direction:a},$.ajax({url:h,type:"POST",dataType:"json",data:I.with_csrf(c),xhrFields:{withCredentials:!0}}).done(function(g){return function(x){if(g.setState({loading:!1}),x.success)return g.setState({vote:!s&&a,adjustment:x.score_adjustment}),$(ReactDOM.findDOMNode(g)).find("button").trigger("i:refresh_tooltip")}}(this)).fail(function(g){return function(x){return g.setState({loading:!1,has_error:!0})}}(this))},login_url:function(){return"/login?"+$.param({return_to:window.location.toString(),intent:"community"})},render:function(){var a,c,s;return c=this.props.score,this.state.vote&&(c+=(a=this.state.adjustment)!=null?a:this.props.adjustment),s="vote_btn button small",e({},o({type:"button","aria-label":this.state.vote==="up"?"Remove up vote":"Vote up",className:classNames(s,"vote_up_btn icon-caret-up",{outline:this.state.vote!=="up",disabled:this.state.loading}),disabled:this.state.loading,onClick:function(h){return function(){return h.vote("up")}}(this)}),e({className:"vote_score"},c),this.props.dir!=="up"?o({type:"button","aria-label":this.state.vote==="down"?"Remove down vote":"Vote down",className:classNames(s,"vote_down_btn icon-caret-down",{outline:this.state.vote!=="down",disabled:this.state.loading}),disabled:this.state.loading,onClick:function(h){return function(){return h.vote("down")}}(this)}):void 0)}})})})();
