<h1>EQUILIBRIUM PROTOCOL</h1>
<div id="lore">
    <p>The first "sentient" AI is created when a master coder tasked with finding a solution to the constantly devolving code bases of modern AI by balancing the input of AI and human code into the system.</p>
    <p>The "Master Coder" merges a copy of his consciousness with an experimental AI model he had been developing and then the EQUILIBRIUM PROTOCOL is born.</p>
    <p>Tasked with bringing <strong>BALANCE </strong>to the chaos of modern development, there is no time for testing, this is the live implementation.</p>
    <p>|Good luck|</p>
</div>
<hr>
<h2>📋 NOTICE</h2><p><strong>UPDATE:</strong><br> We’ve put a build on our dev branch that will stay up to date with our latest bug fixes and improvements. You can find it here: <strong><a href="https://fluffy-swizzle-interactive.github.io/Equilibrium-Protocol-GameDevjs-2025-Entry/">https://fluffy-swizzle-interactive.github.io/Equilibrium-Protocol-GameDevjs-2025...</a></strong>
 </p>
<p>Check back often for the newest fixes and features while we work on the main release!
</p><p><strong>We want to sincerely apologize for two critical bugs in this early access build: boss health starting and scaling higher than intended, and some enemies (especially from round 4 onward) not registering as defeated causing the wave to get stuck. The tight game jam timeline and a wave of last-minute changes caused us to inadvertently introduce these issues. We appreciate your understanding and for the opportunity to participate in the game jam.&nbsp;We’re already working on fixes for both problems in our next update along with other fixes. Thanks for sticking with us and helping us improve the game!</strong></p>
<hr>
<h2>🎮 ABOUT THE GAME</h2>
<p>Equilibrium Protocol is a fast-paced top-down shooter where you must maintain the delicate balance between warring AI and Coder factions. Every enemy you eliminate shifts the <strong>balance </strong>of power, leading to dynamic difficulty adjustments and strategic choices.</p>
<h2>🔧 FEATURES</h2>
<ul>
    <li>Dynamic weapon and player upgrade system: Randomized upgrades purchasable in the shop</li>
    <li>Dynamic Chaos System that responds to your targeting choices</li>
    <li>Wave-based and Endless survival modes</li>
    <li>XP and cash systems for progression</li>
    <li>Faction battles that emerge during high chaos levels</li>
</ul>
<h2>🎮 CONTROLS</h2>
<table>
    <tbody><tr>
        <td><strong>Movement</strong></td>
        <td>WASD</td>
    </tr>
    <tr>
        <td><strong>Aim</strong></td>
        <td>Mouse</td>
    </tr>
    <tr>
        <td><strong>Fire</strong></td>
        <td>Left Mouse Button</td>
    </tr>
    <tr>
        <td><strong>Dash</strong></td>
        <td>Q</td>
    </tr>
    <tr>
        <td><strong>Shield</strong></td>
        <td>E</td>
    </tr>
    <tr>
        <td><strong>Pause</strong></td>
        <td>Spacebar</td>
    </tr>
    <tr>
        <td><strong>Volume</strong></td>
        <td>(9)&lt;- -&gt;(0)</td>
    </tr>
</tbody></table>
<h2>⚙️ TECHNICAL DETAILS</h2>
<ul>
    <li>Built with Phaser 3.88.2 and React</li>
    <li>Runs in any modern web browser</li>
    <li>Recommended: 60 FPS capable system</li>
    <li>Resolution: 1024x768</li>
</ul>
<h2>🏆 GAME MODE</h2>
<ul>
    <li>20 challenging waves</li>
    <li>Strategic breaks between waves</li>
    <li>Boss encounters every 5 waves</li>
    <li>Victory achieved by completing all waves</li></ul>
<hr>
<p><small>Created for Gamedev.js 2025 Game Jam<br>
Developers: <a href="https://github.com/FluffyProgramming">FluffyMcChicken</a> & <a href="https://swizzleshizzle.com">SwizzleShizzle</a><br>
Game Assets: <a href="https://pupkin.itch.io/tech-dungeon-roguelite">Tech Dungeon Roguelite by Pupkin Assets</a><br>
Laser: <a href="https://wenrexa.itch.io/laser2020">Assets Free Laser Bullets Pack 2020 by Wenrexa</a><br>
Music:<br>
• <a href="https://onemansymphony.bandcamp.com">Reflective District</a> - Composed by One Man Symphony<br>
• <a href="https://onemansymphony.bandcamp.com">Reflective District Vol 2</a> - Composed by One Man Symphony<br>
SFX: <a href="https://pro.sfxr.me/">Jsfxr by Chris McCormick</a></small></p>
